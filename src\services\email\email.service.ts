import nodemailer from 'nodemailer';
import { promises as fs } from 'fs';
import path from 'path';
import { getEmailConfig, getEmailHeaders, shouldDisableTracking } from '../../config/email.config';

export interface EmailData {
  to: string | string[];
  subject: string;
  template: string;
  data: any;
  cc?: string | string[];
  bcc?: string | string[];
  attachments?: any[];
}

export class EmailService {
  private transporter!: nodemailer.Transporter;
  private templatesPath: string;
  private emailConfig: ReturnType<typeof getEmailConfig>;

  constructor() {
    this.templatesPath = path.join(__dirname, '../../templates/email');
    this.emailConfig = getEmailConfig();
    this.initializeTransporter();
  }

  private initializeTransporter(): void {
    this.transporter = nodemailer.createTransport(this.emailConfig.smtp);
  }

  /**
   * Send email using template
   */
  async sendEmail(emailData: EmailData): Promise<void> {
    try {
      const htmlContent = await this.renderTemplate(emailData.template, emailData.data);
      
      // Get provider-specific headers for this template
      const customHeaders = getEmailHeaders(emailData.template, this.emailConfig.provider);

      const mailOptions = {
        from: this.emailConfig.from,
        to: Array.isArray(emailData.to) ? emailData.to.join(', ') : emailData.to,
        cc: emailData.cc ? (Array.isArray(emailData.cc) ? emailData.cc.join(', ') : emailData.cc) : undefined,
        bcc: emailData.bcc ? (Array.isArray(emailData.bcc) ? emailData.bcc.join(', ') : emailData.bcc) : undefined,
        subject: emailData.subject,
        html: htmlContent,
        attachments: emailData.attachments,
        headers: customHeaders
      };

      await this.transporter.sendMail(mailOptions);
      console.log(`📧 Email sent to ${emailData.to}: ${emailData.subject}`);
    } catch (error) {
      console.error('Error sending email:', error);
      throw error;
    }
  }

  /**
   * Render email template
   */
  private async renderTemplate(templateName: string, data: any): Promise<string> {
    try {
      const templatePath = path.join(this.templatesPath, `${templateName}.html`);
      let template = await fs.readFile(templatePath, 'utf-8');

      // Handle conditional blocks {{#variable}}...{{/variable}}
      template = template.replace(/\{\{#(\w+)\}\}([\s\S]*?)\{\{\/\1\}\}/g, (match, key, content) => {
        return data[key] ? content : '';
      });

      // Handle nested objects
      template = template.replace(/\{\{(\w+)\.(\w+)\}\}/g, (match, obj, key) => {
        return data[obj]?.[key] || '';
      });

      // Simple template rendering (replace {{variable}} with data)
      template = template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
        return data[key] || '';
      });

      return template;
    } catch (error) {
      console.error(`Error rendering template ${templateName}:`, error);
      // Return a basic template if file doesn't exist
      return this.getBasicTemplate(data);
    }
  }

  /**
   * Get basic email template
   */
  private getBasicTemplate(data: any): string {
    return `
      <!DOCTYPE html>
    <htmlxx>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Alert</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4;">
    <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f4f4f4; padding: 20px;">
        <tr>
            <td align="center">
                <table width="600" cellpadding="0" cellspacing="0" style="background-color: #ffffff; border-radius: 8px;">
                    <!-- Header -->
                    <tr>
                        <td style="background-color: #171617; padding: 20px; text-align: center;">
                            <img src="https://macgence.s3.ap-south-1.amazonaws.com/whitemode.png" alt="GetAnnotator Logo" style="max-width: 140px; height: auto; display: block; margin: 0 auto;">
                        </td>
                    </tr>
                    <!-- Content -->
                    <tr>
                        <td style="padding: 20px;">
                            <h2 style="font-size: 20px; color: #1a3c34; margin: 0 0 15px; text-align: center;">Subscription Alert</h2>
                            <p style="font-size: 14px; line-height: 20px; color: #333333;">Hello {{params.userName}},</p>
                            <p style="font-size: 14px; line-height: 20px; color: #333333;">{{params.alertMessage}}</p>
                            <table width="100%" cellpadding="0" cellspacing="0" style="text-align: center; margin: 20px 0;">
                                <tr>
                                    <td>
                                        <a href="http://app.getannotator.com" style="display: inline-block; padding: 10px 20px; background-color: #1a3c34; color: #ffffff; text-decoration: none; border-radius: 4px; font-size: 14px;">View Dashboard</a>
                                    </td>
                                </tr>
                            </table>
                            <p style="font-size: 12px; line-height: 18px; color: #666666;">For support, contact <a href="mailto:<EMAIL>" style="color: #1a3c34; text-decoration: none;"><EMAIL></a>.</p>
                        </td>
                    </tr>
                    <!-- Footer -->
                    <tr>
                        <td style="background-color: #171617; color: #f0f0f0; padding: 15px; text-align: center; font-size: 12px; line-height: 18px;">
                            <span><EMAIL></span> | 
                            <a href="http://app.getannotator.com/" style="color: #e1e1e1; text-decoration: none;">app.getannotator.com</a> | 
                            <a href="tel:*************" style="color: #f0f0f0; text-decoration: none;">******-209-8904</a><br>
                            <span>Need help? Reach us via our <a href="http://app.getannotator.com/contact" style="color: #e1e1e1; text-decoration: none;">contact form</a>.</span>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
    `;
  }

  /**
   * Send bulk emails
   */
  async sendBulkEmails(emails: EmailData[]): Promise<void> {
    const promises = emails.map(email => this.sendEmail(email));
    await Promise.allSettled(promises);
    console.log(`📧 Sent ${emails.length} bulk emails`);
  }

  /**
   * Test email configuration
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      console.log('✅ Email service connection verified');
      return true;
    } catch (error) {
      console.error('❌ Email service connection failed:', error);
      return false;
    }
  }
}