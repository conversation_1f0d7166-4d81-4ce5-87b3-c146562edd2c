# Zoho Desk Frontend Integration

This folder contains React components for integrating Zoho Desk ticket creation and management into your frontend application.

## Components

1. **ZohoTicketForm**: A form component for creating new support tickets
2. **ZohoTicketComments**: A component for viewing and adding comments to tickets
3. **ZohoTicketSystem**: A complete ticket management system that combines ticket listing, creation, and viewing

## Installation

1. Copy the component files to your React project
2. Install dependencies:

```bash
npm install axios
```

## Usage

### Basic Setup

```jsx
import React from 'react';
import ZohoTicketSystem from './components/ZohoTicketSystem';

function SupportPage() {
  // Get authentication token from your auth system
  const authToken = "your-auth-token";
  
  // Get current user ID if available
  const userId = "current-user-id";
  
  return (
    <div className="support-page">
      <h1>Customer Support</h1>
      <ZohoTicketSystem 
        authToken={authToken}
        userId={userId}
      />
    </div>
  );
}

export default SupportPage;
```

### Using Individual Components

#### Ticket Form Only

```jsx
import React from 'react';
import ZohoTicketForm from './components/ZohoTicketForm';

function CreateTicketPage() {
  const authToken = "your-auth-token";
  const userId = "current-user-id";
  
  const handleSuccess = (newTicket) => {
    console.log('Ticket created:', newTicket);
    // Navigate to ticket list or show success message
  };
  
  const handleError = (error) => {
    console.error('Error creating ticket:', error);
    // Show error message
  };
  
  return (
    <div className="create-ticket-page">
      <h1>Submit a Support Request</h1>
      <ZohoTicketForm 
        authToken={authToken}
        userId={userId}
        onSuccess={handleSuccess}
        onError={handleError}
      />
    </div>
  );
}
```

#### Comments Only

```jsx
import React from 'react';
import ZohoTicketComments from './components/ZohoTicketComments';

function TicketDetailsPage({ ticketId }) {
  const authToken = "your-auth-token";
  
  return (
    <div className="ticket-details">
      {/* Ticket details display */}
      <h2>Ticket Comments</h2>
      <ZohoTicketComments 
        ticketId={ticketId}
        authToken={authToken}
      />
    </div>
  );
}
```

## API Integration

These components expect your backend to provide the following API endpoints:

### Ticket Endpoints

- `GET /api/zoho-desk/tickets`: List tickets (supports filtering by contactId)
- `POST /api/zoho-desk/tickets`: Create a new ticket
- `GET /api/zoho-desk/tickets/:ticketId`: Get a specific ticket

### Comment Endpoints

- `GET /api/zoho-desk/tickets/:ticketId/comments`: Get comments for a ticket
- `POST /api/zoho-desk/tickets/:ticketId/comments`: Add a comment to a ticket

### Department Endpoints

- `GET /api/zoho-desk/departments`: Get list of departments

## Authentication

These components expect an authentication token to be passed as a prop. The token should be included in the Authorization header for all API requests:

```
Authorization: Bearer YOUR_AUTH_TOKEN
```

## Customization

### Styling

Each component comes with its own CSS file that you can modify to match your application's design. The components use BEM-style class names for easy targeting.

### Props

#### ZohoTicketForm Props

| Prop | Type | Description |
|------|------|-------------|
| authToken | string | Authentication token |
| userId | string | (Optional) Current user ID |
| onSuccess | function | Callback when ticket is created successfully |
| onError | function | Callback when an error occurs |

#### ZohoTicketComments Props

| Prop | Type | Description |
|------|------|-------------|
| ticketId | string | ID of the ticket to show comments for |
| authToken | string | Authentication token |
| onError | function | (Optional) Callback when an error occurs |

#### ZohoTicketSystem Props

| Prop | Type | Description |
|------|------|-------------|
| authToken | string | Authentication token |
| userId | string | (Optional) Current user ID |

## Error Handling

All components include built-in error handling and will display appropriate error messages to users. You can also provide custom error handling through the onError prop.

## Responsive Design

The components are designed to be responsive and work well on both desktop and mobile devices.