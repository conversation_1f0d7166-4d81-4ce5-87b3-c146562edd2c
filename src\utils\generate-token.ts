import crypto from "crypto";

/**
 * Generates a secure random token.
 * @param {number} bytes - Number of bytes (default: 32 for a 64-character token)
 * @returns {string} - Hexadecimal random token
 */
export function generateSecureToken(bytes: number = 32): string {
  return crypto.randomBytes(bytes).toString("hex"); // 32 bytes = 64 hex chars
}

export async function createPasswordResetToken() {
  const token = generateSecureToken(32); // 64-character token
  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours expiry

  return { token, expiresAt }; // Send this token to the user via email
}

export async function createEmailPasswordVerificationToken() {
  const resetToken = crypto.randomBytes(32).toString("hex");

  const passwordResetToken = crypto
    .createHash("sha256")
    .update(resetToken)
    .digest("hex");

  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours expiry

  // Token generation complete

  return { resetToken, passwordResetToken, expiresAt }; // resetToken for email, passwordResetToken for database
}

// Utility function to verify token hashing
export function verifyTokenHash(plainToken: string, hashedToken: string): boolean {
  const computedHash = crypto
    .createHash("sha256")
    .update(plainToken)
    .digest("hex");

  return computedHash === hashedToken;
}
