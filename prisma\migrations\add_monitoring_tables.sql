-- Add subscription alerts table
CREATE TABLE "SubscriptionAlert" (
    "id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "severity" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "subscriptionId" TEXT,
    "message" TEXT NOT NULL,
    "data" JSONB,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SubscriptionAlert_pkey" PRIMARY KEY ("id")
);

-- Add indexes for better performance
CREATE INDEX "SubscriptionAlert_userId_idx" ON "SubscriptionAlert"("userId");
CREATE INDEX "SubscriptionAlert_type_idx" ON "SubscriptionAlert"("type");
CREATE INDEX "SubscriptionAlert_severity_idx" ON "SubscriptionAlert"("severity");
CREATE INDEX "SubscriptionAlert_createdAt_idx" ON "SubscriptionAlert"("createdAt");
CREATE INDEX "SubscriptionAlert_isRead_idx" ON "SubscriptionAlert"("isRead");

-- Add foreign key constraints
ALTER TABLE "SubscriptionAlert" ADD CONSTRAINT "SubscriptionAlert_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "SubscriptionAlert" ADD CONSTRAINT "SubscriptionAlert_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES "Subscription"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Update existing Notification table if it doesn't have all required fields
ALTER TABLE "Notification" ADD COLUMN IF NOT EXISTS "priority" TEXT DEFAULT 'MEDIUM';
ALTER TABLE "Notification" ADD COLUMN IF NOT EXISTS "actionUrl" TEXT;
ALTER TABLE "Notification" ADD COLUMN IF NOT EXISTS "expiresAt" TIMESTAMP(3);
ALTER TABLE "Notification" ADD COLUMN IF NOT EXISTS "data" JSONB DEFAULT '{}';

-- Add indexes for Notification table
CREATE INDEX IF NOT EXISTS "Notification_userId_idx" ON "Notification"("userId");
CREATE INDEX IF NOT EXISTS "Notification_type_idx" ON "Notification"("type");
CREATE INDEX IF NOT EXISTS "Notification_isRead_idx" ON "Notification"("isRead");
CREATE INDEX IF NOT EXISTS "Notification_createdAt_idx" ON "Notification"("createdAt");
CREATE INDEX IF NOT EXISTS "Notification_expiresAt_idx" ON "Notification"("expiresAt");