import { Router } from "express";
import { UserController } from "../controllers/user.controller";
import { asyncHand<PERSON> } from "../middlewares/asyncHandler";
import authMiddleware from "../middlewares/checkAuth";

const router = Router();
const userController = new UserController();

router.get("/get-all-users", async<PERSON>andler(userController.getAllUsers.bind(userController)));
router.post("/", asyncHandler(userController.createUser.bind(userController)));
router.get("/get-by-id/:id", asyncHandler(userController.getUserById.bind(userController)));
router.put("/:id", asyncHandler(userController.updateUser.bind(userController)));
router.delete("/:id", asyncHandler(userController.deleteUser.bind(userController)));
router.get(
  "/profile", 
  authMiddleware, 
  asyncHandler(userController.getUserProfile.bind(userController))
);

export default router;
