export const bankTransferTemplate = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Bank Transfer Payment – Getannotator</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f4f4f4;
      color: #333333;
      margin: 0;
      padding: 0;
    }
    .email-wrapper {
      max-width: 600px;
      margin: 0 auto;
      background-color: #ffffff;
      padding: 20px;
      border: 1px solid #dddddd;
    }
    .header {
      background-color: #00003bff;
      color: #ffffff;
      padding: 15px 20px;
      text-align: center;
    }
    .header h2 {
      margin: 0;
    }
    .section-title {
      margin-top: 20px;
      font-weight: bold;
      color: #171617;
    }
    .content p {
      margin: 8px 0;
    }
    .bank-link {
      display: inline-block;
      margin-top: 10px;
      padding: 12px 20px;
      background-color: #00162eff;
      color: #ffffff;
      text-decoration: none;
      border-radius: 5px;
      font-weight: bold;
    }
    .footer {
      margin-top: 30px;
      font-size: 12px;
      color: #777777;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="email-wrapper">
    <div class="header">
      <h2>Getannotator – Bank Transfer Instructions</h2>
      <p>Ref: {{orderId}}</p>
    </div>

    <div class="content">
      <p>Dear {{customerName}},</p>

      <p>Thank you for submitting your project requirements. Below is a summary of the details you provided, along with instructions to complete your payment via bank transfer.</p>

      <div class="section-title">Submitted Information:</div>
      <p><strong>Package Category:</strong> {{packageCategory}}</p>
      <p><strong>Time Zone:</strong> {{timeZone}}</p>
      <p><strong>Industry:</strong> {{industry}}</p>
      <p><strong>Annotation Category:</strong> {{annotationCategory}}</p>
      <p><strong>Preferred Work Duration:</strong> {{fromTime}} to {{toTime}}</p>
      <p><strong>Additional Notes:</strong> {{notes}}</p>

      <div class="section-title">Bank Transfer Instructions:</div>
      <p>To view the appropriate bank transfer details for your country, please visit the link below:</p>
      <a href="https://dashboard.skydo.com/accounts/Getannotator" class="bank-link" target="_blank">
        Access Bank Transfer Instructions
      </a>
      <p>This dashboard provides region-specific bank account details. Please select your country to retrieve accurate payment instructions.</p>
      <p>Please ensure the payment is made directly from your bank account to the details provided on the dashboard.</p>

      <p>After completing the payment, kindly reply to this email with your bank transfer receipt or confirmation screenshot so we can validate and proceed with your request. It will take 48–72 hours to confirm receipt of payment.</p>

      <p>If you have any questions or require assistance, feel free to contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

      <p>We appreciate your business and look forward to delivering exceptional service.</p>
    </div>

   <div class="footer">
                    <span><EMAIL></span> | 
                    <a href="http://app.getannotator.com/">http://app.getannotator.com/</a> | 
                    <a href="tel:+***********">******-209-8904</a><br>
                    <span>Need help? Reach us via our <a href="http://app.getannotator.com/contact">contact form</a>.</span>
                </div>
  </div>
</body>
</html>
`;

export const bankTransferPaymentSuccess = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Bank Transfer Payment Confirmation – Getannotator</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f7fa;
      margin: 0;
      padding: 0;
      color: #333;
    }
    .email-container {
      max-width: 600px;
      margin: 20px auto;
      background: #ffffff;
      border: 1px solid #ddd;
      border-radius: 8px;
      overflow: hidden;
    }
    .header {
      background-color: #00003bff;
      color: #ffffff;
      padding: 20px;
      text-align: center;
    }
    .header h2 {
      margin: 0;
    }
    .content {
      padding: 20px;
    }
    .content p {
      line-height: 1.6;
      margin: 12px 0;
    }
    .section-title {
      font-weight: bold;
      color: #171617;
      margin-top: 20px;
    }
    .cta-button {
      display: inline-block;
      margin: 20px 0;
      background-color: #00003bff;
      color: #ffffff;
      text-decoration: none;
      padding: 12px 20px;
      border-radius: 4px;
      font-weight: bold;
    }
   .footer {
            margin-top: 30px;
            padding: 20px;
            text-align: center;
            font-size: 13px;
            line-height: 20px;
            background-color: #171617;
            color: #ffffff;
        }
        .footer a {
            color: #e1e1e1 !important;
            text-decoration: none;
        }
        .footer span {
            color: #f0f0f0;
        }
    a {
      color: #0056b3;
      text-decoration: none;
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="header">
      <h2>Bank Transfer Payment Confirmed</h2>
      <p>Your Project is Ready – Getannotator</p>
    </div>

    <div class="content">
      <p>Dear {{customerName}},</p>

      <p>Thank you for your payment. We are pleased to confirm that we have successfully received and processed your bank transfer for your recent order with Getannotator.</p>

      <p class="section-title">Payment Details:</p>
      <p><strong>Reference:</strong> {{orderReference}}</p>
      <p><strong>Amount Received:</strong> {{amountReceived}}</p>
      <p><strong>Date Received:</strong> {{dateReceived}}</p>

      <p>Your project <strong>{{orderReference}}</strong> is now fully activated and ready for you in your Getannotator dashboard.</p>

      <p>You can begin your work by accessing your dashboard:</p>

      <a href="{{dashboardLink}}" class="cta-button" target="_blank">Access Dashboard</a>

      <p>If you have any questions or need assistance getting started, feel free to reply to this email or contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

      <p>Thank you once again for choosing Getannotator. We look forward to supporting your project!</p>
    </div>

   <div class="footer">
                    <span><EMAIL></span> | 
                   <a href="http://app.getannotator.com/" style="color: #e1e1e1ff; text-decoration: none;">app.getannotator.com</a> | 
                    <a href="tel:+***********">******-209-8904</a><br>
                    <span>Need help? Reach us via our <a href="http://app.getannotator.com/contact">contact form</a>.</span>
                </div>
  </div>
</body>
</html>
`;


export const bankTransferAcknowledgementTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Payment Under Review</title>
</head>
<body style="font-family: 'Helvetica', Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; color: #333333;">
  <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f4f4f4; padding: 20px;">
    <tr>
      <td align="center">
        <table width="600" cellpadding="0" cellspacing="0" style="background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <!-- Header -->
          <tr>
            <td style="background-color: #171617ff; padding: 20px; text-align: center;">
              <img src="https://macgence.s3.ap-south-1.amazonaws.com/whitemode.png" alt="GetAnnotator Logo" style="max-width: 140px; height: auto; display: block; margin: 0 auto;">
            </td>
          </tr>
          <!-- Content -->
          <tr>
            <td style="padding: 30px;">
              <p style="font-size: 16px; line-height: 24px; margin: 0 0 20px;">Hi {{name}},</p>
              <p style="font-size: 16px; line-height: 24px; margin: 0 0 20px;">Thank you for choosing and believing in <strong style="color: #1a3c34;">GetAnnotator!</strong></p>
              <p style="font-size: 16px; line-height: 24px; margin: 0 0 20px;">We’ve received your <strong>bank transfer payment</strong>, and it’s currently under review.</p>
              <p style="font-size: 16px; line-height: 24px; margin: 0 0 20px;">As soon as it’s verified, your subscription will be fully activated, and you’ll be notified immediately via email.</p>
              
              <!-- Info Box -->
              <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f8fafc; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <tr>
                  <td style="font-size: 14px; line-height: 22px; color: #374151;">
                    If you have any questions, feel free to reach out to us at 
                    <a href="mailto:<EMAIL>" style="color: #1a3c34; text-decoration: none;"><EMAIL></a> 
                    or simply reply to this email.
                  </td>
                </tr>
              </table>

              <!-- Divider -->
              <tr>
                <td style="border-top: 1px solid #e5e7eb; margin: 25px 0;"></td>
              </tr>
              
              <!-- Signature -->
              <p style="font-size: 14px; line-height: 22px; color: #4b5563; margin: 0;">
                Thanks again for trusting us to power your AI data needs!<br>
                <span style="color: #1a3c34; font-weight: 600;">The GetAnnotator Team</span>
              </p>
            </td>
          </tr>
          <!-- Footer -->
          <tr>
            <td style="background-color: #171617ff; color: #ffffff; padding: 20px; text-align: center; font-size: 13px; line-height: 20px;">
              <span style="color: #f0f0f0ff;"><EMAIL></span> | 
              <a href="http://app.getannotator.com/" style="color: #e1e1e1ff; text-decoration: none;">app.getannotator.com</a> | 
              <a href="tel:+***********" style="color: #f0f0f0ff; text-decoration: none;">******-209-8904</a><br>
              <span style="color: #f0f0f0ff;">Need help? Reach us via our <a href="http://app.getannotator.com/contact" style="color: #e1e1e1ff; text-decoration: none;">contact form</a>.</span>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>
`;
