import { FileType } from "@prisma/client";
import prisma from "../../prisma";

export class MessageService {
  async getMessageById(userId: string) {
    // Get recent direct messages
    const directMessages = await prisma.message.findMany({
      where: {
        OR: [{ senderId: userId }, { receiverId: userId }],
        groupId: null,
      },
      orderBy: {
        createdAt: "desc",
      },
      distinct: ["senderId", "receiverId"], // avoid duplicate threads
      take: 50,
    });

    const directChats = await Promise.all(
      directMessages.map(async (msg) => {
        const otherUserId =
          msg.senderId === userId ? msg.receiverId : msg.senderId;
        if (!otherUserId) return null;

        const otherUser = await prisma.user.findUnique({
          where: { id: otherUserId },
          select: {
            id: true,
            name: true,
            // profilePic: true,
          },
        });

        return {
          type: "user",
          id: otherUser?.id,
          name: otherUser?.name,
          // profilePic: otherUser?.profilePic,
          lastMessage: msg.text || msg.fileType,
          lastMessageTime: msg.createdAt,
        };
      })
    );

    // Get group chats where user is a member
    const groups = await prisma.groupChat.findMany({
      where: {
        members: {
          some: { userId },
        },
      },
      include: {
        messages: {
          orderBy: { createdAt: "desc" },
          take: 1,
        },
      },
    });

    const groupChats = groups.map((group) => {
      const lastMsg = group.messages[0];
      return {
        type: "group",
        id: group.id,
        name: group.name,
        // groupPic: group.groupPic,
        lastMessage: lastMsg?.text || lastMsg?.fileType || null,
        lastMessageTime: lastMsg?.createdAt || null,
      };
    });

    const allChats = [...directChats.filter(Boolean), ...groupChats];

    // Sort by latest message
    return allChats.sort((a, b) => {
      const timeA = a?.lastMessageTime
        ? new Date(a.lastMessageTime).getTime()
        : 0;
      const timeB = b?.lastMessageTime
        ? new Date(b.lastMessageTime).getTime()
        : 0;
      return timeB - timeA;
    });
  }
  async getSidebar(userId: string) {
    // 1. Fetch DMs
    const directMessagesRaw = await prisma.conversationParticipant.findMany({
      where: { userId },
      include: {
        conversation: {
          include: {
            participants: {
              include: {
                user: { select: { id: true, name: true } },
              },
            },
            messages: {
              orderBy: { createdAt: "desc" },
              take: 1,
              include: {
                sender: { select: { id: true, name: true } },
                reactions: true,
              },
            },
          },
        },
      },
    });
  
    const directMessages = directMessagesRaw.map((dm) => ({
      type: "dm",
      id: dm.conversation.id,
      participants: dm.conversation.participants.map((p) => p.user),
      latestMessage: dm.conversation.messages[0] || null,
      createdAt: dm.conversation.messages[0]?.createdAt ?? dm.conversation.createdAt,
    }));
  
    // 2. Fetch Group Chats
    const groupChatsRaw = await prisma.groupMember.findMany({
      where: { userId },
      include: {
        group: {
          include: {
            members: {
              include: {
                user: { select: { id: true, name: true } },
              },
            },
            messages: {
              orderBy: { createdAt: "desc" },
              take: 1,
              include: {
                sender: { select: { id: true, name: true } },
                reactions: true,
              },
            },
          },
        },
      },
    });
  
    const groupChats = groupChatsRaw.map((gm) => ({
      type: "group",
      id: gm.group.id,
      name: gm.group.name,
      members: gm.group.members.map((m) => m.user),
      latestMessage: gm.group.messages[0] || null,
      createdAt: gm.group.messages[0]?.createdAt ?? gm.group.createdAt,
    }));
  
    // 3. Combine and sort by latest message or creation
    const combined = [...directMessages, ...groupChats].sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  
    return combined;
  }
  
  getUsers = async (userId: string) => {
    const users = await prisma.user.findMany({
      where: {
        id: userId,
      },
    });
    return users;
  };
  // async sendMessage(data: {
  //   text?: string;
  //   fileUrl?: string;
  //   fileType?: string;
  //   senderId: string;
  //   receiverId?: string;
  //   groupId?: string;
  //   replyToId?: string;
  //   forwardedFromId?: string;
  // }) {
  //   const message = await prisma.message.create({
  //     data,
  //     include: {
  //       sender: true,
  //       group: true,
  //       replyTo: true,
  //       forwardedFrom: true,
  //       reactions: true,
  //     },
  //   });

  //   return message;
  // }

  async editMessage(messageId: string, data: { text?: string }) {
    const message = await prisma.message.update({
      where: { id: messageId },
      data,
    });

    return message;
  }
  async deleteMessage(messageId: string) {
    await prisma.message.delete({
      where: { id: messageId },
    });
  }
  // async replyToMessage(
  //   messageId: string,
  //   data: {
  //     text?: string;
  //     fileUrl?: string;
  //     fileType?: string;
  //     senderId: string;
  //     groupId?: string;
  //   }
  // ) {
  //   const reply = await prisma.message.create({
  //     data: {
  //       ...data,
  //       replyToId: messageId,
  //     },
  //   });

  //   return reply;
  // }
  async getConversation(userId: string) {
    const messages = await prisma.message.findMany({
      where: {
        OR: [{ senderId: userId }, { receiverId: userId }, { groupId: userId }],
      },
      include: {
        sender: true,
        receiver: true,
        group: true,
        replyTo: true,
        reactions: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return messages;
  }

  async getMessagesBetweenUsers(userAId: string, userBId: string) {
    const messages = await prisma.message.findMany({
      where: {
        OR: [
          { senderId: userAId, receiverId: userBId },
          { senderId: userBId, receiverId: userAId },
        ],
      },
      orderBy: {
        createdAt: "asc",
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
          },
        },
        receiver: {
          select: {
            id: true,
            name: true,
          },
        },
        reactions: true,
      },
    });
  
    return messages;
  }

 async getGroupMessages(userId: string, groupId: string) {
  // Check if user is a member of the group
  const isMember = await prisma.groupMember.findFirst({
    where: {
      userId,
      groupId,
    },
  });

  if (!isMember) {
    throw new Error('You are not a member of this group');
  }

  // Fetch group members
  const groupMembers = await prisma.groupMember.findMany({
    where: {
      groupId,
    },
    select: {
      userId: true,
      user: {
        select: {
          id: true,
          name: true,
          email: true, 
        },
      },
    },
  });

  // Fetch all messages of the group
  const messages = await prisma.message.findMany({
    where: {
      groupId,
    },
    orderBy: {
      createdAt: 'asc',
    },
    include: {
      sender: {
        select: {
          id: true,
          name: true,
        },
      },
      reactions: true,
      readBy: true,
    },
  });

  // Optionally: attach members to each message (invisibly for frontend)
  const enrichedMessages = messages.map(msg => ({
    ...msg,
    groupMembers, // won't break frontend unless it's used directly
  }));

  return enrichedMessages;
}


 async getConversationMedia(userId: string, conversationId: string, fileType?: string) {
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
      include: {
        participants: { select: { userId: true } },
      },
    });

    const groupChat = await prisma.groupChat.findUnique({
      where: { id: conversationId },
      include: {
        members: { select: { userId: true } },
      },
    });

    // if (!conversation && !groupChat) {
    //   throw new Error("Conversation or group not found");
    // }

    const isConversationParticipant = conversation?.participants.some(
      (p) => p.userId === userId
    );
    const isGroupMember = groupChat?.members.some((m) => m.userId === userId);

    // if (!isConversationParticipant && !isGroupMember) {
    //   throw new Error("User is not a participant in this conversation or group");
    // }

const whereClause: any = {
  fileUrl: { not: null },
  AND: [],
  OR: [],
};

if (conversation) {
  whereClause.OR.push({ conversationId });
}
if (groupChat) {
  whereClause.OR.push({ groupId: conversationId });
}
if (fileType) {
  whereClause.AND.push({ fileType: fileType.toUpperCase() });
}


    if (fileType) {
      whereClause.AND = { fileType: fileType.toUpperCase() };
    }

    const messages = await prisma.message.findMany({
      where: whereClause,
      select: {
        id: true,
        fileUrl: true,
        fileType: true,
        createdAt: true,
        sender: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });

    return messages.map((msg) => ({
      id: msg.id,
      fileUrl: msg.fileUrl,
      fileType: msg.fileType,
      createdAt: msg.createdAt,
      sender: {
        id: msg.sender.id,
        name: msg.sender.name,
      },
    }));
  }


  async getGroupMembers(groupId: string) {
  const group = await prisma.groupChat.findUnique({
    where: { id: groupId },
    include: {
      members: {
        select: {
          userId: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              
            },
          },
        },
      },
    },
  });

  if (!group) {
    throw new Error("Group not found");
  }

  return group.members.map((member) => member.user);
}

  
}



