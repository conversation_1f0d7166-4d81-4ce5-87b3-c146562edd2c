/**
 * Feature constants for subscription-based access control
 * These features should be configured in packages to control access
 */

export const FEATURES = {
  // Project Management
  CREATE_PROJECT: "CREATE_PROJECT",
  UPDATE_PROJECT: "UPDATE_PROJECT", 
  DELETE_PROJECT: "DELETE_PROJECT",
  
  // Task Management
  CREATE_TASK: "CREATE_TASK",
  UPDATE_TASK: "UPDATE_TASK",
  DELETE_TASK: "DELETE_TASK",
  ASSIGN_TASK: "ASSIGN_TASK",
  
  // Self Task Management
  CREATE_SELF_TASK: "CREATE_SELF_TASK",
  UPDATE_SELF_TASK: "UPDATE_SELF_TASK",
  DELETE_SELF_TASK: "DELETE_SELF_TASK",
  
  // Team Management
  INVITE_COWORKER: "INVITE_COWORKER",
  MANAGE_COWORKER: "MANAGE_COWORKER",
  UPDATE_COWORKER_PERMISSION: "UPDATE_COWORKER_PERMISSION",
  
  // Communication
  SEND_MESSAGE: "SEND_MESSAGE",
  GROUP_CHAT: "GROUP_CHAT",
  UPLOAD_FILE: "UPLOAD_FILE",
  
  // Advanced Features
  BULK_OPERATIONS: "BULK_OPERATIONS",
  ADVANCED_REPORTING: "ADVANCED_REPORTING",
  API_ACCESS: "API_ACCESS",
  CUSTOM_INTEGRATIONS: "CUSTOM_INTEGRATIONS",
  
  // Storage & Limits
  UNLIMITED_STORAGE: "UNLIMITED_STORAGE",
  UNLIMITED_PROJECTS: "UNLIMITED_PROJECTS",
  UNLIMITED_TASKS: "UNLIMITED_TASKS",
  UNLIMITED_COWORKERS: "UNLIMITED_COWORKERS",
} as const;

export type FeatureType = typeof FEATURES[keyof typeof FEATURES];

/**
 * Feature descriptions for admin interface
 */
export const FEATURE_DESCRIPTIONS = {
  [FEATURES.CREATE_PROJECT]: "Create new projects",
  [FEATURES.UPDATE_PROJECT]: "Update existing projects",
  [FEATURES.DELETE_PROJECT]: "Delete projects",
  [FEATURES.CREATE_TASK]: "Create new tasks",
  [FEATURES.UPDATE_TASK]: "Update existing tasks", 
  [FEATURES.DELETE_TASK]: "Delete tasks",
  [FEATURES.ASSIGN_TASK]: "Assign tasks to annotators",
  [FEATURES.CREATE_SELF_TASK]: "Create personal tasks",
  [FEATURES.UPDATE_SELF_TASK]: "Update personal tasks",
  [FEATURES.DELETE_SELF_TASK]: "Delete personal tasks",
  [FEATURES.INVITE_COWORKER]: "Invite team members",
  [FEATURES.MANAGE_COWORKER]: "Manage team members",
  [FEATURES.UPDATE_COWORKER_PERMISSION]: "Update team member permissions",
  [FEATURES.SEND_MESSAGE]: "Send messages and chat",
  [FEATURES.GROUP_CHAT]: "Create and participate in group chats",
  [FEATURES.UPLOAD_FILE]: "Upload files and attachments",
  [FEATURES.BULK_OPERATIONS]: "Perform bulk operations",
  [FEATURES.ADVANCED_REPORTING]: "Access advanced reports",
  [FEATURES.API_ACCESS]: "Access REST API",
  [FEATURES.CUSTOM_INTEGRATIONS]: "Set up custom integrations",
  [FEATURES.UNLIMITED_STORAGE]: "Unlimited file storage",
  [FEATURES.UNLIMITED_PROJECTS]: "Create unlimited projects",
  [FEATURES.UNLIMITED_TASKS]: "Create unlimited tasks",
  [FEATURES.UNLIMITED_COWORKERS]: "Invite unlimited team members",
} as const;

/**
 * Default feature sets for different package tiers
 */
const BASIC_FEATURES = [
  FEATURES.CREATE_PROJECT,
  FEATURES.UPDATE_PROJECT,
  FEATURES.CREATE_TASK,
  FEATURES.UPDATE_TASK,
  FEATURES.CREATE_SELF_TASK,
  FEATURES.UPDATE_SELF_TASK,
  FEATURES.SEND_MESSAGE,
  FEATURES.UPLOAD_FILE,
] as const;

const STANDARD_FEATURES = [
  ...BASIC_FEATURES,
  FEATURES.DELETE_PROJECT,
  FEATURES.DELETE_TASK,
  FEATURES.DELETE_SELF_TASK,
  FEATURES.INVITE_COWORKER,
  FEATURES.MANAGE_COWORKER,
  FEATURES.ASSIGN_TASK,
  FEATURES.GROUP_CHAT,
] as const;

const PREMIUM_FEATURES = [
  ...STANDARD_FEATURES,
  FEATURES.UPDATE_COWORKER_PERMISSION,
  FEATURES.BULK_OPERATIONS,
  FEATURES.ADVANCED_REPORTING,
  FEATURES.API_ACCESS,
  FEATURES.UNLIMITED_PROJECTS,
  FEATURES.UNLIMITED_TASKS,
] as const;

const ENTERPRISE_FEATURES = [
  ...PREMIUM_FEATURES,
  FEATURES.CUSTOM_INTEGRATIONS,
  FEATURES.UNLIMITED_STORAGE,
  FEATURES.UNLIMITED_COWORKERS,
] as const;

export const PACKAGE_FEATURE_SETS = {
  BASIC: BASIC_FEATURES,
  STANDARD: STANDARD_FEATURES,
  PREMIUM: PREMIUM_FEATURES,
  ENTERPRISE: ENTERPRISE_FEATURES,
} as const;