import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { EmailAnalyticsService } from '../../services/email/email-analytics.service';
import prisma from '../../prisma';

// Mock Prisma
jest.mock('../../prisma', () => ({
  emailLog: {
    create: jest.fn(),
    update: jest.fn(),
    count: jest.fn(),
    findMany: jest.fn(),
    deleteMany: jest.fn(),
  },
}));

const mockedPrisma = prisma as jest.Mocked<typeof prisma>;

describe('EmailAnalyticsService', () => {
  let analyticsService: EmailAnalyticsService;

  beforeEach(() => {
    jest.clearAllMocks();
    analyticsService = new EmailAnalyticsService();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('logEmailSent', () => {
    it('should log email sent successfully', async () => {
      const mockEmailLog = {
        id: 'test-email-id',
        to: '<EMAIL>',
        subject: 'Test Email',
        template: 'otp-verification',
        status: 'SENT',
        sentAt: new Date(),
        metadata: { test: 'data' },
      };

      mockedPrisma.emailLog.create.mockResolvedValue(mockEmailLog as any);

      const emailId = await analyticsService.logEmailSent({
        to: '<EMAIL>',
        subject: 'Test Email',
        template: 'otp-verification',
        metadata: { test: 'data' },
      });

      expect(emailId).toBe('test-email-id');
      expect(mockedPrisma.emailLog.create).toHaveBeenCalledWith({
        data: {
          to: '<EMAIL>',
          subject: 'Test Email',
          template: 'otp-verification',
          status: 'SENT',
          sentAt: expect.any(Date),
          metadata: { test: 'data' },
        },
      });
    });

    it('should handle logging errors', async () => {
      mockedPrisma.emailLog.create.mockRejectedValue(new Error('Database error'));

      await expect(
        analyticsService.logEmailSent({
          to: '<EMAIL>',
          subject: 'Test Email',
          template: 'otp-verification',
        })
      ).rejects.toThrow('Database error');
    });
  });

  describe('updateEmailStatus', () => {
    it('should update email status successfully', async () => {
      mockedPrisma.emailLog.update.mockResolvedValue({} as any);

      await analyticsService.updateEmailStatus('test-email-id', 'DELIVERED', {
        deliveredAt: new Date(),
      });

      expect(mockedPrisma.emailLog.update).toHaveBeenCalledWith({
        where: { id: 'test-email-id' },
        data: {
          status: 'DELIVERED',
          deliveredAt: expect.any(Date),
        },
      });
    });

    it('should handle update errors', async () => {
      mockedPrisma.emailLog.update.mockRejectedValue(new Error('Update failed'));

      await expect(
        analyticsService.updateEmailStatus('test-email-id', 'FAILED')
      ).rejects.toThrow('Update failed');
    });
  });

  describe('tracking methods', () => {
    beforeEach(() => {
      mockedPrisma.emailLog.update.mockResolvedValue({} as any);
    });

    it('should track email open', async () => {
      await analyticsService.trackEmailOpen('test-email-id');

      expect(mockedPrisma.emailLog.update).toHaveBeenCalledWith({
        where: { id: 'test-email-id' },
        data: {
          status: 'OPENED',
          openedAt: expect.any(Date),
        },
      });
    });

    it('should track email click', async () => {
      await analyticsService.trackEmailClick('test-email-id');

      expect(mockedPrisma.emailLog.update).toHaveBeenCalledWith({
        where: { id: 'test-email-id' },
        data: {
          status: 'CLICKED',
          clickedAt: expect.any(Date),
        },
      });
    });

    it('should mark email as delivered', async () => {
      await analyticsService.markEmailDelivered('test-email-id');

      expect(mockedPrisma.emailLog.update).toHaveBeenCalledWith({
        where: { id: 'test-email-id' },
        data: {
          status: 'DELIVERED',
          deliveredAt: expect.any(Date),
        },
      });
    });

    it('should mark email as failed', async () => {
      await analyticsService.markEmailFailed('test-email-id', 'SMTP error');

      expect(mockedPrisma.emailLog.update).toHaveBeenCalledWith({
        where: { id: 'test-email-id' },
        data: {
          status: 'FAILED',
          failureReason: 'SMTP error',
        },
      });
    });
  });

  describe('getEmailAnalytics', () => {
    it('should return email analytics for date range', async () => {
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-31');

      // Mock the count queries
      mockedPrisma.emailLog.count
        .mockResolvedValueOnce(100) // totalSent
        .mockResolvedValueOnce(95)  // totalDelivered
        .mockResolvedValueOnce(80)  // totalOpened
        .mockResolvedValueOnce(20)  // totalClicked
        .mockResolvedValueOnce(5);  // totalFailed

      const analytics = await analyticsService.getEmailAnalytics(startDate, endDate);

      expect(analytics).toEqual({
        totalSent: 100,
        totalDelivered: 95,
        totalOpened: 80,
        totalClicked: 20,
        totalFailed: 5,
        deliveryRate: 95,
        openRate: 84.21,
        clickRate: 25,
        bounceRate: 5,
      });

      expect(mockedPrisma.emailLog.count).toHaveBeenCalledTimes(5);
    });

    it('should filter by template when provided', async () => {
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-31');
      const template = 'otp-verification';

      mockedPrisma.emailLog.count.mockResolvedValue(50);

      await analyticsService.getEmailAnalytics(startDate, endDate, template);

      expect(mockedPrisma.emailLog.count).toHaveBeenCalledWith({
        where: {
          sentAt: { gte: startDate, lte: endDate },
          template: 'otp-verification',
        },
      });
    });

    it('should handle zero values correctly', async () => {
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-31');

      mockedPrisma.emailLog.count.mockResolvedValue(0);

      const analytics = await analyticsService.getEmailAnalytics(startDate, endDate);

      expect(analytics).toEqual({
        totalSent: 0,
        totalDelivered: 0,
        totalOpened: 0,
        totalClicked: 0,
        totalFailed: 0,
        deliveryRate: 0,
        openRate: 0,
        clickRate: 0,
        bounceRate: 0,
      });
    });
  });

  describe('getTemplateAnalytics', () => {
    it('should return analytics for all templates', async () => {
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-31');

      // Mock finding distinct templates
      mockedPrisma.emailLog.findMany.mockResolvedValue([
        { template: 'otp-verification' },
        { template: 'welcome-mail' },
      ] as any);

      // Mock count queries for each template
      mockedPrisma.emailLog.count.mockResolvedValue(50);

      const analytics = await analyticsService.getTemplateAnalytics(startDate, endDate);

      expect(analytics).toHaveLength(2);
      expect(analytics[0]).toHaveProperty('template');
      expect(analytics[0]).toHaveProperty('totalSent');
      expect(analytics[0]).toHaveProperty('deliveryRate');
    });
  });

  describe('getRecentEmailActivity', () => {
    it('should return recent email activity', async () => {
      const mockEmails = [
        {
          id: 'email-1',
          to: '<EMAIL>',
          subject: 'Email 1',
          template: 'otp-verification',
          status: 'SENT',
          sentAt: new Date(),
          deliveredAt: null,
          openedAt: null,
          clickedAt: null,
          failureReason: null,
          metadata: {},
        },
        {
          id: 'email-2',
          to: '<EMAIL>',
          subject: 'Email 2',
          template: 'welcome-mail',
          status: 'DELIVERED',
          sentAt: new Date(),
          deliveredAt: new Date(),
          openedAt: null,
          clickedAt: null,
          failureReason: null,
          metadata: {},
        },
      ];

      mockedPrisma.emailLog.findMany.mockResolvedValue(mockEmails as any);

      const activity = await analyticsService.getRecentEmailActivity(50);

      expect(activity).toHaveLength(2);
      expect(activity[0].id).toBe('email-1');
      expect(activity[1].id).toBe('email-2');

      expect(mockedPrisma.emailLog.findMany).toHaveBeenCalledWith({
        orderBy: { sentAt: 'desc' },
        take: 50,
      });
    });

    it('should use default limit when not provided', async () => {
      mockedPrisma.emailLog.findMany.mockResolvedValue([]);

      await analyticsService.getRecentEmailActivity();

      expect(mockedPrisma.emailLog.findMany).toHaveBeenCalledWith({
        orderBy: { sentAt: 'desc' },
        take: 50,
      });
    });
  });

  describe('cleanupOldLogs', () => {
    it('should clean up old email logs', async () => {
      mockedPrisma.emailLog.deleteMany.mockResolvedValue({ count: 25 });

      const deletedCount = await analyticsService.cleanupOldLogs(90);

      expect(deletedCount).toBe(25);
      expect(mockedPrisma.emailLog.deleteMany).toHaveBeenCalledWith({
        where: {
          sentAt: {
            lt: expect.any(Date),
          },
        },
      });
    });

    it('should use default retention period when not provided', async () => {
      mockedPrisma.emailLog.deleteMany.mockResolvedValue({ count: 10 });

      await analyticsService.cleanupOldLogs();

      expect(mockedPrisma.emailLog.deleteMany).toHaveBeenCalled();
    });
  });

  describe('URL generation methods', () => {
    beforeEach(() => {
      process.env.BACKEND_URL = 'https://api.example.com';
    });

    afterEach(() => {
      delete process.env.BACKEND_URL;
    });

    it('should generate tracking pixel URL', () => {
      const url = analyticsService.generateTrackingPixelUrl('test-email-id');

      expect(url).toBe('https://api.example.com/api/email/track/open/test-email-id');
    });

    it('should generate click tracking URL', () => {
      const originalUrl = 'https://example.com/dashboard';
      const url = analyticsService.generateClickTrackingUrl('test-email-id', originalUrl);

      expect(url).toBe(
        'https://api.example.com/api/email/track/click/test-email-id?url=https%3A%2F%2Fexample.com%2Fdashboard'
      );
    });

    it('should use default backend URL when not set', () => {
      delete process.env.BACKEND_URL;

      const url = analyticsService.generateTrackingPixelUrl('test-email-id');

      expect(url).toBe('http://localhost:3000/api/email/track/open/test-email-id');
    });
  });
});