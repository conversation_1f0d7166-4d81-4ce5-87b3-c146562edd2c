/*
  Warnings:

  - A unique constraint covering the columns `[product_id]` on the table `Package` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[business_id]` on the table `Package` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Package" ADD COLUMN     "business_id" TEXT,
ADD COLUMN     "currency" TEXT NOT NULL DEFAULT 'INR',
ADD COLUMN     "discount" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "product_id" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "Package_product_id_key" ON "Package"("product_id");

-- CreateIndex
CREATE UNIQUE INDEX "Package_business_id_key" ON "Package"("business_id");
