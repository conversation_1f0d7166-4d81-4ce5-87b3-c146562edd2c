/**
 * Example usage of Payment Notification Template
 * 
 * This file shows how to use the payment notification template
 * for different payment scenarios (PayPal, Dodo, success, failure)
 */

import { paymentNotificationService } from '../services/payment/payment-notification.service';

// Example 1: PayPal Payment Success
export async function examplePayPalSuccess() {
  await paymentNotificationService.sendPayPalNotification({
    email: '<EMAIL>',
    customerName: '<PERSON>',
    transactionId: 'ORDER123456',
    amount: '$99.00',
    packageName: 'Professional Plan',
    status: 'success',
    paypalTransactionId: 'PAYPAL_TXN_789'
  });
}

// Example 2: PayPal Payment Failure
export async function examplePayPalFailure() {
  await paymentNotificationService.sendPayPalNotification({
    email: '<EMAIL>',
    customerName: '<PERSON>',
    transactionId: 'ORDER123456',
    amount: '$99.00',
    packageName: 'Professional Plan',
    status: 'failed'
  });
}

// Example 3: Dodo Payment Success
export async function exampleDodoSuccess() {
  await paymentNotificationService.sendDodoNotification({
    email: '<EMAIL>',
    customerName: '<PERSON>',
    transactionId: 'DODO_ORDER_456',
    amount: '$149.00',
    packageName: 'Enterprise Plan',
    status: 'success',
    dodoTransactionId: 'DODO_TXN_123'
  });
}

// Example 4: Dodo Payment Failure
export async function exampleDodoFailure() {
  await paymentNotificationService.sendDodoNotification({
    email: '<EMAIL>',
    customerName: 'Jane Smith',
    transactionId: 'DODO_ORDER_456',
    amount: '$149.00',
    packageName: 'Enterprise Plan',
    status: 'failed'
  });
}

// Example 5: Custom Payment Success
export async function exampleCustomSuccess() {
  await paymentNotificationService.sendPaymentSuccess({
    email: '<EMAIL>',
    customerName: 'Mike Johnson',
    transactionId: 'CUSTOM_TXN_789',
    paymentMethod: 'Credit Card',
    amount: '$199.00',
    packageName: 'Premium Plan',
    dashboardUrl: 'https://app.getannotator.com/dashboard'
  });
}

// Example 6: Custom Payment Failure with specific reason
export async function exampleCustomFailure() {
  await paymentNotificationService.sendPaymentFailure({
    email: '<EMAIL>',
    customerName: 'Mike Johnson',
    transactionId: 'CUSTOM_TXN_789',
    paymentMethod: 'Credit Card',
    amount: '$199.00',
    packageName: 'Premium Plan',
    failureReason: 'Insufficient funds in account'
  });
}

// Example 7: Bank Transfer Success
export async function exampleBankTransferSuccess() {
  await paymentNotificationService.sendPaymentSuccess({
    email: '<EMAIL>',
    customerName: 'Sarah Wilson',
    transactionId: 'BANK_TXN_456',
    paymentMethod: 'Bank Transfer',
    amount: '$299.00',
    packageName: 'Annual Subscription'
  });
}

/**
 * How to integrate in your payment controllers:
 * 
 * // In PayPal webhook handler
 * app.post('/webhook/paypal', async (req, res) => {
 *   const { status, transaction_id, amount, customer_email, customer_name } = req.body;
 *   
 *   await paymentNotificationService.sendPayPalNotification({
 *     email: customer_email,
 *     customerName: customer_name,
 *     transactionId: transaction_id,
 *     amount: amount,
 *     packageName: 'Your Package Name',
 *     status: status === 'completed' ? 'success' : 'failed',
 *     paypalTransactionId: transaction_id
 *   });
 *   
 *   res.status(200).send('OK');
 * });
 * 
 * // In Dodo payment handler
 * app.post('/payment/dodo/callback', async (req, res) => {
 *   const { success, orderId, amount, userEmail, userName } = req.body;
 *   
 *   await paymentNotificationService.sendDodoNotification({
 *     email: userEmail,
 *     customerName: userName,
 *     transactionId: orderId,
 *     amount: amount,
 *     packageName: 'Your Package Name',
 *     status: success ? 'success' : 'failed'
 *   });
 *   
 *   res.json({ message: 'Payment processed' });
 * });
 */
