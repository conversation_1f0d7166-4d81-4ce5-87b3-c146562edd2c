import { Request, Response } from "express";
import { Role } from "@prisma/client";
import { onboardingService } from "../../services/onboarding/onboarding.service";
import { JwtPayload } from "jsonwebtoken";
import { AppError } from "../../utils/ApiError";

interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}

export class OnboardingController {
  async create(req: AuthenticatedRequest, res: Response): Promise<void> {
    console.log(req.body, "Request Body");
    const loggedInUser = req.user;

    if (!loggedInUser || loggedInUser.userRole !== Role.ADMIN) {
      throw new AppError("Only admins can create annotators", 403); // <-- important: stop here!
    }

    const { name,lastname, email, password, role, domain, packageId } = req.body;
    console.log({ name, lastname, email, password, role, domain, packageId });


    const annotator = await onboardingService.createAnnotator({
      name,
      lastname,
      email,
      password,
      domain,
      packageId,
      role: role || Role.ANNOTATOR,
    });

    res.status(201).json({
      message: "Annotator created successfully",
      user: {
        id: annotator.id,
        name: annotator.name,
        lastname:annotator.lastname,
        email: annotator.email,
        role: annotator.role,
        createdAt: annotator.createdAt,
      },
    });
  }

  async getAll(req: AuthenticatedRequest, res: Response): Promise<void> {
    const loggedInUser = req.user;

    if (!loggedInUser || loggedInUser.userRole !== Role.ADMIN) {
      throw new AppError("Only admins can view users", 403);
    }

    const users = await onboardingService.getAnnotator(req.query);

    res.status(200).json({
      message: "Users fetched successfully",
      data: users,
    });
  }
  async getbyId(req: AuthenticatedRequest, res: Response): Promise<void> {
    const loggedInUser = req.user;

    if (!loggedInUser || loggedInUser.userRole !== Role.ADMIN) {
      throw new AppError("Only admins can view annotators", 403); // <-- important: stop here!
    }

    const { id } = req.params;

    if (!id) {
      throw new AppError("Annotator ID is required", 400); // <-- important: stop here!
    }

    const annotator = await onboardingService.getAnnotatorById(id);

    if (!annotator) {
      throw new AppError("Annotator not found", 404); // <-- important: stop here!
    }

    res.status(200).json({
      message: "Annotator fetched successfully",
      data: annotator,
    });
  }
  async updateById(req: AuthenticatedRequest, res: Response): Promise<void> {
    const loggedInUser = req.user;

    if (!loggedInUser || loggedInUser.userRole !== Role.ADMIN) {
      res.status(403).json({ message: "Only admins can update annotators" });
      return; // <-- important: stop here!
    }

    const { id } = req.params;

    if (!id) {
      throw new AppError("Annotator ID is required", 400); // <-- important: stop here!
    }

    const { name,lastname, email, password } = req.body;

    if (!name && !email && !password && !lastname) {
      throw new AppError(
        "At least one of name, email, or password is required",
        400
      );
      //   return;
    }

    const updatedAnnotator = await onboardingService.updateAnnotator(id, {
      name,
      lastname,
      email,
      password,
    });

    if (!updatedAnnotator) {
      throw new AppError("Annotator not found", 404); // <-- important: stop here!
      //   return;
    }

    res.status(200).json({
      message: "Annotator updated successfully",
      user: {
        id: updatedAnnotator.id,
        name: updatedAnnotator.name,
        lastname:updatedAnnotator.lastname,
        email: updatedAnnotator.email,
        role: updatedAnnotator.role,
        createdAt: updatedAnnotator.createdAt,
      },
    });
  }

  async updateStatus(req: AuthenticatedRequest, res: Response): Promise<void> {
    const loggedInUser = req.user;

    if (!loggedInUser || loggedInUser.userRole !== Role.ADMIN) {
      throw new AppError("Only admins can update user status", 403);
    }

    const { id } = req.params;

    if (!id) {
      throw new AppError("User ID is required", 400);
    }

    const { status } = req.body;

    if (!status) {
      throw new AppError("Account status is required", 400);
    }

    const updatedUser =
      await onboardingService.updateAnnotatorAccountStatus(id, status);

    if (!updatedUser) {
      throw new AppError("User not found", 404);
    }

    res.status(200).json({
      message: "User account status updated successfully",
      user: {
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        accountStatus: updatedUser.accountStatus,
      },
    });
  }
  async deleteById(req: AuthenticatedRequest, res: Response): Promise<void> {
    const loggedInUser = req.user;

    if (!loggedInUser || loggedInUser.userRole !== Role.ADMIN) {
      throw new AppError("Only admins can delete annotators", 403); // <-- important: stop here!
    }

    const { id } = req.params;

    if (!id) {
      throw new AppError("Annotator ID is required", 400); // <-- important: stop here!
    }

    const deletionResult = await onboardingService.deleteAnnotator(id);

    if (!deletionResult) {
      throw new AppError("User not found", 404); // <-- important: stop here!
    }

    res.status(200).json({
      message: deletionResult.message,
      deletionType: deletionResult.deletionType,
      relationshipDetails: deletionResult.relationshipDetails,
      user: {
        id: deletionResult.id,
        name: deletionResult.name,
        email: deletionResult.email,
        role: deletionResult.role,
        isDeleted: deletionResult.isDeleted,
        accountStatus: deletionResult.accountStatus,
        createdAt: deletionResult.createdAt,
      },
    });
  }

  async resetPassword(req: AuthenticatedRequest, res: Response): Promise<void> {
    const { id } = req.params;

    if (!id) {
      throw new AppError("Annotator ID is required", 400); // <-- important: stop here!
    }

    const { password } = req.body;

    // if (!password) {
    //   throw new AppError("Password is required", 400); // <-- important: stop here!
    // }

    const updatedAnnotator = await onboardingService.resetPassword(
      id,
      password
    );

    if (!updatedAnnotator) {
      throw new AppError("Annotator not found", 404); // <-- important: stop here!
    }

    res.status(200).json({
      message: "Annotator password reset successfully",
    });
  }

  async suspendUser(req: AuthenticatedRequest, res: Response): Promise<void> {
    const loggedInUser = req.user;

    if (!loggedInUser || loggedInUser.userRole !== Role.ADMIN) {
      throw new AppError("Only admins can suspend users", 403);
    }

    const { id } = req.params;

    if (!id) {
      throw new AppError("User ID is required", 400);
    }

    const { suspendedUntil } = req.body;
    if (!suspendedUntil) {
      throw new AppError("Suspension duration (in days) is required", 400);
    }

    const updatedUser = await onboardingService.suspendUserAccount(
      id,
      suspendedUntil
    );

    if (!updatedUser) {
      throw new AppError("User not found", 404);
    }

    res.status(200).json({
      message: "User suspended successfully",
      user: {
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        accountStatus: updatedUser.accountStatus,
        suspendedUntil: updatedUser.suspendedUntil,
      },
    });
  }

  async reactivateUser(req: AuthenticatedRequest, res: Response): Promise<void> {
    const loggedInUser = req.user;

    if (!loggedInUser || loggedInUser.userRole !== Role.ADMIN) {
      throw new AppError("Only admins can reactivate users", 403);
    }

    const { id } = req.params;

    if (!id) {
      throw new AppError("User ID is required", 400);
    }

    const updatedUser = await onboardingService.reactivateUserAccount(id);

    if (!updatedUser) {
      throw new AppError("User not found", 404);
    }

    res.status(200).json({
      message: "User reactivated successfully",
      user: {
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        accountStatus: updatedUser.accountStatus,
        suspendedUntil: updatedUser.suspendedUntil,
      },
    });
  }

  async activateUser(req: AuthenticatedRequest, res: Response): Promise<void> {
  const { id } = req.params;

  if (!id) {
    throw new AppError("Annotator ID is required", 400);
  }

  const updatedAnnotator = await onboardingService.activateUserAccount(id);

  if (!updatedAnnotator) {
    throw new AppError("Annotator not found", 404);
  }

  res.status(200).json({
    message: "Annotator activated successfully",
  });
}

}
