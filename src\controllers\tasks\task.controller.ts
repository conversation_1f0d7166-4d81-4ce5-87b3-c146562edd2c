import { Request, Response } from "express";
import taskService from "../../services/tasks/task.service";
import * as ApiResponse from "../../helper/apiResponse";
import { JwtPayload } from "jsonwebtoken";

interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}

class TaskController {
async create(req: AuthenticatedRequest, res: Response) {
  try {
     const { projectId } = req.params;
    const {
      name,
      description,
      color,
      priority,
      annotatorIds,
      startDate,
      dueDate,
    } = req.body;

    const createdById = req.user?.userId;

    const task = await taskService.createTask({
      name,
      description,
      color,
      priority,
      projectId,
      createdById,
      annotatorIds,
      startDate: new Date(startDate),
      dueDate: new Date(dueDate),
    });

    return ApiResponse.successResponseWithData(res, "Task created successfully", task);
  } catch (error) {
    console.error(error);
    return ApiResponse.errorResponse(res, "Failed to create task");
  }
}

async createCooworker(req: AuthenticatedRequest, res: Response) {
  try {
      const { projectId } = req.params;
    const {
      name,
      description,
      color,
      priority,
      annotatorIds,
      startDate,
      dueDate,
    } = req.body;

    const createdById = req.user?.userId;

    const task = await taskService.createCooworkerTask({
      name,
      description,
      color,
      priority,
      projectId,
      createdById,
      annotatorIds,
      startDate: new Date(startDate),
      dueDate: new Date(dueDate),
    });

    return ApiResponse.successResponseWithData(res, "Task created successfully", task);
  } catch (error) {
    console.error(error);
    return ApiResponse.errorResponse(res, "Failed to create task");
  }
}


  async getAll(req: Request, res: Response) {
    try {
       const { projectId } = req.query;
      const tasks = await taskService.getAllTasks(projectId as string);
      return ApiResponse.successResponseWithData(
        res,
        "Tasks fetched successfully",
        tasks,
        tasks.length
      );
    } catch (error) {
      console.error(error);
      return ApiResponse.errorResponse(res, "Failed to fetch tasks");
    }
  }

  async getCooworkerAll(req: Request, res: Response) {
    try {
      const tasks = await taskService.getAllTasks();
      return ApiResponse.successResponseWithData(
        res,
        "Tasks fetched successfully",
        tasks,
        tasks.length
      );
    } catch (error) {
      console.error(error);
      return ApiResponse.errorResponse(res, "Failed to fetch tasks");
    }
  }

  async getById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const task = await taskService.getTaskById(id);
      if (!task) {
        return ApiResponse.notFoundResponse(res, "Task not found");
      }
      return ApiResponse.successResponseWithData(
        res,
        "Task fetched successfully",
        task
      );
    } catch (error) {
      console.error(error);
      return ApiResponse.errorResponse(res, "Failed to fetch task");
    }
  }

  async update(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const data = req.body;

      if (data.startDate) data.startDate = new Date(data.startDate);
      if (data.dueDate) data.dueDate = new Date(data.dueDate);

      const updatedTask = await taskService.updateTask(id, data);

      return ApiResponse.successResponseWithData(
        res,
        "Task updated successfully",
        updatedTask
      );
    } catch (error) {
      console.error(error);
      return ApiResponse.errorResponse(res, "Failed to update task");
    }
  }

  async delete(req: Request, res: Response) {
    try {
      const { id } = req.params;
      await taskService.deleteTask(id);
      return ApiResponse.successResponse(res, "Task deleted successfully");
    } catch (error) {
      console.error(error);
      return ApiResponse.errorResponse(res, "Failed to delete task");
    }
  }

  async getByStatus(req: AuthenticatedRequest, res: Response) {
    try {
      const clientId = req.user?.userId;

      const tasks = await taskService.getTasksByStatus(clientId);

      return ApiResponse.successResponseWithData(
        res,
        "Tasks grouped by status",
        tasks
      );
    } catch (error) {
      console.error(error);
      return ApiResponse.errorResponse(res, "Failed to fetch grouped tasks");
    }
  }


  async getProjectStats(req: Request, res: Response) {
    const { projectId } = req.params;

    if (!projectId) {
      return res.status(400).json({
        success: false,
        message: "Project ID is required"
      });
    }

    try {
      const stats = await taskService.getProjectTaskStats(projectId);

      return res.status(200).json({
        success: true,
        message: "Project task statistics retrieved successfully",
        data: {
          total: stats.total,
          completed: stats.completed,
          pending: stats.inProgress, // Still mapping for UI
          toDo: stats.toDo
        }
      });
    } catch (error) {
      console.error("Error fetching project task stats:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to retrieve task statistics"
      });
    }
  }
  

}

export default new TaskController();
