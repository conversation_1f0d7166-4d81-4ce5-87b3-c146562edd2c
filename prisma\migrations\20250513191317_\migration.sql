/*
  Warnings:

  - A unique constraint covering the columns `[externalReference]` on the table `Subscription` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Payment" ADD COLUMN     "billingInfo" JSONB,
ADD COLUMN     "businessId" TEXT,
ADD COLUMN     "cardCountry" TEXT,
ADD COLUMN     "cardLastFour" TEXT,
ADD COLUMN     "cardNetwork" TEXT,
ADD COLUMN     "cardType" TEXT,
ADD COLUMN     "customerEmail" TEXT,
ADD COLUMN     "customerId" TEXT,
ADD COLUMN     "paymentLink" TEXT,
ADD COLUMN     "settlementAmount" DOUBLE PRECISION,
ADD COLUMN     "settlementCurrency" TEXT,
ADD COLUMN     "taxAmount" DOUBLE PRECISION;

-- AlterTable
ALTER TABLE "Subscription" ADD COLUMN     "externalReference" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "Subscription_externalReference_key" ON "Subscription"("externalReference");
