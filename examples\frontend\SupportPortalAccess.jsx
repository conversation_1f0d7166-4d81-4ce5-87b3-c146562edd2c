import React from 'react';
import Zoho<PERSON><PERSON><PERSON>utton from './ZohoSSOButton';
import './ZohoSSOButton.css';

const SupportPortalAccess = ({ authToken }) => {
  return (
    <div className="support-portal-section">
      <h2>Support Portal</h2>
      
      <p>
        Access our support portal to view your tickets, check knowledge base articles,
        and get help from our support team.
      </p>
      
      <ZohoSSOButton 
        authToken={authToken} 
        buttonText="Access Support Portal" 
      />
      
      <div className="support-portal-features">
        <div className="feature">
          <h3>View Your Tickets</h3>
          <p>Check the status of your existing support tickets</p>
        </div>
        
        <div className="feature">
          <h3>Knowledge Base</h3>
          <p>Find answers to common questions in our knowledge base</p>
        </div>
        
        <div className="feature">
          <h3>Live Chat</h3>
          <p>Chat with our support team in real-time</p>
        </div>
      </div>
    </div>
  );
};

export default SupportPortalAccess;