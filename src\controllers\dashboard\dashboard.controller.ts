import { Request, Response } from "express";
import { dashboardService } from "../../services/dashboard/dashboard.service";
import { JwtPayload } from "jsonwebtoken";
import {
  errorResponse,
  successResponseWithData,
} from "../../helper/apiResponse";

interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}
export class DashboardController {
  async getDashboardSummary(req: AuthenticatedRequest, res: Response) {
    try {
      const clientId = req.user?.userId;

      if (!clientId) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      const summary = await dashboardService.getSummary(clientId);
      res.status(200).json(summary);
    } catch (error) {
      console.error("Dashboard Summary Error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  }

  async getCooworkerDashboardSummary(req: AuthenticatedRequest, res: Response) {
    try {
      const cooworkeID = req.user?.userId;

      if (!cooworkeID) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      const summary = await dashboardService.getCooworkerSummary(cooworkeID);
      res.status(200).json(summary);
    } catch (error) {
      console.error("Dashboard Summary Error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  }

  async getDashboardCooWorkerSummary(req: AuthenticatedRequest, res: Response) {
    try {
      const cooworker = req.user?.userId;

      if (!cooworker) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      const summary = await dashboardService.getCoworkerSummary(cooworker);
      res.status(200).json(summary);
    } catch (error) {
      console.error("Dashboard Summary Error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  }

  async getClientAnnotators(req: AuthenticatedRequest, res: Response) {
    try {
      const clientId = req.user?.userId;
      if (!clientId) {
        return errorResponse(res, "Unauthorized or missing client ID");
      }

      const annotators = await dashboardService.getAnnotatorsByClientId(
        clientId
      );

      // Map to only extract developer (annotator) info
      const result = annotators.map((a) => a.developer);

      return successResponseWithData(
        res,
        "Annotators fetched successfully",
        result,
        result.length
      );
    } catch (error) {
      console.error(error);
      return errorResponse(res, "Failed to fetch annotators");
    }
  }

  async getCoowerkerClientAnnotators(req: AuthenticatedRequest, res: Response) {
    try {
      const clientId = req.user?.userId;
      if (!clientId) {
        return errorResponse(res, "Unauthorized or missing client ID");
      }

      const annotators = await dashboardService.getAnnotatorsByCoWorkersId(
        clientId
      );

      // Map to only extract developer (annotator) info
      const result = annotators.map((a) => a.developer);

      return successResponseWithData(
        res,
        "Annotators fetched successfully",
        result,
        result.length
      );
    } catch (error) {
      console.error(error);
      return errorResponse(res, "Failed to fetch annotators");
    }
  }

  async getCoordinatorDashboardSummary(
    req: AuthenticatedRequest,
    res: Response
  ) {
    try {
      const coordinatorId = req.user?.userId;

      if (!coordinatorId) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      const summary = await dashboardService.getCoordinatorSummary(
        coordinatorId
      );
      return successResponseWithData(
        res,
        "Coordinator dashboard data fetched successfully",
        summary
      );
    } catch (error) {
      console.error("Coordinator Dashboard Summary Error:", error);
      return errorResponse(res, "Failed to fetch coordinator dashboard data");
    }
  }

  async getAdminDashboardSummary(req: AuthenticatedRequest, res: Response) {
    try {
      // Check if user is admin
      if (req.user?.userRole !== "ADMIN") {
        return res
          .status(403)
          .json({ message: "Forbidden: Admin access required" });
      }

      const summary = await dashboardService.getAdminSummary();
      return successResponseWithData(
        res,
        "Admin dashboard data fetched successfully",
        summary
      );
    } catch (error) {
      console.error("Admin Dashboard Summary Error:", error);
      return errorResponse(res, "Failed to fetch admin dashboard data");
    }
  }
}
