import prisma from "../../prisma";
import { dodoAxios } from "../../lib/axiosDodo";
import paypalClient2 from "../../lib/axiosPaypal2";
import { Prisma, Provider } from "@prisma/client";
import * as path from "path";
import { chromium } from "playwright";
import * as fs from "fs";
const logoPath = path.resolve(__dirname, "../../../assets/image.png");
type PaymentWithRelations = Prisma.PaymentGetPayload<{
  include: {
    user: { select: { name: true; email: true; BillingAddress: true } };
    subscription: {
      include: { package: { select: { name: true; price: true } } };
    };
    BankTransferPayment: true;
  };
}>;
export class BillingsService {
  async getClientsBillingsdetails(
    clientId: string,
    query?: { skip?: number; take?: number }
  ) {
    const paymentsDetails = await prisma.payment.findMany({
      where: {
        userId: clientId,
      },
      select: {
        amount: true,
        paymentId: true,
        paymentMethod: true,
        status: true,
        subscription: {
          select: {
            startDate: true,
            endDate: true,
            next_billing_date: true,
            package: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      skip: query?.skip,
      take: query?.take,
      orderBy: { createdAt: "desc" },
    });

    return paymentsDetails;
  }

  async getCLientsSubscriptionsList(
    clientId: string,
    query?: { skip?: number; take?: number }
  ) {
    const paymentsDetails = await prisma.subscription.findMany({
      where: {
        userId: clientId,
      },
      select: {
        id: true,
        startDate: true,
        endDate: true,
        next_billing_date: true,
        status: true,
        package: {
          select: {
            name: true,
            price: true,
          },
        },
      },
      skip: query?.skip,
      take: query?.take,
      orderBy: { createdAt: "desc" },
    });
    return paymentsDetails;
  }

  async getPaymentsList(query?: { skip?: number; take?: number }) {
    const paymentsDetails = await prisma.payment.findMany({
      select: {
        user: {
          select: {
            name: true,
          },
        },
        createdAt: true,
        amount: true,
        paymentId: true,
        paymentMethod: true,
        status: true,
        subscription: {
          select: {
            startDate: true,
            endDate: true,
            next_billing_date: true,
            package: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      skip: query?.skip,
      take: query?.take,
      orderBy: { createdAt: "desc" },
    });

    return paymentsDetails;
  }

  async downloadInvoice(paymentId: string, clientId?: string) {
    try {
      // First, find the payment and verify access
      const payment: Prisma.PaymentGetPayload<{
        include: {
          user: { select: { name: true; email: true; BillingAddress: true } };
          subscription: {
            include: { package: { select: { name: true; price: true } } };
          };
          BankTransferPayment: true;
        };
      }> | null = await prisma.payment.findFirst({
        where: {
          paymentId: paymentId,
          ...(clientId && { userId: clientId }), // Only filter by userId if clientId is provided (for client access)
        },
        include: {
          user: {
            select: {
              name: true,
              email: true,
              BillingAddress: true,
            },
          },
          subscription: {
            include: {
              package: {
                select: {
                  name: true,
                  price: true,
                },
              },
            },
          },
          BankTransferPayment: true,
        },
      });

      if (!payment) {
        throw new Error("Payment not found or access denied");
      }

      let invoiceData: Buffer;

      // Fetch invoice based on provider
      console.log(
        `Fetching invoice for payment ${payment.paymentId} from provider ${payment.provider}`,
        logoPath
      );
      let html: string = "";

      console.log(payment);
      // let pdfBuffer =
      switch (payment.paymentMethod) {
        case "dodo":
          html = this.generateInvoice(payment, "Dodo");
          break;
        case "paypal":
          html = this.generateInvoice(payment, "PayPal");
          break;
        case "bank_transfer":
          html = this.generateInvoice(payment, "Bank Transfer");
          break;
        default:
          // pdfBuffer = this.generateFallbackReceipt(payment, payment.provider);
          break;
      }

      // Generate PDF using Playwright
      const browser = await chromium.launch();
      const page = await browser.newPage();

      // Set content directly
      await page.setContent(html, { waitUntil: "networkidle" });

      // Generate PDF
      const pdfBuffer = await page.pdf({
        format: "A4",
        printBackground: true,
        margin: { top: "20px", bottom: "20px", left: "20px", right: "20px" },
      });

      await browser.close();

      // if (html) {
      //   const browser = await chromium.launch();
      //   const page = await browser.newPage();
      //   await page.setContent(html, { waitUntil: "networkidle" });
      //   pdfBuffer = await page.pdf({
      //     format: "A4",
      //     printBackground: true,
      //     margin: { top: "20px", bottom: "20px", left: "20px", right: "20px" },
      //   });
      //   await browser.close();
      // }
      return {
        invoiceData: pdfBuffer,
        payment,
      };
    } catch (error: any) {
      console.error("Error in downloadInvoice service:", error);
      throw error;
    }
  }

  private generateInvoice(
    payment: PaymentWithRelations,
    provider: string
  ): string {
    const showBankDetails = payment.paymentMethod === "bank_transfer";
    const logoPath = path.resolve(__dirname, "../../../assets/image.png");

    // Convert the image to Base64
    const logoBase64 = fs.readFileSync(logoPath).toString("base64");
    const logoDataUri = `data:image/png;base64,${logoBase64}`;
    return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Tax Invoice</title>
  <style>
    body { font-family: Arial, sans-serif; font-size: 12px; margin: 0; padding: 0; }
    .invoice-box { max-width: 800px; margin: auto; padding: 30px; border: 1px solid #eee; }
    .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
    .header img { max-height: 50px; }
    .title { color: #d32f2f; font-size: 20px; font-weight: bold; text-align: right; }
    .company-details { font-size: 11px; line-height: 1.4; }
    .bill-to { margin-top: 20px; }
    .bill-to p, .company-details p { margin: 2px 0; }
    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
    table th { background: #d32f2f; color: #fff; padding: 8px; font-size: 11px; border: 1px solid #ddd; }
    table td { padding: 8px; font-size: 11px; border: 1px solid #ddd; }
    .totals { margin-top: 10px; width: 300px; float: right; }
    .totals td { padding: 6px 10px; border: 1px solid #ddd; font-size: 11px; }
    .bank-details { clear: both; margin-top: 40px; font-size: 11px; line-height: 1.4; }
    .footer { margin-top: 40px; font-size: 11px; text-align: center; }
  </style>
</head>
<body>
  <div class="invoice-box">
    <div class="header">
      <div><img src="${logoDataUri}" alt="Logo"></div>
      <div class="title">
        TAX INVOICE<br>
        # ${payment.paymentId || "INV-00001"}<br>
        Balance Due: ₹${payment.amount || "0.00"}
      </div>
    </div>

    <div class="company-details">
      <strong>Macgence Technologies Private Limited</strong><br>
      CN U72200UP2022PTC164392<br>
      7th Floor, Platina Heights C - 24 Sector 62<br>
      Noida Uttar Pradesh 201301, India<br>
      GSTIN: 09AAPCM7850A1Z9
    </div>

    <div class="bill-to">
      <strong>Bill To</strong><br>
       ${payment.user?.name || "N/A"}<br>
      ${payment.user?.BillingAddress?.address || ""}<br>
      ${payment.user?.BillingAddress?.street || ""}, ${
      payment.user.BillingAddress?.state || ""
    }<br>
      ${payment.user?.BillingAddress?.country || ""}
      <br><br>
      Invoice Date: ${new Date(payment.createdAt).toLocaleDateString()}<br>
      Terms: Net 25<br>
      Due Date: ${new Date(
        new Date(payment.createdAt).getTime() + 25 * 24 * 60 * 60 * 1000
      ).toLocaleDateString()}
    </div>

    <table>
      <tr>
        <th>#</th>
        <th>Item & Description</th>
        <th>HSN/SAC</th>
        <th>Qty</th>
        <th>Rate</th>
        <th>IGST</th>
        <th>Amount</th>
      </tr>
      <tr>
        <td>1</td>
        <td>${payment.subscription?.package?.name || "Subscription"}</td>
        <td>000000</td>
        <td>1.00</td>
        <td>${payment.currency || "₹"} ${
      payment.subscription?.package?.price || payment.amount
    }</td>
        <td>0.00%</td>
        <td>${payment.currency || "₹"} ${payment.amount || 0}</td>
      </tr>
    </table>

    <table class="totals">
      <tr><td>Items in Total</td><td>1.00</td></tr>
      <tr><td>Sub Total</td><td>${payment.amount || 0}</td></tr>
      <tr><td>IGST(0%)</td><td>0.00</td></tr>
      <tr><td><strong>Total</strong></td><td><strong>${
        payment.currency || "₹"
      } ${payment.amount || 0}</strong></td></tr>
    </table>

    <div style="clear:both;"></div>
    <p><strong>Total In Words:</strong> Indian Rupee ${
      payment.amount === 0 ? "Zero" : payment.amount
    } Only</p>

   ${
     payment.paymentMethod === "bank_transfer" && payment.BankTransferPayment
       ? `
  <div class="bank-details">
    <strong>Bank Details</strong><br>
    Beneficiary Name - ${payment.BankTransferPayment.bankHolderName}<br>
    Beneficiary's Bank Name - ${payment.BankTransferPayment.bankName}<br>
    Beneficiary A/c Number - ${payment.BankTransferPayment.accountNumber}<br>
    Transaction Date - ${new Date(
      payment.BankTransferPayment.transactionDate
    ).toLocaleDateString()}<br>
    Amount - ${payment.BankTransferPayment.currency} ${
           payment.BankTransferPayment.amount
         }<br>
    Transferred From (Optional) - ${
      payment.BankTransferPayment.transferedAccNo || "-"
    }<br>
  </div>
`
       : ""
   }

    <div class="footer">
      Thanks for your business.
    </div>
  </div>
</body>
</html>
  `;
  }

  private async fetchDodoInvoice(
    paymentId: string,
    payment?: any
  ): Promise<Buffer> {
    try {
      // Dodo API call to get invoice PDF
      // Try different possible endpoints for Dodo invoice
      let response;
      try {
        // First try the direct invoice endpoint
        response = await dodoAxios.get(`/invoices/payments/${paymentId}`, {
          responseType: "arraybuffer",
          headers: {
            Accept: "application/pdf",
          },
        });
      } catch (firstError) {
        // If that fails, try alternative endpoints
        try {
          response = await dodoAxios.get(`/invoices/payments/${paymentId}`, {
            responseType: "arraybuffer",
            headers: {
              Accept: "application/pdf",
            },
          });
        } catch (secondError) {
          // Try another common pattern
          response = await dodoAxios.get(`/payments/${paymentId}/receipt`, {
            responseType: "arraybuffer",
            headers: {
              Accept: "application/pdf",
            },
          });
        }
      }

      return Buffer.from(response.data);
    } catch (error: any) {
      console.error("Error fetching Dodo invoice:", error);
      if (error.response) {
        console.error(
          "Dodo API Error:",
          error.response.status,
          error.response.data
        );
      }
      // If all API calls fail, generate a simple text receipt as fallback
      console.log(
        "All Dodo invoice endpoints failed, generating fallback receipt"
      );
      return this.generateFallbackReceipt(payment, "Dodo");
    }
  }

  private async fetchPayPalInvoice(
    paymentId: string,
    payment?: any
  ): Promise<Buffer> {
    try {
      console.log(paymentId, "paymentId");
      // Step 2: Get payment details
      const paymentDetailsRes = await paypalClient2.get(
        `/v2/payments/captures/${paymentId}`
      );

      console.log(paymentDetailsRes, "paymentsDetails");

      const paymentDetails = paymentDetailsRes.data;
      const payerEmail =
        paymentDetails.payer?.email_address ||
        payment?.payer_email ||
        "<EMAIL>";
      const amount = paymentDetails.amount;
      const paymentTime = paymentDetails.create_time;

      // Step 3: Create invoice
      const invoiceCreateRes = await paypalClient2.post(
        `/v2/invoicing/invoices`,
        {
          detail: {
            invoice_number: `INV-${Date.now()}`,
            currency_code: amount.currency_code,
            note: "Thank you for your purchase!",
          },
          invoicer: {
            name: { given_name: "Your Company" },
            email_address: "<EMAIL>",
          },
          primary_recipients: [
            {
              billing_info: {
                name: { given_name: "Customer" },
                email_address: payerEmail,
              },
            },
          ],
          items: [
            {
              name: "Purchase",
              quantity: "1",
              unit_amount: {
                currency_code: amount.currency_code,
                value: amount.value,
              },
            },
          ],
        }
      );

      const invoiceId = invoiceCreateRes.data.id;

      // Step 4: Mark invoice as paid
      await paypalClient2.post(
        `/v2/invoicing/invoices/${invoiceId}/record-payment`,
        {
          payment_date: new Date(paymentTime).toISOString(),
          method: "PAYPAL",
          note: `Paid via PayPal Payment ID: ${paymentId}`,
        }
      );

      // Step 5: Download PDF
      const invoicePdfRes = await paypalClient2.get(
        `/v2/invoicing/invoices/${invoiceId}/pdf`
      );

      return Buffer.from(invoicePdfRes.data);
    } catch (error: any) {
      console.error("Error fetching PayPal invoice:", error);
      if (error.response) {
        console.error(
          "PayPal API Error:",
          error.response.status,
          error.response.data
        );
      }

      // Fallback: Try predefined endpoints (optional)
      try {
        const response = await paypalClient2.get(
          `/v2/invoicing/invoices/${paymentId}/pdf`,
          {
            responseType: "arraybuffer",
            headers: {
              Accept: "application/pdf",
            },
          }
        );
        return Buffer.from(response.data);
      } catch {
        console.log(
          "All PayPal invoice endpoints failed, generating fallback receipt"
        );
        return this.generateFallbackReceipt(payment, "PayPal");
      }
    }
  }

  private generateFallbackReceipt(payment: any, provider: string): Buffer {
    const receiptText = `
PAYMENT RECEIPT
===============

Provider: ${provider}
Payment ID: ${payment.paymentId}
Amount: ${payment.currency} ${payment.amount}
Status: ${payment.status}
Payment Method: ${payment.paymentMethod}
Date: ${new Date(payment.createdAt).toLocaleDateString()}

Customer Information:
Name: ${payment.user?.name || "N/A"}
Email: ${payment.user?.email || "N/A"}

${
  payment.subscription?.package
    ? `
Package Information:
Package: ${payment.subscription.package.name}
Price: ${payment.subscription.package.price}
`
    : ""
}

This is a system-generated receipt.
For any queries, please contact support.

Generated on: ${new Date().toISOString()}
    `;

    return Buffer.from(receiptText, "utf-8");
  }

  async cancelSubscription(
    subscriptionId: string,
    clientId?: string,
    reason?: string
  ) {
    // Use Prisma transaction to ensure atomicity
    return await prisma.$transaction(async (tx) => {
      try {
        // First, find the subscription and verify access with transaction client
        const subscription = await tx.subscription.findFirst({
          where: {
            id: subscriptionId,
            ...(clientId && { userId: clientId }), // Only filter by userId if clientId is provided (for client access)
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            package: {
              select: {
                id: true,
                name: true,
                price: true,
              },
            },
            Payment: {
              select: {
                id: true,
                paymentId: true,
                status: true,
              },
              where: {
                status: {
                  in: ["SUCCESS", "PENDING", "PROCESSING"]
                }
              }
            }
          },
        });

        if (!subscription) {
          throw new Error("Subscription not found or access denied");
        }

        // Check if subscription is already cancelled
        if (subscription.status === "CANCELLED") {
          throw new Error("Subscription is already cancelled");
        }

        // Check if subscription can be cancelled
        const nonCancellableStatuses = ["EXPIRED", "FAILED", "REFUNDED"];
        if (nonCancellableStatuses.includes(subscription.status)) {
          throw new Error(
            `Cannot cancel subscription with status: ${subscription.status}`
          );
        }

        let cancellationResult: any = {};
        let externalCancellationSuccess = false;

        // Cancel subscription with external provider based on the subscription data
        try {
          if (subscription.paypal_subscription_id) {
            // Cancel PayPal subscription
            cancellationResult = await this.cancelPayPalSubscription(
              subscription.paypal_subscription_id,
              reason
            );
            externalCancellationSuccess = cancellationResult.success;
          } else if (subscription.subscription_id) {
            // Cancel Dodo subscription
            cancellationResult = await this.cancelDodoSubscription(
              subscription.subscription_id,
              reason
            );
            externalCancellationSuccess = cancellationResult.success;
          } else {
            // No external subscription to cancel (e.g., bank transfer)
            externalCancellationSuccess = true;
            cancellationResult = {
              success: true,
              provider: "Internal",
              message: "No external subscription to cancel"
            };
          }
        } catch (externalError: any) {
          console.error("External cancellation failed:", externalError);
          // Log the external error but don't throw - we'll handle this gracefully
          cancellationResult = {
            success: false,
            error: externalError.message,
            provider: subscription.paypal_subscription_id ? "PayPal" : "Dodo"
          };
        }

        const currentTime = new Date();

        // Update subscription status in database within transaction
        const updatedSubscription = await tx.subscription.update({
          where: { id: subscriptionId },
          data: {
            status: "CANCELLED",
            endDate: currentTime,
            rawResponse: cancellationResult.rawResponse || subscription.rawResponse || {},
            updatedAt: currentTime,
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            package: {
              select: {
                id: true,
                name: true,
                price: true,
              },
            },
          },
        });

        // Cancel any pending payments associated with this subscription
        if (subscription.Payment && subscription.Payment.length > 0) {
          await tx.payment.updateMany({
            where: {
              subscriptionId: subscriptionId,
              status: {
                in: ["PENDING", "PROCESSING"]
              }
            },
            data: {
              status: "CANCELLED",
              updatedAt: currentTime,
              error_message: `Cancelled due to subscription cancellation. Reason: ${reason || "User requested"}`
            }
          });
        }

        // Create audit log for the cancellation
        await tx.userActivityLog.create({
          data: {
            userId: subscription.userId,
            activity: `Subscription cancelled - ID: ${subscriptionId}, Package: ${subscription.package.name}, Reason: ${reason || "Not provided"}${externalCancellationSuccess ? "" : " (External cancellation failed)"}`,
            timestamp: currentTime
          }
        });

        // Create subscription alert for the user
        await tx.subscriptionAlert.create({
          data: {
            type: "SUBSCRIPTION_CANCELLED",
            severity: "INFO",
            userId: subscription.userId,
            subscriptionId: subscriptionId,
            message: `Your subscription for ${subscription.package.name} has been cancelled${reason ? `. Reason: ${reason}` : ""}`,
            data: {
              packageName: subscription.package.name,
              cancelledAt: currentTime,
              reason: reason || "Not provided",
              externalCancellationSuccess
            }
          }
        });

        // Log the cancellation
        console.log(
          `Subscription ${subscriptionId} cancelled for user ${subscription.userId}. Reason: ${reason || "Not provided"}. External cancellation: ${externalCancellationSuccess ? "Success" : "Failed"}`
        );

        return {
          subscription: updatedSubscription,
          cancellationDetails: {
            ...cancellationResult,
            externalCancellationSuccess
          },
          message: externalCancellationSuccess 
            ? "Subscription cancelled successfully" 
            : "Subscription cancelled locally, but external provider cancellation failed. Please contact support if needed.",
          warning: !externalCancellationSuccess ? "External provider cancellation failed" : null
        };

      } catch (error: any) {
        console.error("Error in subscription cancellation transaction:", error);
        // The transaction will automatically rollback
        throw error;
      }
    }, {
      // Transaction options for better control
      maxWait: 10000, // Maximum time to wait for a transaction slot (10s)
      timeout: 30000, // Maximum time for the transaction to run (30s)
      isolationLevel: 'ReadCommitted' // Ensure read committed isolation
    });
  }

  private async cancelPayPalSubscription(
    paypalSubscriptionId: string,
    reason?: string
  ) {
    const maxRetries = 3;
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Cancelling PayPal subscription: ${paypalSubscriptionId} (Attempt ${attempt}/${maxRetries})`);

        const response = await paypalClient2.post(
          `/v1/billing/subscriptions/${paypalSubscriptionId}/cancel`,
          {
            reason: reason || "User requested cancellation",
          },
          {
            timeout: 10000, // 10 second timeout
          }
        );

        console.log(`PayPal subscription ${paypalSubscriptionId} cancelled successfully`);
        return {
          success: true,
          provider: "PayPal",
          externalId: paypalSubscriptionId,
          rawResponse: response.data,
          attempts: attempt
        };
      } catch (error: any) {
        lastError = error;
        console.error(`PayPal cancellation attempt ${attempt} failed:`, error.message);
        
        if (error.response) {
          console.error(
            "PayPal API Error:",
            error.response.status,
            error.response.data
          );
          
          // If it's already cancelled or doesn't exist, consider it a success
          if (error.response.status === 422 || 
              (error.response.data && error.response.data.name === "RESOURCE_NOT_FOUND")) {
            console.log("PayPal subscription already cancelled or not found, treating as success");
            return {
              success: true,
              provider: "PayPal",
              externalId: paypalSubscriptionId,
              rawResponse: error.response.data,
              note: "Subscription was already cancelled or not found",
              attempts: attempt
            };
          }
        }

        // If not the last attempt, wait before retrying
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt - 1) * 1000; // Exponential backoff
          console.log(`Waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // All attempts failed
    console.error(`All ${maxRetries} attempts to cancel PayPal subscription failed`);
    return {
      success: false,
      provider: "PayPal",
      externalId: paypalSubscriptionId,
      error: lastError?.message || "Unknown error",
      rawResponse: lastError?.response?.data,
      attempts: maxRetries
    };
  }

  private async cancelDodoSubscription(
    dodoSubscriptionId: string,
    reason?: string
  ) {
    const maxRetries = 3;
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Cancelling Dodo subscription: ${dodoSubscriptionId} (Attempt ${attempt}/${maxRetries})`);

        const response = await dodoAxios.patch(
          `/subscriptions/${dodoSubscriptionId}`,
          {
            status: "cancelled",
            billing: null,
            cancel_at_next_billing_date: null,
            disable_on_demand: null,
            metadata: reason ? { cancellation_reason: reason } : null,
            tax_id: null,
          },
          {
            timeout: 10000, // 10 second timeout
          }
        );

        console.log(`Dodo subscription ${dodoSubscriptionId} cancelled successfully`);
        return {
          success: true,
          provider: "Dodo",
          externalId: dodoSubscriptionId,
          rawResponse: response.data,
          attempts: attempt
        };
      } catch (error: any) {
        lastError = error;
        console.error(`Dodo cancellation attempt ${attempt} failed:`, error.message);
        
        if (error.response) {
          console.error(
            "Dodo API Error:",
            error.response.status,
            error.response.data
          );
          
          // If it's already cancelled or doesn't exist, consider it a success
          if (error.response.status === 404 || 
              error.response.status === 410 ||
              (error.response.data && 
               (error.response.data.message?.includes("already cancelled") ||
                error.response.data.message?.includes("not found")))) {
            console.log("Dodo subscription already cancelled or not found, treating as success");
            return {
              success: true,
              provider: "Dodo",
              externalId: dodoSubscriptionId,
              rawResponse: error.response.data,
              note: "Subscription was already cancelled or not found",
              attempts: attempt
            };
          }
        }

        // If not the last attempt, wait before retrying
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt - 1) * 1000; // Exponential backoff
          console.log(`Waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // All attempts failed
    console.error(`All ${maxRetries} attempts to cancel Dodo subscription failed`);
    return {
      success: false,
      provider: "Dodo",
      externalId: dodoSubscriptionId,
      error: lastError?.message || "Unknown error",
      rawResponse: lastError?.response?.data,
      attempts: maxRetries
    };
  }

  async getSubscriptionDetails(subscriptionId: string, clientId?: string) {
    try {
      const subscription = await prisma.subscription.findFirst({
        where: {
          id: subscriptionId,
          ...(clientId && { userId: clientId }),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          package: {
            select: {
              id: true,
              name: true,
              description: true,
              price: true,
              billingType: true,
              currency: true,
            },
          },
          Payment: {
            select: {
              id: true,
              paymentId: true,
              amount: true,
              status: true,
              createdAt: true,
              provider: true,
            },
            orderBy: {
              createdAt: "desc",
            },
            take: 5, // Get last 5 payments
          },
        },
      });

      if (!subscription) {
        throw new Error("Subscription not found or access denied");
      }

      return subscription;
    } catch (error: any) {
      console.error("Error fetching subscription details:", error);
      throw error;
    }
  }
}
