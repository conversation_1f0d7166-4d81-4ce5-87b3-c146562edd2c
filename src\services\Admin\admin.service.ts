// services/admin/admin.service.ts

import bcrypt from "bcryptjs";
import prisma from "../../prisma";
import { onboardingService } from "../onboarding/onboarding.service";

export class AdminService {
  async create(data: {
    name: string;
    lastname?: string;
    email: string;
    password?: string;
    autoSendLink?: boolean;
    createdById?: string;
  }) {
    const existing = await prisma.user.findUnique({ where: { email: data.email } });
    if (existing) throw new Error("Admin already exists with this email");

    let passwordHash: string | undefined = undefined;
    if (data.password && !data.autoSendLink) {
      passwordHash = await bcrypt.hash(data.password, 10);
    }

    const admin = await prisma.user.create({
      data: {
        name: data.name,
        lastname: data.lastname,
        email: data.email,
        passwordHash,
        role: "ADMIN",
        emailVerified: new Date(),
        createdById: data.createdById,
      }
    });

    // Create conversations with all existing project coordinators
    await this.createChatsWithCoordinators(admin.id);

    return admin;
  }

  async getAll() {
    return prisma.user.findMany({ where: { role: "ADMIN" } });
  }

  async getById(id: string) {
    return prisma.user.findUnique({ where: { id, role: "ADMIN" } });
  }

  async update(id: string, data: Partial<{ name: string; lastname: string; email: string }>) {
    return prisma.user.update({
      where: { id },
      data,
    });
  }

  async delete(id: string) {
    // First check if the user exists and get their role
    const user = await prisma.user.findUnique({
      where: { id },
      select: { id: true, role: true }
    });

    if (!user) {
      throw new Error("User not found");
    }

    // If it's an ANNOTATOR or PROJECT_COORDINATOR, use the onboarding service's smart deletion logic
    if (user.role === "ANNOTATOR" || user.role === "PROJECT_COORDINATOR") {
      return await onboardingService.deleteAnnotator(id);
    }

    // For ADMIN users, check for relationships before deletion
    if (user.role === "ADMIN") {
      // Check if admin has created other users
      const createdUsersCount = await prisma.user.count({
        where: { createdById: id }
      });

      // Check if admin has other relationships
      const [conversationParticipantsCount, messagesCount, adminTasksCount] = await Promise.all([
        prisma.conversationParticipant.count({ where: { userId: id } }),
        prisma.message.count({ where: { senderId: id } }),
        prisma.adminTask.count({ where: { OR: [{ createdById: id }, { assignedToId: id }] } })
      ]);

      const hasRelationships = createdUsersCount > 0 || conversationParticipantsCount > 0 || 
                              messagesCount > 0 || adminTasksCount > 0;

      if (hasRelationships) {
        // Perform soft delete for admins with relationships
        const softDeletedUser = await prisma.user.update({
          where: { id },
          data: {
            isDeleted: true,
            accountStatus: "DELETED",
            email: `deleted_${Date.now()}_${user.id}@deleted.local`,
          }
        });

        return {
          ...softDeletedUser,
          deletionType: "soft",
          message: "Admin has relationships and has been soft deleted",
          relationshipDetails: [
            ...(createdUsersCount > 0 ? [`${createdUsersCount} created user(s)`] : []),
            ...(conversationParticipantsCount > 0 ? [`${conversationParticipantsCount} conversation(s)`] : []),
            ...(messagesCount > 0 ? [`${messagesCount} message(s)`] : []),
            ...(adminTasksCount > 0 ? [`${adminTasksCount} admin task(s)`] : [])
          ]
        };
      } else {
        // Hard delete if no relationships
        const deletedUser = await prisma.user.delete({ where: { id } });
        return {
          ...deletedUser,
          deletionType: "hard",
          message: "Admin has been permanently deleted as no relationships were found",
          relationshipDetails: []
        };
      }
    }

    // For other roles (CLIENT, COWORKER), use soft delete as default
    const softDeletedUser = await prisma.user.update({
      where: { id },
      data: {
        isDeleted: true,
        accountStatus: "DELETED",
        email: `deleted_${Date.now()}_${user.id}@deleted.local`,
      }
    });

    return {
      ...softDeletedUser,
      deletionType: "soft",
      message: "User has been soft deleted",
      relationshipDetails: []
    };
  }


  async suspend(id: string, duration: "24h" | "7d" | "30d" | "always") {
    const now = new Date();
    let suspendedUntil: Date | null = null;
  
    if (duration !== "always") {
      const hoursMap = { "24h": 24, "7d": 7 * 24, "30d": 30 * 24 };
      const hours = hoursMap[duration];
      suspendedUntil = new Date(now.getTime() + hours * 60 * 60 * 1000);
    }
  
    return prisma.user.update({
      where: { id },
      data: {
        accountStatus: "SUSPENDED",
        suspendedUntil,
      },
    });
  }

  //activate admin
  async activate(id: string) {
    const admin = await prisma.user.findUnique({ where: { id, role: "ADMIN" } });
  
    if (!admin) throw new Error("Admin not found");
  
    return prisma.user.update({
      where: { id },
      data: {
        accountStatus: "ACTIVE",
        suspendedUntil: null,
      },
    });
  }

  async resetPassword(adminId: string, newPassword: string) {
    const passwordHash = await bcrypt.hash(newPassword, 10);
    return prisma.user.update({
      where: { id: adminId },
      data: { passwordHash }
    });
  }
  

  async deleteByOtherAdmin(id: string, requestedById: string) {
    const adminToDelete = await prisma.user.findUnique({ where: { id } });
  
    if (!adminToDelete || adminToDelete.role !== "ADMIN") {
      throw new Error("Admin not found or not an admin");
    }
  
    if (adminToDelete.createdById !== requestedById) {
      throw new Error("Only the creator admin can delete this admin");
    }
  
    // Use the same smart deletion logic
    return this.delete(id);
  }

  private async createChatsWithCoordinators(adminId: string) {
    const coordinators = await prisma.user.findMany({
      where: {
        role: "PROJECT_COORDINATOR",
        accountStatus: "ACTIVE",
      },
      select: {
        id: true,
      },
    });

    const conversationPromises = coordinators.map(coordinator => {
      return prisma.conversation.create({
        data: {
          isGroup: false,
          participants: {
            create: [
              { userId: adminId },
              { userId: coordinator.id }
            ],
          },
        },
      });
    });

    await Promise.all(conversationPromises);
  }

  //client action
  // Suspend a client
async suspendClient(id: string, duration: "24h" | "7d" | "30d" | "always") {
  const now = new Date();
  let suspendedUntil: Date | null = null;

  if (duration !== "always") {
    const hoursMap = { "24h": 24, "7d": 7 * 24, "30d": 30 * 24 };
    suspendedUntil = new Date(now.getTime() + hoursMap[duration]! * 60 * 60 * 1000);
  }

  return prisma.user.update({
    where: { id },
    data: {
      accountStatus: "SUSPENDED",
      suspendedUntil,
    },
  });
}

// Activate a client
async activateClient(id: string) {
  const client = await prisma.user.findUnique({ where: { id } });
  if (!client || client.role !== "CLIENT") throw new Error("Client not found");

  return prisma.user.update({
    where: { id },
    data: {
      accountStatus: "ACTIVE",
      suspendedUntil: null,
    },
  });
}

// Reset client password
async resetClientPassword(id: string, newPassword: string) {
  const passwordHash = await bcrypt.hash(newPassword, 10);
  return prisma.user.update({
    where: { id },
    data: { passwordHash },
  });
}

// Soft delete a client
async softDeleteClient(id: string) {
  const client = await prisma.user.findUnique({ where: { id } });
  if (!client || client.role !== "CLIENT") throw new Error("Client not found");

  return prisma.user.update({
    where: { id },
    data: { isDeleted: true },
  });
}

}
