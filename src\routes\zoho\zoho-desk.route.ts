import { Router } from "express";
import { ZohoDeskController } from "../../controllers/zoho/zoho-desk.controller";
import { asyncHandler } from "../../middlewares/asyncHandler";
import { authenticateUser } from "../../middlewares/auth.middleware";
import upload from "../../utils/multer";

const router = Router();
const controller = new ZohoDeskController();

// ============ AUTHENTICATION ROUTES ============
router.route("/auth/initiate").get(asyncHandler(controller.initiateDeskAuth.bind(controller)));
router.route("/auth/callback").get(asyncHandler(controller.handleDeskCallback.bind(controller)));

// ============ TICKET ROUTES ============
router.route("/tickets")
  .get(asyncHandler(controller.listTickets.bind(controller)))
  .post(asyncHandler(controller.createTicket.bind(controller)));

router.route("/tickets/:ticketId")
  .get(asyncHandler(controller.getTicket.bind(controller)))
  .patch(asyncHandler(controller.updateTicket.bind(controller)))
  .delete(asyncHandler(controller.deleteTicket.bind(controller)));

// ============ TICKET COMMENTS ============
router.route("/tickets/:ticketId/comments")
  .get(asyncHandler(controller.getTicketComments.bind(controller)))
  .post(asyncHandler(controller.addTicketComment.bind(controller)));

// ============ TICKET ATTACHMENTS ============
router.route("/tickets/:ticketId/attachments")
  .post(upload.single('file'), asyncHandler(controller.uploadAttachment.bind(controller)));

// ============ CONTACT ROUTES ============
router.route("/contacts")
  .get(asyncHandler(controller.searchContacts.bind(controller)))
  .post(asyncHandler(controller.createContact.bind(controller)));

router.route("/contacts/:contactId")
  .get(asyncHandler(controller.getContact.bind(controller)));

// ============ UTILITY ROUTES ============
router.route("/departments")
  .get(asyncHandler(controller.getDepartments.bind(controller)));

router.route("/agents")
  .get(asyncHandler(controller.getAgents.bind(controller)));

// ============ SSO ROUTES ============
// router.route("/sso")
//   .get(authenticateUser, asyncHandler(controller.generateSSOUrl.bind(controller)));

export default router;