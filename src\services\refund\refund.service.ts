import { Prisma, PrismaClient } from "@prisma/client";
import { AppError } from "../../utils/ApiError";
import { dodoAxios } from "../../lib/axiosDodo";
const prisma = new PrismaClient();

export class RefundService {
  async create(body: { paymentId: string }) {
    const { paymentId } = body;
    const existingRefund = await prisma.refund.findFirst({
      where: { paymentId },
    });
    if (existingRefund) {
      throw new AppError("Refund already Created", 400);
    }

    const response = await dodoAxios.post("refunds", {
      payment_id: paymentId,
    });
    return response;
  }

  async getAll(query: { page: number; limit: number; search: string }) {
    const { page, limit, search } = query;
    const skip = (page - 1) * limit;
    const take = limit;
    const where: Prisma.RefundWhereInput | undefined = search
      ? {
          OR: [
            {
              paymentId: {
                contains: search,
                mode: Prisma.QueryMode.insensitive,
              },
            },
            {
              paymentId: {
                contains: search,
                mode: Prisma.QueryMode.insensitive,
              },
            },
          ],
        }
      : undefined;
    const packages = await prisma.refund.findMany({
      where,
      skip,
      take,
      orderBy: { createdAt: "desc" },
    });
    const totalCount = await prisma.refund.count({ where });
    return {
      packages,
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
    };
  }

  async getById(id: string) {
    const refund = await prisma.refund.findUnique({
      where: { id },
    });
    if (!refund) {
      throw new AppError("Refund not found", 404);
    }
    return refund;
  }
}
