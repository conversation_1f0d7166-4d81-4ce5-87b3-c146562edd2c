// import nodemailer from "nodemailer";

// export const transporter = nodemailer.createTransport({
//   host: "smtp-relay.brevo.com",
//   port: 587,
//   secure: false, 
//   auth: {
//     user: "<EMAIL>",
//     pass: "mknQMRrOv4E6ytP7",
//   },
// });


// utils/mailTransporter.ts
// utils/mailTransporter.ts

import nodemailer from "nodemailer";

const getMailTransporter = nodemailer.createTransport({
  host: process.env.MAIL_HOST,
  port: 587,
  secure: false,
  auth: {
    user: process.env.MAIL_USER,
    pass: process.env.MAIL_PASS,
  },
});
export default getMailTransporter;

