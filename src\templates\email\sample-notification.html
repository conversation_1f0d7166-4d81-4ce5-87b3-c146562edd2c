<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{subject}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #6610f2;
            margin-bottom: 10px;
        }
        .notification-badge {
            background: linear-gradient(135deg, #6610f2, #e83e8c);
            color: white;
            padding: 15px 25px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 25px;
            font-weight: bold;
        }
        .content-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .action-button {
            display: inline-block;
            background: #6610f2;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 15px 0;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        .priority-high {
            border-left: 4px solid #dc3545;
        }
        .priority-medium {
            border-left: 4px solid #ffc107;
        }
        .priority-low {
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{companyName}}</div>
        </div>

        <div class="notification-badge">
            {{notificationType}} Notification
        </div>

        <h2>{{title}}</h2>
        
        <p>Hi {{firstName}},</p>
        
        <div class="content-section priority-{{priority}}">
            <h4>{{notificationTitle}}</h4>
            <p>{{message}}</p>
            
            {{#details}}
            <ul>
                {{#items}}
                <li>{{.}}</li>
                {{/items}}
            </ul>
            {{/details}}
        </div>

        {{#actionRequired}}
        <div style="text-align: center; margin: 25px 0;">
            <a href="{{actionUrl}}" class="action-button">{{actionText}}</a>
        </div>
        {{/actionRequired}}

        {{#additionalInfo}}
        <div class="content-section">
            <h4>Additional Information</h4>
            <p>{{additionalInfo}}</p>
        </div>
        {{/additionalInfo}}

        <p>If you have any questions, please don't hesitate to contact our support team.</p>
        
        <div class="footer">
            <p>Best regards,<br>
            <strong>{{teamName}}</strong><br>
            <strong>{{companyName}}</strong></p>
            
            <p style="margin-top: 20px;">
                <a href="{{supportEmail}}" style="color: #6610f2;">{{supportEmail}}</a> | 
                <a href="{{websiteUrl}}" style="color: #6610f2;">{{websiteUrl}}</a>
            </p>
            
            <p style="font-size: 12px; color: #999; margin-top: 15px;">
                This notification was sent to {{email}} on {{currentDate}}
            </p>
        </div>
    </div>
</body>
</html>