import prisma from "../../prisma";
import { AppError } from "../../utils/ApiError";
import { createPasswordResetToken } from "../../utils/generate-token";
import { mailSender } from "../../utils/mailSender";
import { EmailIntegrationService } from "../email/email-integration.service";
import bcrypt from "bcryptjs";
import { AssignmentService } from "../assignment/assignment.service";
import EnhancedEmailService from "../email/enhanced-email.service";

export class CoworkerService {
  // private static emailIntegration = new EmailIntegrationService();
  private emailService: EnhancedEmailService;

  constructor() {
    this.emailService = new EnhancedEmailService();
  }
  async inviteCoworker(
    email: string,
    permissionRole: "EDIT" | "VIEW",
    clientId: string
  ) {
    if (!email || !permissionRole) {
      throw new AppError("Email and Permission Role are required.", 404);
    }

    const existingUser = await prisma.user.findUnique({ where: { email } });
    const client = await prisma.user.findUnique({
      where: {
        id: clientId,
      },
      select: {
        email: true,
        name: true
      },
    });

    const newToken = await createPasswordResetToken();
    await prisma.passwordResetToken.create({
      data: {
        identifier: email,
        token: newToken.token,
        expires: newToken.expiresAt,
      },
    });

    if (existingUser) {
      if (existingUser.role !== "COWORKER") {
        throw new Error(
          "This email is already registered with a different role."
        );
      }

      await prisma.user.update({
        where: { id: existingUser.id },
        data: {
          clientOwnerId: clientId,
          coworkerPermission: permissionRole,
          updatedAt: new Date(),
        },
      });

      // await CoworkerService.sendInviteEmail(email, newToken.token); // send invite
      await this.emailService.sendEmail({
        to: email,
        subject: `Welcome to ${
          process.env.COMPANY_NAME || "Our Platform"
        } - Verify Your Email`,
        template: "invite-coworker",
        data: {
          coworkerName: "CoWorker",
          // otpCode: otp,
          invitationLink: `${
            process.env.FRONTEND_URL || "http://localhost:3000"
          }/coworker/accept-invite?email=${encodeURIComponent(email)}&token=${
            newToken.token
          }`,
          role: permissionRole,
          timeLimit: 10, // 10 minutes
          companyName: process.env.COMPANY_NAME || "Our Platform",
          supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
          phoneNumber: process.env.SUPPORT_PHONE || "******-567-8900",
          websiteUrl: process.env.FRONTEND_URL || "https://company.com",
        },
      });
      return { updated: true };
    }

    const coworker = await prisma.user.create({
      data: {
        email,
        name: "",
        role: "COWORKER",
        clientOwnerId: clientId,
        coworkerPermission: permissionRole,
        passwordHash: null,
        emailVerified: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    await this.emailService.sendEmail({
      to: email,
      subject: `Welcome to ${
        process.env.COMPANY_NAME || "Our Platform"
      } - Verify Your Email`,
      template: "invite-coworker",
      data: {
        coworkerName: "CoWorker",
        clientName: client?.name,
        platformName: "CLIENT",
        // otpCode: otp,
        invitationLink: `${
          process.env.FRONTEND_URL || "http://localhost:3000"
        }/coworker/accept-invite?email=${encodeURIComponent(email)}&token=${
          newToken.token
        }`,

        role: permissionRole,
        timeLimit: 10, // 10 minutes
        companyName: process.env.COMPANY_NAME || "Our Platform",
        supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
        phoneNumber: process.env.SUPPORT_PHONE || "******-567-8900",
        websiteUrl: process.env.FRONTEND_URL || "https://company.com",
      },
    });
    return { created: true, coworker };
  }

  static async listCoworkers(clientId: string, query: any) {
    if (!clientId) {
      throw new AppError("Client ID is required.", 400);
    }
    console.log("Client ID:", clientId);
    console.log("Query:", query);

    const { page = 1, limit = 10 } = query;

    const coworkers = await prisma.user.findMany({
      where: {
        clientOwnerId: clientId,
        role: "COWORKER",
      },
      select: {
        id: true,
        name: true,
        email: true,
        coworkerPermission: true,
        createdAt: true,
        updatedAt: true,
      },
      skip: (page - 1) * limit,
      take: Number(limit),
    });
    const totalCount = await prisma.user.count({
      where: {
        clientOwnerId: clientId,
        role: "COWORKER",
      },
    });
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;
    const nextPage = hasNextPage ? page + 1 : null;
    const previousPage = hasPreviousPage ? page - 1 : null;

    return {
      data: coworkers,
      totalCount,
      totalPages,
      hasNextPage,
      hasPreviousPage,
      nextPage,
      previousPage,
    };
  }

  static async updateCoworkerPermission(
    coworkerId: string,
    permissionRole: "EDIT" | "VIEW",
    clientId: string
  ) {
    const coworker = await prisma.user.findFirst({
      where: {
        id: coworkerId,
        clientOwnerId: clientId,
        role: "COWORKER",
      },
    });

    if (!coworker) {
      throw new AppError("Coworker not found or not authorized.", 400);
    }

    return await prisma.user.update({
      where: { id: coworkerId },
      data: { coworkerPermission: permissionRole, updatedAt: new Date() },
    });
  }

  static async resendInvite(coworkerId: string, clientId: string) {
    const coworker = await prisma.user.findFirst({
      where: {
        id: coworkerId,
        clientOwnerId: clientId,
        role: "COWORKER",
      },
    });

    if (!coworker) {
      throw new AppError("Coworker not found or unauthorized.", 400);
    }

    const newToken = await createPasswordResetToken();
    await prisma.passwordResetToken.create({
      data: {
        identifier: coworker.email,
        token: newToken.token,
        expires: newToken.expiresAt,
      },
    });

    await CoworkerService.sendInviteEmail(coworker.email, newToken.token);
    return { message: "Invite resent successfully." };
  }

  static async acceptInvite(
    email: string,
    password: string,
    name: string,
    token: string
  ) {
    const coworker = await prisma.user.findUnique({ where: { email } });

    if (!coworker || coworker.role !== "COWORKER") {
      throw new AppError("Invalid coworker invite or already registered.", 400);
    }

    if (coworker.passwordHash) {
      throw new AppError("Coworker already accepted the invite.", 400);
    }

    const tokenExist = await prisma.passwordResetToken.findFirst({
      where: {
        token: token,
        identifier: email,
      },
    });
    if (!tokenExist || tokenExist.expires < new Date()) {
      throw new AppError("Invalid credentails", 401);
    }
    const hashedPassword = await bcrypt.hash(password, 10);

    await prisma.user.update({
      where: { id: coworker.id },
      data: {
        name,
        passwordHash: hashedPassword,
        updatedAt: new Date(),
        emailVerified: new Date(),
      },
    });

    await prisma.passwordResetToken.deleteMany({ where: { token } });

    const assignment = await prisma.assignment.findFirst({
      where: {
        client: {
          coWorkers: {
            some: { id: coworker.id },
          },
        },
      },
      include: {
        client: true,
        developer: true,
        coordinator: true,
      },
    });

    if (!assignment) {
      console.log("No assignment found for coworker.");
      return { message: "Invite accepted, but no related chat setup found." };
    }

    const client = assignment.clientId;
    const developer = assignment.developerId;
    const coordinator = assignment.coordinatorId;

    const createDMIfNotExist = async (userA: string, userB: string) => {
      const existing = await prisma.conversation.findFirst({
        where: {
          isGroup: false,
          participants: {
            every: {
              userId: { in: [userA, userB] },
            },
          },
        },
      });

      if (!existing) {
        await prisma.conversation.create({
          data: {
            isGroup: false,
            participants: {
              create: [{ userId: userA }, { userId: userB }],
            },
          },
        });
      }
    };

    // 🔹 Create DM chats
    await createDMIfNotExist(coworker.id, client);
    await createDMIfNotExist(coworker.id, developer);
    await createDMIfNotExist(coworker.id, coordinator);

    // 🔸 Add coworker to all private group chats where the client is a member
    const allClientGroups = await prisma.groupChat.findMany({
      where: {
        isPrivate: true,
        members: {
          some: {
            userId: client,
          },
        },
      },
      include: {
        members: true,
      },
    });

    for (const group of allClientGroups) {
      const isAlreadyMember = group.members.some(
        (member) => member.userId === coworker.id
      );

      if (!isAlreadyMember) {
        await prisma.groupChat.update({
          where: { id: group.id },
          data: {
            members: {
              create: { userId: coworker.id },
            },
          },
        });
      }
    }

    return { message: "Invite accepted successfully." };
  }

  static async sendInviteEmail(email: string, token: string) {
    const inviteLink = `${
      process.env.FRONTEND_URL || "http://localhost:3000"
    }/coworker/accept-invite?email=${encodeURIComponent(email)}&token=${token}`;
    console.log("Generated Invite Link:", inviteLink);

    // Get client information for better email context
    const client = await prisma.user.findFirst({
      where: {
        coWorkers: {
          some: { email },
        },
      },
      select: {
        name: true,
        Profile: {
          select: {
            companyName: true,
          },
        },
      },
    });

    // await this.emailIntegration.sendCoworkerInvitation(email, {
    //   coworkerName: "Team Member", // We don't have the coworker's name yet
    //   clientName: client?.Profile?.companyName || client?.name || "Your Team",
    //   role: "Coworker",
    //   invitationLink: inviteLink,
    // });
  }
}
