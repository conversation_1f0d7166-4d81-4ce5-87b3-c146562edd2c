#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Email Template Preview System...\n');

// Check if required dependencies are installed
function checkDependencies() {
  console.log('1. Checking dependencies...');
  
  try {
    require('commander');
    console.log('   ✅ Commander.js is installed');
  } catch {
    console.log('   ❌ Commander.js is missing');
    console.log('   Installing commander...');
    execSync('npm install commander@^12.0.0', { stdio: 'inherit' });
    console.log('   ✅ Commander.js installed');
  }
  
  try {
    require('axios');
    console.log('   ✅ Axios is installed');
  } catch {
    console.log('   ⚠️  Axios is missing (optional for testing)');
  }
}

// Check if email template files exist
function checkTemplateFiles() {
  console.log('\n2. Checking template files...');
  
  const requiredFiles = [
    'src/controllers/email-preview/email-preview.controller.ts',
    'src/services/email-preview/email-preview.service.ts',
    'src/routes/email-preview/email-preview.route.ts',
    'src/scripts/email-template-cli.ts'
  ];
  
  let allFilesExist = true;
  
  requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`   ✅ ${file}`);
    } else {
      console.log(`   ❌ ${file} - Missing`);
      allFilesExist = false;
    }
  });
  
  if (!allFilesExist) {
    console.log('   ⚠️  Some template preview files are missing');
    console.log('   Please ensure all files are properly created');
  }
}

// Check if routes are properly configured
function checkRouteConfiguration() {
  console.log('\n3. Checking route configuration...');
  
  const indexRoutePath = 'src/routes/index.ts';
  
  if (fs.existsSync(indexRoutePath)) {
    const indexContent = fs.readFileSync(indexRoutePath, 'utf8');
    
    if (indexContent.includes('email-preview')) {
      console.log('   ✅ Email preview routes are configured');
    } else {
      console.log('   ❌ Email preview routes are not configured in index.ts');
      console.log('   Please add the email preview routes to src/routes/index.ts');
    }
  } else {
    console.log('   ❌ Routes index file not found');
  }
}

// Check template structure
function checkTemplateStructure() {
  console.log('\n4. Checking template structure...');
  
  const templateIndexPath = 'src/templates/index.ts';
  const templateEmailDir = 'src/templates/email';
  
  if (fs.existsSync(templateIndexPath)) {
    console.log('   ✅ Template index file exists');
    
    const indexContent = fs.readFileSync(templateIndexPath, 'utf8');
    if (indexContent.includes('EMAIL_TEMPLATES')) {
      console.log('   ✅ EMAIL_TEMPLATES mapping found');
    } else {
      console.log('   ⚠️  EMAIL_TEMPLATES mapping not found');
    }
    
    if (indexContent.includes('TemplateRenderer')) {
      console.log('   ✅ TemplateRenderer class found');
    } else {
      console.log('   ⚠️  TemplateRenderer class not found');
    }
  } else {
    console.log('   ❌ Template index file not found');
  }
  
  if (fs.existsSync(templateEmailDir)) {
    const htmlFiles = fs.readdirSync(templateEmailDir).filter(f => f.endsWith('.html'));
    console.log(`   ✅ Found ${htmlFiles.length} HTML template files`);
  } else {
    console.log('   ❌ Email templates directory not found');
  }
}

// Test CLI functionality
function testCLI() {
  console.log('\n5. Testing CLI functionality...');
  
  try {
    execSync('npm run email-cli list', { stdio: 'pipe' });
    console.log('   ✅ CLI list command works');
  } catch (error) {
    console.log('   ❌ CLI list command failed');
    console.log(`   Error: ${error.message}`);
  }
}

// Display next steps
function displayNextSteps() {
  console.log('\n🎉 Setup Complete!\n');
  
  console.log('📋 Next Steps:');
  console.log('1. Start the development server:');
  console.log('   npm run dev\n');
  
  console.log('2. Access the email preview dashboard:');
  console.log('   http://localhost:3000/api/email-preview\n');
  
  console.log('3. Use CLI tools:');
  console.log('   npm run email-cli list                    # List all templates');
  console.log('   npm run email-cli preview welcome-mail   # Preview a template');
  console.log('   npm run email-cli validate               # Validate all templates\n');
  
  console.log('4. Interactive workflow:');
  console.log('   npm run email-workflow\n');
  
  console.log('5. Test the system:');
  console.log('   npm run test-email-system\n');
  
  console.log('📚 Documentation:');
  console.log('   See EMAIL_TEMPLATE_PREVIEW_GUIDE.md for detailed instructions\n');
  
  console.log('🔧 Configuration:');
  console.log('   Edit email-template-config.json to customize settings\n');
}

// Main setup function
function runSetup() {
  try {
    checkDependencies();
    checkTemplateFiles();
    checkRouteConfiguration();
    checkTemplateStructure();
    testCLI();
    displayNextSteps();
    
    console.log('✅ Email Template Preview System is ready to use!');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    console.log('\nPlease check the error above and try again.');
    console.log('If you need help, refer to EMAIL_TEMPLATE_PREVIEW_GUIDE.md');
  }
}

// Run setup
runSetup();