import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { Role } from "@prisma/client";
import { AppError } from "../utils/ApiError";
import prisma from "../prisma";

interface CustomJwtPayload {
  userId: string;
  email: string;
  userRole: Role;
}

// Extend Request to include user
export interface AuthenticatedRequest extends Request {
  user?: CustomJwtPayload;
}

const secretKey: string =
  process.env.JWT_SECRET || "abcdefghijklmnopqrstuvwxyz1234567890";

const authMiddleware = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader =
      req.headers.authorization ||
      (req.headers.Authorization as string) ||
      req.cookies.authToken;

    if (!authHeader) {
      throw new AppError("Token is missing", 401);
    }

    const token = authHeader.startsWith("Bearer ")
      ? authHeader.split(" ")[1]
      : authHeader;

    const decoded = jwt.verify(token, secretKey) as CustomJwtPayload;
    if (!decoded) {
      throw new AppError("Invalid token", 401);
    }
    if (!decoded.userRole) {
      throw new AppError("User role is missing in the token", 403);
    }

    // Check user account status in database
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        accountStatus: true,
        suspendedUntil: true,
        isDeleted: true,
      },
    });

    if (!user) {
      throw new AppError("User not found", 401);
    }

    // Check if user account is deleted
    if (user.isDeleted) {
      throw new AppError("Your account has been deleted. Please contact support.", 403);
    }

    // Check if user account is suspended
    if (user.accountStatus === "SUSPENDED") {
      // Check if suspension has expired
      if (user.suspendedUntil && new Date() > user.suspendedUntil) {
        // Suspension has expired, reactivate the account
        await prisma.user.update({
          where: { id: user.id },
          data: {
            accountStatus: "ACTIVE",
            suspendedUntil: null,
          },
        });
      } else {
        // Account is still suspended
        const suspensionMessage = user.suspendedUntil 
          ? `Your account is suspended until ${user.suspendedUntil.toLocaleDateString()}. Please contact support.`
          : "Your account has been suspended. Please contact support.";
        throw new AppError(suspensionMessage, 403);
      }
    }

    req.user = decoded;
    next();
  } catch (error) {
    console.error(error);
    if (error instanceof AppError) {
      res.status(error.statusCode).json({ status: 0, message: error.message });
    } else {
      res.status(401).json({ status: 0, message: "Invalid or expired token" });
    }
  }
};

export default authMiddleware;
