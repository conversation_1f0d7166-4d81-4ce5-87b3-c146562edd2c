// Optional: Run once to register your webhook
import dotenv from "dotenv";
dotenv.config(); // MUST be called before accessing process.env
import axios from "axios";

const PAYPAL_API = process.env.PAYPAL_API!;
const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID!;
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET!;

let tokenCache = {
  token: "",
  expiresAt: 0,
};
const basicAuth = Buffer.from(
  `${PAYPAL_CLIENT_ID}:${PAYPAL_CLIENT_SECRET}`
).toString("base64");
export async function fetchAccessToken(): Promise<string> {
  console.log(PAYPAL_API, "mangaread")
  const response = await axios.post(
    `${PAYPAL_API}/v1/oauth2/token`,
    "grant_type=client_credentials",
    {
      headers: {
        Authorization: `Basic ${basicAuth}`,
        "Content-Type": "application/x-www-form-urlencoded",
      },
    }
  );

  const { access_token, expires_in } = response.data;

  tokenCache.token = access_token;
  tokenCache.expiresAt = Date.now() + expires_in * 1000 - 60_000; // expire 1 min early

  return access_token;
}

(async () => {
  const token = await fetchAccessToken();

  const res = await axios.post(
    `${process.env.PAYPAL_API}/v1/notifications/webhooks`,
    {
      url: "https://bc02-2409-40e3-3e-6320-3c3a-83b-e113-f5de.ngrok-free.app/webhooks/paypal",
      event_types: [
        { name: "BILLING.SUBSCRIPTION.CREATED" },
        { name: "BILLING.SUBSCRIPTION.ACTIVATED" },
        { name: "BILLING.SUBSCRIPTION.CANCELLED" },
        { name: "PAYMENT.SALE.COMPLETED" },
      ],
    },
    {
      headers: { Authorization: `Bearer ${token}` },
    }
  );

  console.log("Webhook registered ✅:", res.data.id);
})();
