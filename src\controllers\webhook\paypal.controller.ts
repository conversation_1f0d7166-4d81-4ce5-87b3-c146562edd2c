import dotenv from "dotenv";

dotenv.config();
import { Request, Response } from "express";
import paypalClient2, {
  fetchAccessToken,
  verifyPaypalWebhook,
} from "../../lib/axiosPaypal2";
import prisma from "../../prisma";
import { PaymentStatus, SubscriptionStatus } from "@prisma/client";

const PAYPAL_API = process.env.PAYPAL_API;
const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID;
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET;
const WEBHOOK_URL = process.env.PAYPAL_WEBHOOK_URL; // You can define this in .env

export class PayPalWebhookController {
  async handleWebhook(req: Request, res: Response): Promise<void> {
    try {
      const verified = await verifyPaypalWebhook(req);
      if (!verified) {
        res.status(400).send("Invalid webhook");
        return;
      }
      const event = req.body;
      const resource = event.resource;

      console.log("📨 PayPal Webhook:", event.event_type);

      switch (event.event_type) {
        case "BILLING.SUBSCRIPTION.CREATED": {
          const subscriptionId = resource.custom_id;

          console.log("Subscription Created:", subscriptionId);

          await prisma.subscription.updateMany({
            where: { id: subscriptionId },
            data: {
              subscription_id: resource.id,
              status: SubscriptionStatus.PENDING,
              rawResponse: resource,
            },
          });

          break;
        }

        case "BILLING.SUBSCRIPTION.ACTIVATED": {
          const subscriptionId = resource.custom_id;

          console.log("Subscription Activated:", subscriptionId);

          await prisma.subscription.updateMany({
            where: { id: subscriptionId },
            data: {
              subscription_id: resource.id,
              status: SubscriptionStatus.ACTIVE,
              startDate: new Date(resource.start_time),
              endDate: new Date(resource.billing_info.next_billing_time),
              next_billing_date: new Date(
                resource.billing_info.next_billing_time
              ),
              rawResponse: resource,
            },
          });

          break;
        }

        case "BILLING.SUBSCRIPTION.CANCELLED": {
          const subscriptionId = resource.id;

          console.log("Subscription Cancelled:", subscriptionId);

          await prisma.subscription.updateMany({
            where: { subscription_id: subscriptionId },
            data: {
              status: SubscriptionStatus.CANCELLED,
              endDate: new Date(),
              rawResponse: resource,
            },
          });

          break;
        }

        case "PAYMENT.SALE.COMPLETED": {
          const sale = resource;
          const billingAgreementId = sale.billing_agreement_id;

          console.log("Payment Completed:", sale.id);

          const subscription = await prisma.subscription.findFirst({
            where: {
              OR: [
                { id: sale.custom },
                // { externalReference: billingAgreementId },
              ],
            },
          });

          if (!subscription) {
            console.warn(
              "No matching subscription for payment:",
              billingAgreementId
            );
            break;
          }

          await prisma.payment.create({
            data: {
              paymentId: sale.id,
              subscriptionId: subscription.id,
              userId: subscription.userId,
              amount: parseFloat(sale.amount.total),
              currency: sale.amount.currency,
              paymentMethod: "paypal",
              status: PaymentStatus.SUCCESS,
              rawResponse: sale,
              settlementAmount: parseFloat(sale.amount.total),
              settlementCurrency: sale.amount.currency,
              customerEmail: sale.payer?.payer_info?.email || undefined,
              cardCountry: sale.payer?.payer_info?.country_code || undefined,
            },
          });

          // Optional: update billing dates on subscription
          await prisma.subscription.update({
            where: { id: subscription.id },
            data: {
              previous_billing_date:
                subscription.next_billing_date ?? new Date(),
              next_billing_date: undefined, // set based on your billing logic
            },
          });

          break;
        }

        default:
          console.log("Unhandled event:", event.event_type);
      }

      res.sendStatus(200);
    } catch (error) {
      console.error("PayPal webhook error:", error);
      res.sendStatus(500);
    }
  }

  // async registerWebhook(req: Request, res: Response) {
  //   try {
  //     if (
  //       !PAYPAL_API ||
  //       !PAYPAL_CLIENT_ID ||
  //       !PAYPAL_CLIENT_SECRET ||
  //       !WEBHOOK_URL
  //     ) {
  //       return res
  //         .status(500)
  //         .json({ message: "Missing environment variables" });
  //     }

  //     const accessToken = await fetchAccessToken();

  //     // Register webhook
  //     const webhookRes = await paypalClient2.post(
  //       `/v1/notifications/webhooks`,
  //       {
  //         url: WEBHOOK_URL,
  //         event_types: [
  //           { name: "BILLING.SUBSCRIPTION.CREATED" },
  //           { name: "BILLING.SUBSCRIPTION.ACTIVATED" },
  //           { name: "BILLING.SUBSCRIPTION.CANCELLED" },
  //           { name: "PAYMENT.SALE.COMPLETED" },
  //         ],
  //       },
  //       {
  //         headers: { Authorization: `Bearer ${accessToken}` },
  //       }
  //     );

  //     res.json({
  //       message: "Webhook registered ✅",
  //       webhook_id: webhookRes.data.id,
  //     });
  //   } catch (error: any) {
  //     console.error(
  //       "Webhook registration failed:",
  //       error?.response?.data || error.message
  //     );
  //     res.status(500).json({
  //       message: "Webhook registration failed",
  //       error: error?.response?.data || error.message,
  //     });
  //   }
  // }
  async registerWebhook(req: Request, res: Response): Promise<void> {
    try {
      if (
        !PAYPAL_API ||
        !PAYPAL_CLIENT_ID ||
        !PAYPAL_CLIENT_SECRET ||
        !WEBHOOK_URL
      ) {
        res.status(500).json({ message: "Missing env variables" });
        return;
      }

      const accessToken = await fetchAccessToken();

      const webhookRes = await paypalClient2.post(
        `/v1/notifications/webhooks`,
        {
          url: WEBHOOK_URL,
          event_types: [
            { name: "BILLING.SUBSCRIPTION.CREATED" },
            { name: "BILLING.SUBSCRIPTION.ACTIVATED" },
            { name: "BILLING.SUBSCRIPTION.CANCELLED" },
            { name: "PAYMENT.SALE.COMPLETED" },
          ],
        },
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        }
      );

      res.json({
        message: "Webhook registered ✅",
        webhook_id: webhookRes.data.id,
      });
    } catch (error: any) {
      console.error(
        "Webhook registration failed:",
        error?.response?.data || error.message
      );
      res.status(500).json({
        message: "Webhook registration failed",
        error: error?.response?.data || error.message,
      });
    }
  }

async deleteWebHook(req: Request, res: Response): Promise<void> {
  try {
    if (!PAYPAL_API || !PAYPAL_CLIENT_ID || !PAYPAL_CLIENT_SECRET || !WEBHOOK_URL) {
      res.status(500).json({ message: "Missing environment variables" });
      return;
    }

    const { webhook_id } = req.params;
    if (!webhook_id) {
      res.status(400).json({ message: "Missing webhook ID in request" });
      return;
    }

    const accessToken = await fetchAccessToken();

    await paypalClient2.delete(`/v1/notifications/webhooks/${webhook_id}`, {
      headers: { Authorization: `Bearer ${accessToken}` },
    });

    res.status(200).json({
      message: "Webhook deleted ✅",
      webhook_id,
    });
  } catch (error: any) {
    console.error(
      "Webhook deletion failed:",
      error?.response?.data || error.message
    );
    res.status(500).json({
      message: "Webhook deletion failed",
      error: error?.response?.data || error.message,
    });
  }
}


}
