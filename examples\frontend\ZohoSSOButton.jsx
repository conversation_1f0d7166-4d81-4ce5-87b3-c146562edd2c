import React, { useState } from 'react';
import axios from 'axios';

/**
 * A button component that redirects users to Zoho Desk using SSO
 */
const ZohoSSOButton = ({ authToken, buttonText = 'Access Support Portal', className = '', redirectTo = '' }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleSSOLogin = async () => {
    if (!authToken) {
      setError('Authentication required');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Get SSO URL from the backend
      const response = await axios.get('/api/zoho-desk/sso', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        params: redirectTo ? { redirectTo } : {}
      });

      // Redirect to the SSO URL
      const { ssoUrl } = response.data.data;
      window.location.href = ssoUrl;
    } catch (err) {
      console.error('Failed to get SSO URL:', err);
      setError('Failed to access support portal. Please try again.');
      setIsLoading(false);
    }
  };

  return (
    <div className="zoho-sso-container">
      <button
        onClick={handleSSOLogin}
        disabled={isLoading}
        className={`zoho-sso-button ${className}`}
      >
        {isLoading ? 'Loading...' : buttonText}
      </button>
      
      {error && (
        <div className="zoho-sso-error">
          {error}
        </div>
      )}
    </div>
  );
};

export default ZohoSSOButton;