import { Router } from "express";
import { asyncHandler } from "../../middlewares/asyncHandler";
import { SelfTaskController } from "../../controllers/selfTask/selfTask.controller";
import authMiddleware from "../../middlewares/checkAuth";
import { subscriptionMiddleware } from "../../middlewares/subscription.middleware";

const router = Router();
const controller = new SelfTaskController();

router.post(
  "/create-task",
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  asyncHandler(controller.create.bind(controller))
);
router.get(
  "/get-all-task",
  authMiddleware,
  asyncHandler(controller.getAll.bind(controller))
);
router.get(
  "/task/pending",
  authMiddleware,
  asyncHandler(controller.getPending.bind(controller))
);
router.get(
  "/task/in-progress",
  authMiddleware,
  asyncHandler(controller.getInProgress.bind(controller))
);
router.get(
  "/task/completed",
  authMiddleware,
  asyncHandler(controller.getCompleted.bind(controller))
);
router.get(
  "/get-single-task/:id",
  authMiddleware,
  asyncHandler(controller.getById.bind(controller))
);
router.patch(
  "/update-task/:id",
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  asyncHandler(controller.update.bind(controller))
);
router.delete(
  "/delete-task/:id",
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  asyncHandler(controller.delete.bind(controller))
);

export default router;
