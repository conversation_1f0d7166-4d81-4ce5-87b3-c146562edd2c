-- CreateEnum
CREATE TYPE "BankTransferStatus" AS ENUM ('PENDING', 'VERIFIED', 'REJECTED');

-- CreateTable
CREATE TABLE "BankTransferPayment" (
    "id" TEXT NOT NULL,
    "paymentId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "subscriptionId" TEXT,
    "packageId" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "transactionId" TEXT NOT NULL,
    "bankHolderName" TEXT NOT NULL,
    "accountNumber" TEXT NOT NULL,
    "ifscCode" TEXT NOT NULL,
    "bankName" TEXT NOT NULL,
    "screenshotUrl" TEXT NOT NULL,
    "status" "BankTransferStatus" NOT NULL DEFAULT 'PENDING',
    "adminNotes" TEXT,
    "verifiedAt" TIMESTAMP(3),
    "verifiedById" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BankTransferPayment_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "BankTransferPayment_paymentId_key" ON "BankTransferPayment"("paymentId");

-- CreateIndex
CREATE INDEX "BankTransferPayment_userId_idx" ON "BankTransferPayment"("userId");

-- CreateIndex
CREATE INDEX "BankTransferPayment_status_idx" ON "BankTransferPayment"("status");

-- CreateIndex
CREATE INDEX "BankTransferPayment_createdAt_idx" ON "BankTransferPayment"("createdAt");

-- AddForeignKey
ALTER TABLE "BankTransferPayment" ADD CONSTRAINT "BankTransferPayment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BankTransferPayment" ADD CONSTRAINT "BankTransferPayment_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES "Subscription"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BankTransferPayment" ADD CONSTRAINT "BankTransferPayment_packageId_fkey" FOREIGN KEY ("packageId") REFERENCES "Package"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BankTransferPayment" ADD CONSTRAINT "BankTransferPayment_verifiedById_fkey" FOREIGN KEY ("verifiedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BankTransferPayment" ADD CONSTRAINT "BankTransferPayment_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payment"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
