/*
  Warnings:

  - A unique constraint covering the columns `[userId,date]` on the table `AttendanceSummary` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateTable
CREATE TABLE "Product" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "paypalId" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Product_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Product_name_key" ON "Product"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Product_paypalId_key" ON "Product"("paypalId");

-- CreateIndex
CREATE UNIQUE INDEX "AttendanceSummary_userId_date_key" ON "AttendanceSummary"("userId", "date");
