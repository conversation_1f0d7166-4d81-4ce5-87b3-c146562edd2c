-- CreateTable
CREATE TABLE "SubscriptionAlert" (
    "id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "severity" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "subscriptionId" TEXT,
    "message" TEXT NOT NULL,
    "data" JSONB,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SubscriptionAlert_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "SubscriptionAlert_userId_idx" ON "SubscriptionAlert"("userId");

-- CreateIndex
CREATE INDEX "SubscriptionAlert_type_idx" ON "SubscriptionAlert"("type");

-- CreateIndex
CREATE INDEX "SubscriptionAlert_severity_idx" ON "SubscriptionAlert"("severity");

-- CreateIndex
CREATE INDEX "SubscriptionAlert_createdAt_idx" ON "SubscriptionAlert"("createdAt");

-- CreateIndex
CREATE INDEX "SubscriptionAlert_isRead_idx" ON "SubscriptionAlert"("isRead");

-- AddForeignKey
ALTER TABLE "SubscriptionAlert" ADD CONSTRAINT "SubscriptionAlert_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SubscriptionAlert" ADD CONSTRAINT "SubscriptionAlert_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES "Subscription"("id") ON DELETE SET NULL ON UPDATE CASCADE;
