import { Router } from "express";
import { async<PERSON>and<PERSON> } from "../../middlewares/asyncHandler";
import { AdminTaskController } from "../../controllers/adminTask/adminTask.controller";
import authMiddleware from "../../middlewares/checkAuth";
import { subscriptionMiddleware } from "../../middlewares/subscription.middleware";

const router = Router();
const controller = new AdminTaskController();

router.post("/create-task", 
  authMiddleware, 
  subscriptionMiddleware.checkSubscriptionWithWarning,
  asyncHandler(controller.create.bind(controller))
);
router.get("/get-all-task", asyncHandler(controller.getAll.bind(controller)));
router.get("/get-single-task/:id", asyncHandler(controller.getById.bind(controller)));
router.put("/update-task/:id", asyncHandler(controller.update.bind(controller)));
router.delete("/delete-task/:id", async<PERSON><PERSON><PERSON>(controller.delete.bind(controller)));

export default router;
