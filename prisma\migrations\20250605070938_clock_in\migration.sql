/*
  Warnings:

  - You are about to drop the column `timeIn` on the `AttendanceSummary` table. All the data in the column will be lost.
  - You are about to drop the column `timeOut` on the `AttendanceSummary` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "AttendanceSummary" DROP COLUMN "timeIn",
DROP COLUMN "timeOut";

-- CreateTable
CREATE TABLE "ClockSession" (
    "id" TEXT NOT NULL,
    "attendanceSummaryId" TEXT NOT NULL,
    "timeIn" TIMESTAMP(3) NOT NULL,
    "timeOut" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ClockSession_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "ClockSession" ADD CONSTRAINT "ClockSession_attendanceSummaryId_fkey" FOREIGN KEY ("attendanceSummaryId") REFERENCES "AttendanceSummary"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
