# User Blocking System

This document describes the user blocking functionality that allows admins to block clients, annotators, coordinators, and coworkers during onboarding or at any time.

## Overview

The system provides comprehensive user account management capabilities:

- **Block users**: Prevent users from logging in
- **Temporary suspension**: Block users for a specific duration
- **Permanent suspension**: Block users indefinitely
- **Reactivation**: Restore access to previously blocked users
- **Automatic expiration**: Temporary suspensions automatically expire

## User Account Statuses

The system uses the following account statuses:

- `ACTIVE`: User can log in and access the system
- `SUSPENDED`: User is blocked and cannot log in
- `DELETED`: User account is marked as deleted

## API Endpoints

### 1. Block/Suspend User

**Endpoint**: `PATCH /onboarding/update-status/:id/suspend`

**Description**: Updates user account status to block them from logging in.

**Headers**:
```
Authorization: Bearer <admin_token>
Content-Type: application/json
```

**Request Body**:
```json
{
  "status": "SUSPENDED"  // or "ACTIVE", "DELETED"
}
```

**Response**:
```json
{
  "message": "User account status updated successfully",
  "user": {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "role": "ANNOTATOR",
    "accountStatus": "SUSPENDED"
  }
}
```

### 2. Temporary Suspension

**Endpoint**: `PATCH /onboarding/suspension/:id`

**Description**: Suspends user for a specific number of days.

**Headers**:
```
Authorization: Bearer <admin_token>
Content-Type: application/json
```

**Request Body**:
```json
{
  "suspendedUntil": 7  // Number of days
}
```

**Response**:
```json
{
  "message": "User suspended successfully",
  "user": {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "role": "ANNOTATOR",
    "accountStatus": "SUSPENDED",
    "suspendedUntil": "2024-01-15T10:30:00.000Z"
  }
}
```

### 3. Reactivate User

**Endpoint**: `PATCH /onboarding/reactivate/:id`

**Description**: Reactivates a suspended user account.

**Headers**:
```
Authorization: Bearer <admin_token>
Content-Type: application/json
```

**Response**:
```json
{
  "message": "User reactivated successfully",
  "user": {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "role": "ANNOTATOR",
    "accountStatus": "ACTIVE",
    "suspendedUntil": null
  }
}
```

### 4. Get All Users

**Endpoint**: `GET /onboarding/getall`

**Description**: Retrieves all users with their account status.

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `role`: Filter by user role (optional)

**Headers**:
```
Authorization: Bearer <admin_token>
```

**Response**:
```json
{
  "message": "Users fetched successfully",
  "data": {
    "data": [
      {
        "id": "user_id",
        "name": "User Name",
        "email": "<EMAIL>",
        "role": "ANNOTATOR",
        "accountStatus": "ACTIVE",
        "suspendedUntil": null,
        "isDeleted": false,
        "createdAt": "2024-01-01T10:00:00.000Z"
      }
    ],
    "totalCount": 50,
    "totalPages": 5,
    "hasNextPage": true,
    "hasPreviousPage": false,
    "nextPage": 2,
    "previousPage": null
  }
}
```

## Supported User Types

The blocking system supports the following user types:

- **CLIENT**: Business clients who use the platform
- **ANNOTATOR**: Users who perform annotation tasks
- **PROJECT_COORDINATOR**: Users who manage projects
- **COWORKER**: Team members working under clients

**Note**: ADMIN users cannot be blocked or suspended.

## Authentication Flow

### Login Process

When a user attempts to log in:

1. **Credential Validation**: Email and password are verified
2. **Account Status Check**: System checks if account is deleted or suspended
3. **Suspension Expiry Check**: If suspended temporarily, system checks if suspension has expired
4. **Auto-Reactivation**: Expired suspensions are automatically cleared
5. **Access Decision**: Login is allowed or denied based on account status

### Middleware Protection

Every authenticated request goes through account status validation:

1. **Token Verification**: JWT token is validated
2. **User Lookup**: User account is fetched from database
3. **Status Check**: Account status and suspension expiry are verified
4. **Auto-Reactivation**: Expired suspensions are cleared automatically
5. **Request Processing**: Request continues or is blocked based on status

## Error Messages

### Login Errors

- **Deleted Account**: "Your account has been deleted. Please contact support."
- **Permanent Suspension**: "Your account has been suspended. Please contact support."
- **Temporary Suspension**: "Your account is suspended until [date]. Please contact support."

### API Errors

- **Unauthorized**: "Only admins can [action] users"
- **User Not Found**: "User not found"
- **Invalid Status**: "Invalid account status"
- **Admin Protection**: "Cannot suspend admin users"

## Usage Examples

### Block a User Permanently

```bash
curl -X PATCH \
  http://localhost:3000/onboarding/update-status/user_id/suspend \
  -H "Authorization: Bearer admin_token" \
  -H "Content-Type: application/json" \
  -d '{"status": "SUSPENDED"}'
```

### Suspend User for 7 Days

```bash
curl -X PATCH \
  http://localhost:3000/onboarding/suspension/user_id \
  -H "Authorization: Bearer admin_token" \
  -H "Content-Type: application/json" \
  -d '{"suspendedUntil": 7}'
```

### Reactivate User

```bash
curl -X PATCH \
  http://localhost:3000/onboarding/reactivate/user_id \
  -H "Authorization: Bearer admin_token"
```

### Get All Suspended Users

```bash
curl -X GET \
  "http://localhost:3000/onboarding/getall?role=ANNOTATOR" \
  -H "Authorization: Bearer admin_token"
```

## Database Schema

The user blocking system uses the following database fields:

```sql
-- User table fields
accountStatus    AccountStatus? @default(ACTIVE)  -- ACTIVE, SUSPENDED, DELETED
suspendedUntil   DateTime?                        -- Suspension expiry date
isDeleted        Boolean        @default(false)   -- Soft delete flag
```

## Security Considerations

1. **Admin Only**: Only users with ADMIN role can block/unblock users
2. **Admin Protection**: ADMIN users cannot be blocked or suspended
3. **Token Validation**: All requests require valid JWT tokens
4. **Database Consistency**: Account status is checked on every request
5. **Automatic Cleanup**: Expired suspensions are automatically cleared

## Testing

Use the provided test script to verify the blocking functionality:

```bash
node test-user-blocking.js
```

The test script covers:
- User creation and blocking
- Login prevention for blocked users
- Temporary suspension with expiry
- User reactivation
- Different user types (CLIENT, ANNOTATOR, etc.)

## Troubleshooting

### Common Issues

1. **User Still Can Login**: Check if suspension has expired or if account status was properly updated
2. **Admin Cannot Block User**: Verify the admin has proper JWT token and ADMIN role
3. **Suspension Not Working**: Ensure middleware is properly configured and database is updated
4. **Auto-Reactivation Not Working**: Check if system time is correct and suspension expiry logic is working

### Debug Steps

1. Check user account status in database
2. Verify JWT token contains correct user ID and role
3. Check server logs for authentication middleware errors
4. Ensure database connection is working
5. Verify API endpoints are properly configured