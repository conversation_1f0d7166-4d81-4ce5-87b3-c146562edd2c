<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Changed Successfully</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 10px;
        }
        .success-banner {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 25px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .confirmation-details {
            background: #d4edda;
            border-left: 4px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .security-alert {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        .reset-button {
            display: inline-block;
            background: #dc3545;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px;
        }
        .contact-button {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        .contact-info {
            margin-top: 20px;
        }
        .contact-info a {
            color: #28a745;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{companyName}}</div>
        </div>

        <div class="success-banner">
            <h2>🔐 Password Changed Successfully</h2>
        </div>

        <p>Hi {{firstName}},</p>

        <div class="confirmation-details">
            <h4>✅ Password Update Confirmation</h4>
            <p>This is a confirmation that the password for your <strong>{{platformName}}</strong> account was successfully changed on <strong>{{dateTime}}</strong>.</p>
            
            <p><strong>Account:</strong> {{email}}</p>
            <p><strong>Changed on:</strong> {{dateTime}}</p>
            <p><strong>IP Address:</strong> {{ipAddress}}</p>
        </div>

        <p>If you made this change, no further action is needed. Your account is secure.</p>

        <div class="security-alert">
            <h4>⚠️ Didn't change your password?</h4>
            <p>If you did not request this change, please take immediate action to secure your account:</p>
            
            <div class="action-buttons">
                <a href="{{resetLink}}" class="reset-button">🔒 Reset Password Immediately</a>
                <a href="mailto:{{supportEmail}}" class="contact-button">📞 Contact Support</a>
            </div>
        </div>

        <p><strong>Your account security is very important to us.</strong> If you have any concerns or questions, please don't hesitate to contact our support team.</p>

        <div class="footer">
            <p>Thank you,<br>
            <strong>{{companyName}}</strong></p>
            
            <div class="contact-info">
                <a href="{{websiteUrl}}">{{websiteUrl}}</a> | 
                <a href="mailto:{{supportEmail}}">{{supportEmail}}</a>
            </div>
        </div>
    </div>
</body>
</html>