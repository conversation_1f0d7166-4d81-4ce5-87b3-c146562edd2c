import { Router } from 'express';
import UserCommunicationController from '../../controllers/user-communication/user-communication.controller';
import checkAuth from '../../middlewares/checkAuth';
import { hasRole, hasMultipleRole } from '../../middlewares/checkRole';

const router = Router();
const userCommunicationController = new UserCommunicationController();

/**
 * @swagger
 * /api/user-communication/send-password-creation-link:
 *   post:
 *     summary: Send password creation link to user
 *     tags: [User Communication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - firstName
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address
 *               firstName:
 *                 type: string
 *                 description: User's first name
 *     responses:
 *       200:
 *         description: Password creation link sent successfully
 *       400:
 *         description: Invalid request
 */
router.post('/send-password-creation-link', userCommunicationController.sendPasswordCreationLink);

/**
 * @swagger
 * /api/user-communication/send-account-suspended:
 *   post:
 *     summary: Send account suspended notification
 *     tags: [User Communication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - reason
 *             properties:
 *               userId:
 *                 type: string
 *                 description: ID of the user to suspend
 *               reason:
 *                 type: string
 *                 description: Reason for suspension
 *               duration:
 *                 type: number
 *                 description: Suspension duration in days (optional)
 *     responses:
 *       200:
 *         description: Account suspended and notification sent
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: User not found
 */
router.post('/send-account-suspended', checkAuth, hasMultipleRole(['ADMIN']), userCommunicationController.sendAccountSuspended);

/**
 * @swagger
 * /api/user-communication/send-account-reactivated:
 *   post:
 *     summary: Send account reactivated notification
 *     tags: [User Communication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: string
 *                 description: ID of the user to reactivate
 *     responses:
 *       200:
 *         description: Account reactivated and notification sent
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: User not found
 */
router.post('/send-account-reactivated', checkAuth, hasMultipleRole(['ADMIN']), userCommunicationController.sendAccountReactivated);

/**
 * @swagger
 * /api/user-communication/send-welcome-email:
 *   post:
 *     summary: Send welcome email with meeting link
 *     tags: [User Communication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - meetingLink
 *             properties:
 *               userId:
 *                 type: string
 *                 description: ID of the user
 *               meetingLink:
 *                 type: string
 *                 format: uri
 *                 description: Link to schedule discovery meeting
 *               yourName:
 *                 type: string
 *                 description: Name of the person sending the email
 *               teamName:
 *                 type: string
 *                 description: Team name
 *     responses:
 *       200:
 *         description: Welcome email sent successfully
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Authentication required
 *       404:
 *         description: User not found
 */
router.post('/send-welcome-email', checkAuth, userCommunicationController.sendWelcomeEmail);

/**
 * @swagger
 * /api/user-communication/send-password-changed:
 *   post:
 *     summary: Send password changed notification
 *     tags: [User Communication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: string
 *                 description: ID of the user
 *               ipAddress:
 *                 type: string
 *                 description: IP address from which password was changed
 *     responses:
 *       200:
 *         description: Password change notification sent
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Authentication required
 *       404:
 *         description: User not found
 */
router.post('/send-password-changed', checkAuth, userCommunicationController.sendPasswordChanged);

/**
 * @swagger
 * /api/user-communication/send-team-assignment:
 *   post:
 *     summary: Send team assignment notification
 *     tags: [User Communication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - projectId
 *               - annotatorId
 *               - coordinatorId
 *             properties:
 *               userId:
 *                 type: string
 *                 description: ID of the client user
 *               projectId:
 *                 type: string
 *                 description: ID of the project
 *               annotatorId:
 *                 type: string
 *                 description: ID of the assigned annotator
 *               coordinatorId:
 *                 type: string
 *                 description: ID of the assigned coordinator
 *     responses:
 *       200:
 *         description: Team assignment notification sent
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Authentication required
 *       404:
 *         description: One or more entities not found
 */
router.post('/send-team-assignment', checkAuth, userCommunicationController.sendTeamAssignment);

/**
 * @swagger
 * /api/user-communication/test-email-service:
 *   get:
 *     summary: Test email service connection
 *     tags: [User Communication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Email service is working correctly
 *       401:
 *         description: Authentication required
 *       500:
 *         description: Email service connection failed
 */
router.get('/test-email-service', checkAuth, userCommunicationController.testEmailService);

export default router;