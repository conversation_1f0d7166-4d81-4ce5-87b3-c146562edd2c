import prisma from "../../prisma";

export class FAQService {
  async create(data: { question: string; answer: string }) {
    return prisma.fAQ.create({ data });
  }

  async getAll() {
    return prisma.fAQ.findMany();
  }

  async getById(id: string) {
    return prisma.fAQ.findUnique({ where: { id } });
  }

  async update(id: string, data: Partial<{ question: string; answer: string; isActive: boolean }>) {
    return prisma.fAQ.update({
      where: { id },
      data,
    });
  }

  async delete(id: string) {
    return prisma.fAQ.delete({ where: { id } });
  }
}
