// import prisma from "../../prisma";
// import { SubscriptionStatus, BankTransferStatus } from "@prisma/client";
// import { EmailService } from "../email/email.service";
// import { BankTransferService } from "../bank-transfer/bank-transfer.service";

// export class BankTransferRenewalService {
//   private emailService: EmailService;
//   private bankTransferService: BankTransferService;

//   constructor() {
//     this.emailService = new EmailService();
//     this.bankTransferService = new BankTransferService();
//   }

//   /**
//    * Check and send renewal reminders for bank transfer subscriptions
//    */
//   async processRenewalReminders() {
//     try {
//       console.log("🔄 Processing bank transfer renewal reminders...");

//       // Get subscriptions expiring in 3, 2, 1 days
//       const threeDaysReminders = await this.getSubscriptionsExpiringInDays(3);
//       const twoDaysReminders = await this.getSubscriptionsExpiringInDays(2);
//       const oneDayReminders = await this.getSubscriptionsExpiringInDays(1);

//       // Send reminder emails
//       await this.sendReminderEmails(threeDaysReminders, 3);
//       await this.sendReminderEmails(twoDaysReminders, 2);
//       await this.sendReminderEmails(oneDayReminders, 1);

//       // Process expired subscriptions
//       await this.processExpiredSubscriptions();

//       console.log("✅ Bank transfer renewal reminders processed successfully");

//       return {
//         threeDaysReminders: threeDaysReminders.length,
//         twoDaysReminders: twoDaysReminders.length,
//         oneDayReminders: oneDayReminders.length,
//         processedAt: new Date(),
//       };
//     } catch (error) {
//       console.error("❌ Error processing renewal reminders:", error);
//       throw new Error("Failed to process renewal reminders");
//     }
//   }

//   /**
//    * Get subscriptions expiring in specific number of days
//    */
//   private async getSubscriptionsExpiringInDays(days: number) {
//     const targetDate = new Date();
//     targetDate.setDate(targetDate.getDate() + days);
//     targetDate.setHours(0, 0, 0, 0);

//     const nextDay = new Date(targetDate);
//     nextDay.setDate(nextDay.getDate() + 1);

//     return await prisma.subscription.findMany({
//       where: {
//         status: SubscriptionStatus.ACTIVE,
//         endDate: {
//           gte: targetDate,
//           lt: nextDay,
//         },
//         // Only bank transfer subscriptions (check if user has bank transfer payments)
//         user: {
//           BankTransferPayment: {
//             some: {
//               status: BankTransferStatus.VERIFIED,
//             },
//           },
//         },
//       },
//       include: {
//         user: {
//           select: {
//             id: true,
//             name: true,
//             lastname: true,
//             email: true,
//           },
//         },
//         package: {
//           select: {
//             id: true,
//             name: true,
//             price: true,
//             currency: true,
//             billingType: true,
//           },
//         },
//         // Get user's latest bank transfer payment for reference
//         // user: {
//         //   include: {
//         //     BankTransferPayment: {
//         //       where: {
//         //         status: BankTransferStatus.VERIFIED,
//         //       },
//         //       orderBy: {
//         //         verifiedAt: "desc",
//         //       },
//         //       take: 1,
//         //     },
//         //   },
//         // },
//       },
//     });
//   }

//   /**
//    * Send renewal reminder emails
//    */
//   private async sendReminderEmails(subscriptions: any[], daysRemaining: number) {
//     for (const subscription of subscriptions) {
//       try {
//         // Check if reminder was already sent today
//         const reminderSent = await this.checkReminderSent(subscription.id, daysRemaining);
//         if (reminderSent) {
//           continue;
//         }

//         await this.emailService.sendEmail({
//           to: subscription.user.email,
//           subject: `Subscription Renewal Reminder - ${daysRemaining} Day${daysRemaining > 1 ? 's' : ''} Remaining`,
//           template: "bank-transfer-renewal-reminder",
//           data: {
//             userName: subscription.user.name,
//             packageName: subscription.package.name,
//             daysRemaining,
//             expirationDate: subscription.endDate.toLocaleDateString(),
//             amount: subscription.package.price,
//             currency: subscription.package.currency,
//             renewalInstructions: this.getRenewalInstructions(),
//             skyDoLink: "https://skydo.com",
//             supportEmail: "<EMAIL>",
//           },
//         });

//         // Log reminder sent
//         await this.logReminderSent(subscription.id, daysRemaining);

//         console.log(`📧 Reminder sent to ${subscription.user.email} (${daysRemaining} days)`);
//       } catch (error) {
//         console.error(`❌ Failed to send reminder to ${subscription.user.email}:`, error);
//       }
//     }
//   }

//   /**
//    * Process expired subscriptions
//    */
//   private async processExpiredSubscriptions() {
//     const expiredDate = new Date();
//     expiredDate.setHours(0, 0, 0, 0);

//     const expiredSubscriptions = await prisma.subscription.findMany({
//       where: {
//         status: SubscriptionStatus.ACTIVE,
//         endDate: {
//           lt: expiredDate,
//         },
//         // Only bank transfer subscriptions
//         user: {
//           BankTransferPayment: {
//             some: {
//               status: BankTransferStatus.VERIFIED,
//             },
//           },
//         },
//       },
//       include: {
//         user: {
//           select: {
//             id: true,
//             name: true,
//             lastname: true,
//             email: true,
//           },
//         },
//         package: {
//           select: {
//             id: true,
//             name: true,
//             price: true,
//             currency: true,
//           },
//         },
//       },
//     });

//     for (const subscription of expiredSubscriptions) {
//       try {
//         // Check if user has made a renewal payment in the last 7 days
//         const renewalPayment = await this.checkForRenewalPayment(subscription.userId, subscription.packageId);

//         if (renewalPayment) {
//           // Auto-renew subscription
//           await this.autoRenewSubscription(subscription, renewalPayment);
//         } else {
//           // Expire subscription
//           await this.expireSubscription(subscription);
//         }
//       } catch (error) {
//         console.error(`❌ Failed to process expired subscription ${subscription.id}:`, error);
//       }
//     }
//   }

//   /**
//    * Check for renewal payment in the last 7 days
//    */
//   private async checkForRenewalPayment(userId: string, packageId: string) {
//     const sevenDaysAgo = new Date();
//     sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

//     return await prisma.bankTransferPayment.findFirst({
//       where: {
//         userId,
//         packageId,
//         status: BankTransferStatus.VERIFIED,
//         verifiedAt: {
//           gte: sevenDaysAgo,
//         },
//         // Ensure this payment is not already linked to an active subscription
//         subscription: {
//           OR: [
//             { status: SubscriptionStatus.EXPIRED },
//             { status: SubscriptionStatus.CANCELLED },
//             // { status: null },
//           ],
//         },
//       },
//       orderBy: {
//         verifiedAt: "desc",
//       },
//     });
//   }

//   /**
//    * Auto-renew subscription
//    */
//   private async autoRenewSubscription(subscription: any, renewalPayment: any) {
//     try {
//       const newStartDate = new Date();
//       const newEndDate = new Date();
//       newEndDate.setMonth(newEndDate.getMonth() + 1); // Extend by 1 month

//       // Update current subscription to expired
//       await prisma.subscription.update({
//         where: { id: subscription.id },
//         data: { status: SubscriptionStatus.EXPIRED },
//       });

//       // Create new subscription
//       const newSubscription = await prisma.subscription.create({
//         data: {
//           userId: subscription.userId,
//           packageId: subscription.packageId,
//           status: SubscriptionStatus.ACTIVE,
//           startDate: newStartDate,
//           endDate: newEndDate,
//         },
//       });

//       // Link renewal payment to new subscription
//       await prisma.bankTransferPayment.update({
//         where: { id: renewalPayment.id },
//         data: { subscriptionId: newSubscription.id },
//       });

//       // Send renewal confirmation email
//       await this.emailService.sendEmail({
//         to: subscription.user.email,
//         subject: "Subscription Renewed Successfully",
//         template: "bank-transfer-renewal-success",
//         data: {
//           userName: subscription.user.name,
//           packageName: subscription.package.name,
//           newStartDate: newStartDate.toLocaleDateString(),
//           newEndDate: newEndDate.toLocaleDateString(),
//           amount: renewalPayment.amount,
//           currency: renewalPayment.currency,
//           transactionId: renewalPayment.transactionId,
//           dashboardLink: `${process.env.FRONTEND_URL}/dashboard`,
//         },
//       });

//       console.log(`🔄 Auto-renewed subscription for ${subscription.user.email}`);

//       return newSubscription;
//     } catch (error) {
//       console.error("❌ Error auto-renewing subscription:", error);
//       throw error;
//     }
//   }

//   /**
//    * Expire subscription
//    */
//   private async expireSubscription(subscription: any) {
//     try {
//       // Update subscription status to expired
//       await prisma.subscription.update({
//         where: { id: subscription.id },
//         data: { status: SubscriptionStatus.EXPIRED },
//       });

//       // Send expiration notification email
//       await this.emailService.sendEmail({
//         to: subscription.user.email,
//         subject: "Subscription Expired",
//         template: "bank-transfer-subscription-expired",
//         data: {
//           userName: subscription.user.name,
//           packageName: subscription.package.name,
//           expiredDate: subscription.endDate.toLocaleDateString(),
//           renewalInstructions: this.getRenewalInstructions(),
//           skyDoLink: "https://skydo.com",
//           supportEmail: "<EMAIL>",
//           dashboardLink: `${process.env.FRONTEND_URL}/dashboard`,
//         },
//       });

//       console.log(`⏰ Expired subscription for ${subscription.user.email}`);
//     } catch (error) {
//       console.error("❌ Error expiring subscription:", error);
//       throw error;
//     }
//   }

//   /**
//    * Check if reminder was already sent today
//    */
//   private async checkReminderSent(subscriptionId: string, daysRemaining: number) {
//     const today = new Date();
//     today.setHours(0, 0, 0, 0);
//     const tomorrow = new Date(today);
//     tomorrow.setDate(tomorrow.getDate() + 1);

//     const reminder = await prisma.subscriptionReminderLog.findFirst({
//       where: {
//         subscriptionId,
//         daysRemaining,
//         sentAt: {
//           gte: today,
//           lt: tomorrow,
//         },
//       },
//     });

//     return !!reminder;
//   }

//   /**
//    * Log reminder sent
//    */
//   private async logReminderSent(subscriptionId: string, daysRemaining: number) {
//     await prisma.subscriptionReminderLog.create({
//       data: {
//         subscriptionId,
//         daysRemaining,
//         sentAt: new Date(),
//       },
//     });
//   }

//   /**
//    * Get renewal instructions
//    */
//   private getRenewalInstructions() {
//     return [
//       "Visit skydo.com to get bank transfer details for your country",
//       "Complete the payment using the provided bank details",
//       "Submit your payment details through our website with transaction ID and screenshot",
//       "Your subscription will be automatically renewed once payment is verified",
//     ];
//   }

//   /**
//    * Get renewal statistics for admin dashboard
//    */
//   async getRenewalStatistics() {
//     try {
//       const today = new Date();
//       const threeDaysFromNow = new Date();
//       threeDaysFromNow.setDate(today.getDate() + 3);

//       const [
//         expiringIn3Days,
//         expiringIn2Days,
//         expiringIn1Day,
//         expiredToday,
//         autoRenewedThisMonth,
//         expiredThisMonth,
//       ] = await Promise.all([
//         this.getSubscriptionsExpiringInDays(3),
//         this.getSubscriptionsExpiringInDays(2),
//         this.getSubscriptionsExpiringInDays(1),
//         this.getExpiredSubscriptionsToday(),
//         this.getAutoRenewedSubscriptionsThisMonth(),
//         this.getExpiredSubscriptionsThisMonth(),
//       ]);

//       return {
//         expiringIn3Days: expiringIn3Days.length,
//         expiringIn2Days: expiringIn2Days.length,
//         expiringIn1Day: expiringIn1Day.length,
//         expiredToday: expiredToday.length,
//         autoRenewedThisMonth: autoRenewedThisMonth.length,
//         expiredThisMonth: expiredThisMonth.length,
//         lastUpdated: new Date(),
//       };
//     } catch (error) {
//       console.error("❌ Error getting renewal statistics:", error);
//       throw new Error("Failed to get renewal statistics");
//     }
//   }

//   private async getExpiredSubscriptionsToday() {
//     const today = new Date();
//     today.setHours(0, 0, 0, 0);
//     const tomorrow = new Date(today);
//     tomorrow.setDate(tomorrow.getDate() + 1);

//     return await prisma.subscription.findMany({
//       where: {
//         status: SubscriptionStatus.EXPIRED,
//         updatedAt: {
//           gte: today,
//           lt: tomorrow,
//         },
//       },
//     });
//   }

//   private async getAutoRenewedSubscriptionsThisMonth() {
//     const startOfMonth = new Date();
//     startOfMonth.setDate(1);
//     startOfMonth.setHours(0, 0, 0, 0);

//     return await prisma.subscription.findMany({
//       where: {
//         status: SubscriptionStatus.ACTIVE,
//         createdAt: {
//           gte: startOfMonth,
//         },
//         // Check if this is a renewal (user has previous expired subscription)
//         user: {
//           Subscription: {
//             some: {
//               status: SubscriptionStatus.EXPIRED,
//               updatedAt: {
//                 gte: startOfMonth,
//               },
//             },
//           },
//         },
//       },
//     });
//   }

//   private async getExpiredSubscriptionsThisMonth() {
//     const startOfMonth = new Date();
//     startOfMonth.setDate(1);
//     startOfMonth.setHours(0, 0, 0, 0);

//     return await prisma.subscription.findMany({
//       where: {
//         status: SubscriptionStatus.EXPIRED,
//         updatedAt: {
//           gte: startOfMonth,
//         },
//       },
//     });
//   }
// }