import { Request, Response } from "express";
import { AuthenticatedRequest } from "../../middlewares/checkAuth";
import { NotificationService } from "../../services/notification/notification.service";

export class NotificationController {
  private notificationService = new NotificationService();

  async getNotifications(req: Request, res: Response) {
    try {
      const { page, limit, unreadOnly, search } = req.query;

      const query = {
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        unreadOnly: unreadOnly === "true",
        search: search ? (search as string) : undefined,
      };

      const userId = (req as AuthenticatedRequest).user!.userId;
      const result = await this.notificationService.getNotifications(userId, query);

      res.status(200).json({
        data: result,
        message: "Notifications fetched successfully",
      });
    } catch (error) {
      res.status(500).json({
        message: "Failed to fetch notifications",
        error: error instanceof Error ? error.message : error,
      });
    }
  }

  async markNotificationRead(req: Request, res: Response) {
    try {
      const { notificationId } = req.params;
      const userId = (req as AuthenticatedRequest).user!.userId;

      await this.notificationService.markNotificationRead(notificationId, userId);

      res.status(200).json({
        message: "Notification marked as read successfully",
      });
    } catch (error) {
      res.status(500).json({
        message: "Failed to mark notification as read",
        error: error instanceof Error ? error.message : error,
      });
    }
  }
}
