import { dodoAxios } from "../../lib/axiosDodo";
import prisma from "../../prisma";

class OnboardingService {
  // 1. Create Customer in Dodo
  async createDodoCustomer({ email, name }: { email: string; name: string }) {
    const response = await dodoAxios.post("/customers", { email, name });
    return response.data; // e.g., { id: 'cus_123', ... }
  }
  async getDodoCustomer({ customerId }: { customerId: string }) {
    const response = await dodoAxios.get(`/customers/${customerId}`);
    return response.data; // e.g., { id: 'cus_123', email: '
    // '
  }
  // 1.1 Get All Customers
  async getAllDodoCustomers() {
    const response = await dodoAxios.get("/customers");
    return response.data; // e.g., [{ id: 'cus_123', email: '
    // ', name: '<PERSON>' }, ...]
  }
  // 1.2 Update Customer
  async updateDodoCustomer({
    customerId,
    email,
    name,
  }: {
    customerId: string;
    email: string;
    name: string;
  }) {
    const response = await dodoAxios.put(`/customers/${customerId}`, {
      email,
      name,
    });
    return response.data; // e.g., { id: 'cus_123', email: '
    // ', name: 'John Doe' }
  }

  // 2. Create Product in Dodo
  async createDodoProduct({ name, price }: { name: string; price: number }) {
    const response = await dodoAxios.post("/products", { name, price });
    return response.data; // e.g., { id: 'prod_123' }
  }

  async getDodoProduct({ productId }: { productId: string }) {
    const response = await dodoAxios.get(`/products/${productId}`);
    return response.data; // e.g., { id: 'prod_123', name: 'Product Name', price: 100 }
  }
  // 2.1 Get All Products
  async getAllDodoProducts() {
    const response = await dodoAxios.get("/products");
    return response.data; // e.g., [{ id: 'prod_123', name: 'Product Name', price: 100 }, ...]
  }
  // 2.2 Update Product
  async updateDodoProduct({
    productId,
    name,
    price,
  }: {
    productId: string;
    name: string;
    price: number;
  }) {
    const response = await dodoAxios.patch(`/products/${productId}`, {
      name,
      price,
    });
    return response.data; // e.g., { id: 'prod_123', name: 'Product Name', price: 100 }
  }

  // 3. Create Subscription
  async createDodoSubscription({
    customerId,
    productId,
  }: {
    customerId: string;
    productId: string;
  }) {
    const response = await dodoAxios.post("/subscriptions", {
      customer: customerId,
      product: productId,
    });
    return response.data;
  }

  // 4. Apply Discount
  async applyDodoDiscount({
    subscriptionId,
    discountCode,
  }: {
    subscriptionId: string;
    discountCode: string;
  }) {
    const response = await dodoAxios.post(
      `/subscriptions/${subscriptionId}/apply-discount`,
      {
        code: discountCode,
      }
    );
    return response.data;
  }

  async createDodoPayment({
    customerId,
    amount,
  }: {
    customerId: string;
    amount: number;
  }) {
    const response = await dodoAxios.post("/payments", {
      customer: customerId,
      amount,
    });
    return response.data; // e.g., { id: 'pay_123', status: 'PENDING' }
  }

  // 5. Get Payment Status
  async getDodoPaymentStatus(paymentId: string) {
    const response = await dodoAxios.get(`/payments/${paymentId}`);
    return response.data; // e.g., { status: 'SUCCESS' }
  }
}
