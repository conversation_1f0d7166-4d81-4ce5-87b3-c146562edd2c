import { Request, Response } from "express";
import { PackageService } from "../../services/package/package.service";
import * as ApiResponse from "../../helper/apiResponse";
import { JwtPayload } from "jsonwebtoken";

interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}

interface QueryParams {
  page: number;
  limit: number;
  search: string;
}

export class PackageController {
  private packageService = new PackageService();
  async create(req: AuthenticatedRequest, res: Response) {
    const response = await this.packageService.createPackage(req.body);
    return ApiResponse.successResponseWithData(
      res,
      "Task created successfully",
      response
    );
  }
  async getAll(req: AuthenticatedRequest, res: Response) {
    const { page = 1, limit = 10, search } = req.query;
    const query: QueryParams = {
      page: parseInt(page as string, 10) || 1,
      limit: parseInt(limit as string, 10) || 10,
      search: (search as string) || "",
    };
    const response = await this.packageService.getAllPackages(query);
    return ApiResponse.successResponseWithData(
      res,
      "Task fetched successfully",
      response
    );
  }

  async getById(req: AuthenticatedRequest, res: Response) {
    const response = await this.packageService.getPackageById(req.params.id);
    return ApiResponse.successResponseWithData(
      res,
      "Task fetched successfully",
      response
    );
  }

  async update(req: AuthenticatedRequest, res: Response) {
    const response = await this.packageService.updatePackage(
      req.params.id,
      req.body
    );
    return ApiResponse.successResponseWithData(
      res,
      "Task updated successfully",
      response
    );
  }
  async delete(req: AuthenticatedRequest, res: Response) {
    const response = await this.packageService.deletePackage(req.params.id);
    return ApiResponse.successResponseWithData(
      res,
      "Task deleted successfully",
      response
    );
  }

  async getAllActivePackages(req: AuthenticatedRequest, res: Response) {
    const { page = 1, limit = 10, search } = req.query;
    const query: QueryParams = {
      page: parseInt(page as string, 10) || 1,
      limit: parseInt(limit as string, 10) || 10,
      search: (search as string) || "",
    };
    const response = await this.packageService.getAllActivePackages(query);
    return ApiResponse.successResponseWithData(
      res,
      "Task fetched successfully",
      response
    );
  }
}
