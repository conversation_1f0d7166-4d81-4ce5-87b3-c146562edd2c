import { AppError } from "../../utils/ApiError";
import zohoDeskClient from "../../lib/axiosZohoDesk";

export interface ZohoTicket {
  id?: string;
  subject: string;
  description: string;
  priority: "Low" | "Medium" | "High";
  status: "Open" | "In Progress" | "On Hold" | "Escalated" | "Closed";
  departmentId?: string;
  contactId?: string;
  assigneeId?: string;
  category?: string;
  subCategory?: string;
  tags?: string[];
  customFields?: Record<string, any>;
  attachments?: any[];
  // Additional fields from the curl command
  entitySkills?: string[];
  productId?: string;
  dueDate?: string;
  channel?: string;
  language?: string;
  classification?: string;
  phone?: string;
  email?: string;
  cf?: {
    cf_permanentaddress?: string | null;
    cf_dateofpurchase?: string | null;
    cf_phone?: string | null;
    cf_numberofitems?: string | null;
    cf_url?: string | null;
    cf_secondaryemail?: string | null;
    cf_severitypercentage?: string | null;
    cf_modelname?: string | null;
    [key: string]: any;
  };
  
  // Additional fields that might be required by Zoho Desk API
  ticketNumber?: string;
  isSpam?: boolean;
  isRead?: boolean;
  isDeleted?: boolean;
  isResponseRequired?: boolean;
  isTrashed?: boolean;
  source?: string;
  threadCount?: number;
  threadIds?: string[];
  responseDueDate?: string;
  closedTime?: string;
  approvalCount?: number;
  timeEntryCount?: number;
  taskCount?: number;
  accountId?: string;
  layoutName?: string;
  webUrl?: string;
  channelRelatedInfo?: any;
  customerResponseTime?: any;
  productName?: string;
  departmentName?: string;
  teamId?: string;
  teamName?: string;
  layoutId?: string;
  contactName?: string;
  assigneeName?: string;
  sharedDepartments?: string[];
  commentCount?: number;
  attachmentCount?: number;
  contractId?: string;
  contractName?: string;
  resolutionDueDate?: string;
  escalatedTime?: string;
  isOverDue?: boolean;
  isFcr?: boolean;
  isEscalated?: boolean;
  slaId?: string;
  slaName?: string;
  team?: any;
  secondaryContacts?: any[];
  secondaryContactIds?: string[];
  productFields?: any;
  createdTime?: string;
  modifiedTime?: string;
  createdTimeFormat?: string;
  modifiedTimeFormat?: string;
  createdBy?: any;
  modifiedBy?: any;
}

export interface ZohoContact {
  id?: string;
  firstName: string;
  lastName?: string;
  email: string;
  phone?: string;
  mobile?: string;
  description?: string;
}

export class ZohoDeskService {
  constructor() {
    // No need for manual setup - using interceptor-based client
  }

  // ============ TICKET OPERATIONS ============

  async createTicket(ticketData: ZohoTicket): Promise<any> {
    try {
      const response = await zohoDeskClient.post("/tickets", ticketData);
      return response.data;
    } catch (error: any) {
      console.error("Zoho Desk API Error:", {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });
      
      // Extract more detailed error information if available
      const errorDetails = error.response?.data?.errorDetails || 
                          error.response?.data?.errors || 
                          error.response?.data?.message || 
                          error.message;
      
      throw new AppError(
        `Failed to create ticket: ${JSON.stringify(errorDetails)}`,
        error.response?.status || 500
      );
    }
  }

  async getTicket(ticketId: string): Promise<any> {
    try {
      const response = await zohoDeskClient.get(`/tickets/${ticketId}`);
      return response.data;
    } catch (error: any) {
      throw new AppError(
        `Failed to get ticket: ${
          error.response?.data?.message || error.message
        }`,
        error.response?.status || 500
      );
    }
  }

  async updateTicket(
    ticketId: string,
    updateData: Partial<ZohoTicket>
  ): Promise<any> {
    try {
      const response = await zohoDeskClient.patch(
        `/tickets/${ticketId}`,
        updateData
      );
      return response.data;
    } catch (error: any) {
      throw new AppError(
        `Failed to update ticket: ${
          error.response?.data?.message || error.message
        }`,
        error.response?.status || 500
      );
    }
  }

  async deleteTicket(ticketId: string): Promise<any> {
    try {
      const response = await zohoDeskClient.delete(`/tickets/${ticketId}`);
      return response.data;
    } catch (error: any) {
      throw new AppError(
        `Failed to delete ticket: ${
          error.response?.data?.message || error.message
        }`,
        error.response?.status || 500
      );
    }
  }

  async listTickets(params?: {
    limit?: number;
    from?: number;
    departmentId?: string;
    status?: string;
    priority?: string;
    assigneeId?: string;
    contactId?: string;
    sortBy?: string;
  }): Promise<any> {
    try {
      const response = await zohoDeskClient.get("/tickets", { params });
      return response.data;
    } catch (error: any) {
      throw new AppError(
        `Failed to list tickets: ${
          error.response?.data?.message || error.message
        }`,

        error.response?.status || 500
      );
    }
  }

  // ============ CONTACT OPERATIONS ============

  async createContact(contactData: ZohoContact): Promise<any> {
    try {
      const response = await zohoDeskClient.post("/contacts", contactData);
      return response.data;
    } catch (error: any) {
      throw new AppError(
        `Failed to create contact: ${
          error.response?.data?.message || error.message
        }`,

        error.response?.status || 500
      );
    }
  }

  async getContact(contactId: string): Promise<any> {
    try {
      const response = await zohoDeskClient.get(`/contacts/${contactId}`);
      return response.data;
    } catch (error: any) {
      throw new AppError(
        `Failed to get contact: ${
          error.response?.data?.message || error.message
        }`,

        error.response?.status || 500
      );
    }
  }

  async searchContacts(email: string): Promise<any> {
    try {
      const response = await zohoDeskClient.get("/contacts/search", {
        params: { email },
      });
      return response.data;
    } catch (error: any) {
      throw new AppError(
        `Failed to search contacts: ${
          error.response?.data?.message || error.message
        }`,

        error.response?.status || 500
      );
    }
  }

  // ============ DEPARTMENT OPERATIONS ============

  async getDepartments(): Promise<any> {
    try {
      const response = await zohoDeskClient.get("/departments");
      return response.data;
    } catch (error: any) {
      throw new AppError(
        `Failed to get departments: ${
          error.response?.data?.message || error.message
        }`,

        error.response?.status || 500
      );
    }
  }

  // ============ AGENT OPERATIONS ============

  async getAgents(): Promise<any> {
    try {
      const response = await zohoDeskClient.get("/agents");
      return response.data;
    } catch (error: any) {
      throw new AppError(
        `Failed to get agents: ${
          error.response?.data?.message || error.message
        }`,

        error.response?.status || 500
      );
    }
  }

  // ============ TICKET COMMENTS ============

  async addTicketComment(
    ticketId: string,
    comment: {
      content: string;
      contentType?: "html" | "plainText";
      isPublic?: boolean;
    }
  ): Promise<any> {
    try {
      const response = await zohoDeskClient.post(
        `/tickets/${ticketId}/comments`,
        comment
      );
      return response.data;
    } catch (error: any) {
      throw new AppError(
        `Failed to add comment: ${
          error.response?.data?.message || error.message
        }`,

        error.response?.status || 500
      );
    }
  }

  async getTicketComments(ticketId: string): Promise<any> {
    try {
      const response = await zohoDeskClient.get(
        `/tickets/${ticketId}/comments`
      );
      return response.data;
    } catch (error: any) {
      throw new AppError(
        `Failed to get comments: ${
          error.response?.data?.message || error.message
        }`,

        error.response?.status || 500
      );
    }
  }

  // ============ ATTACHMENTS ============

  async uploadAttachment(ticketId: string, file: any): Promise<any> {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await zohoDeskClient.post(
        `/tickets/${ticketId}/attachments`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      return response.data;
    } catch (error: any) {
      throw new AppError(
        `Failed to upload attachment: ${
          error.response?.data?.message || error.message
        }`,

        error.response?.status || 500
      );
    }
  }
}
