#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Email Template Development Workflow\n');

// Check if development server is running
function checkServerRunning() {
  try {
    execSync('curl -s http://localhost:3000/api/email-preview > /dev/null', { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

// Display workflow options
function showWorkflowOptions() {
  console.log('Choose your workflow:');
  console.log('1. 📋 List all templates');
  console.log('2. 👁️  Preview specific template');
  console.log('3. 🧪 Test template rendering');
  console.log('4. 📊 Validate all templates');
  console.log('5. 🆕 Create new template');
  console.log('6. 🌐 Open preview dashboard');
  console.log('7. 📧 Send test email');
  console.log('8. ❓ Show template info');
  console.log('0. 🚪 Exit\n');
}

// Execute workflow choice
function executeWorkflow(choice) {
  try {
    switch (choice) {
      case '1':
        console.log('📋 Listing all templates...\n');
        execSync('npm run email-cli list', { stdio: 'inherit' });
        break;
        
      case '2':
        const templateName = prompt('Enter template name: ');
        if (templateName) {
          console.log(`👁️  Previewing template: ${templateName}\n`);
          execSync(`npm run email-cli preview ${templateName}`, { stdio: 'inherit' });
        }
        break;
        
      case '3':
        const testTemplate = prompt('Enter template name to test: ');
        if (testTemplate) {
          console.log(`🧪 Testing template: ${testTemplate}\n`);
          execSync(`npm run email-cli test ${testTemplate}`, { stdio: 'inherit' });
        }
        break;
        
      case '4':
        console.log('📊 Validating all templates...\n');
        execSync('npm run email-cli validate', { stdio: 'inherit' });
        break;
        
      case '5':
        const newTemplateName = prompt('Enter new template name: ');
        const category = prompt('Enter category (optional): ') || 'General';
        const description = prompt('Enter description (optional): ') || '';
        
        if (newTemplateName) {
          console.log(`🆕 Creating template: ${newTemplateName}\n`);
          const cmd = `npm run email-cli create ${newTemplateName} --category "${category}" --description "${description}"`;
          execSync(cmd, { stdio: 'inherit' });
        }
        break;
        
      case '6':
        if (checkServerRunning()) {
          console.log('🌐 Opening preview dashboard...');
          console.log('Dashboard URL: http://localhost:3000/api/email-preview');
          
          // Try to open in browser (works on macOS, Linux, Windows)
          try {
            const platform = process.platform;
            if (platform === 'darwin') {
              execSync('open http://localhost:3000/api/email-preview');
            } else if (platform === 'win32') {
              execSync('start http://localhost:3000/api/email-preview');
            } else {
              execSync('xdg-open http://localhost:3000/api/email-preview');
            }
          } catch {
            console.log('Please open the URL manually in your browser.');
          }
        } else {
          console.log('❌ Development server is not running.');
          console.log('Start it with: npm run dev');
        }
        break;
        
      case '7':
        const emailTemplate = prompt('Enter template name: ');
        const emailAddress = prompt('Enter email address: ');
        
        if (emailTemplate && emailAddress) {
          console.log(`📧 This would send test email for ${emailTemplate} to ${emailAddress}`);
          console.log('Use the web dashboard or API to send test emails.');
        }
        break;
        
      case '8':
        const infoTemplate = prompt('Enter template name: ');
        if (infoTemplate) {
          console.log(`❓ Getting info for template: ${infoTemplate}\n`);
          execSync(`npm run email-cli info ${infoTemplate}`, { stdio: 'inherit' });
        }
        break;
        
      case '0':
        console.log('👋 Goodbye!');
        process.exit(0);
        break;
        
      default:
        console.log('❌ Invalid choice. Please try again.');
    }
  } catch (error) {
    console.error('❌ Error executing workflow:', error.message);
  }
}

// Simple prompt function for Node.js
function prompt(question) {
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  return new Promise((resolve) => {
    readline.question(question, (answer) => {
      readline.close();
      resolve(answer.trim());
    });
  });
}

// Main workflow loop
async function runWorkflow() {
  // Check if server is running
  const serverRunning = checkServerRunning();
  if (serverRunning) {
    console.log('✅ Development server is running');
  } else {
    console.log('⚠️  Development server is not running');
    console.log('   Start it with: npm run dev\n');
  }

  while (true) {
    showWorkflowOptions();
    
    const choice = await prompt('Enter your choice (0-8): ');
    console.log('');
    
    executeWorkflow(choice);
    
    if (choice !== '0') {
      console.log('\n' + '='.repeat(50) + '\n');
    }
  }
}

// Run the workflow
runWorkflow().catch(console.error);