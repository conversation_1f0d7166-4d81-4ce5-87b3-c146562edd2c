-- Add email analytics table
CREATE TABLE IF NOT EXISTS "EmailLog" (
    "id" TEXT NOT NULL,
    "to" TEXT NOT NULL,
    "subject" TEXT NOT NULL,
    "template" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'SENT',
    "sentAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deliveredAt" TIMESTAMP(3),
    "openedAt" TIMESTAMP(3),
    "clickedAt" TIMESTAMP(3),
    "failureReason" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EmailLog_pkey" PRIMARY KEY ("id")
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS "EmailLog_to_idx" ON "EmailLog"("to");
CREATE INDEX IF NOT EXISTS "EmailLog_template_idx" ON "EmailLog"("template");
CREATE INDEX IF NOT EXISTS "EmailLog_status_idx" ON "EmailLog"("status");
CREATE INDEX IF NOT EXISTS "EmailLog_sentAt_idx" ON "EmailLog"("sentAt");
CREATE INDEX IF NOT EXISTS "EmailLog_template_sentAt_idx" ON "EmailLog"("template", "sentAt");

-- Add email preferences table for users
CREATE TABLE IF NOT EXISTS "EmailPreferences" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "emailType" TEXT NOT NULL,
    "enabled" BOOLEAN NOT NULL DEFAULT true,
    "frequency" TEXT DEFAULT 'IMMEDIATE',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EmailPreferences_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint for user email preferences
CREATE UNIQUE INDEX IF NOT EXISTS "EmailPreferences_userId_emailType_key" ON "EmailPreferences"("userId", "emailType");

-- Add foreign key constraint if User table exists
-- ALTER TABLE "EmailPreferences" ADD CONSTRAINT "EmailPreferences_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add email queue table
CREATE TABLE IF NOT EXISTS "EmailQueue" (
    "id" TEXT NOT NULL,
    "emailData" JSONB NOT NULL,
    "priority" TEXT NOT NULL DEFAULT 'NORMAL',
    "scheduledFor" TIMESTAMP(3),
    "attempts" INTEGER NOT NULL DEFAULT 0,
    "maxAttempts" INTEGER NOT NULL DEFAULT 3,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "error" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EmailQueue_pkey" PRIMARY KEY ("id")
);

-- Create indexes for email queue
CREATE INDEX IF NOT EXISTS "EmailQueue_status_idx" ON "EmailQueue"("status");
CREATE INDEX IF NOT EXISTS "EmailQueue_priority_idx" ON "EmailQueue"("priority");
CREATE INDEX IF NOT EXISTS "EmailQueue_scheduledFor_idx" ON "EmailQueue"("scheduledFor");
CREATE INDEX IF NOT EXISTS "EmailQueue_status_priority_createdAt_idx" ON "EmailQueue"("status", "priority", "createdAt");
CREATE INDEX IF NOT EXISTS "EmailQueue_createdAt_idx" ON "EmailQueue"("createdAt");
CREATE INDEX IF NOT EXISTS "EmailQueue_updatedAt_idx" ON "EmailQueue"("updatedAt");