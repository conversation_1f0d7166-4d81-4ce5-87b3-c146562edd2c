import { Request, Response, NextFunction } from "express";
import * as ApiResponse from "../../helper/apiResponse";
import { ZohoDeskService, ZohoTicket, ZohoContact } from "../../services/zoho/zoho-desk.service";
// import { ZohoSSOService } from "../../services/zoho/zoho-sso.service";
import { asyncHandler } from "../../middlewares/asyncHandler";
import { AppError } from "../../utils/ApiError";

export class ZohoDeskController {
  private zohoDeskService: ZohoDeskService;
  // private zohoSSOService: ZohoSSOService;

  constructor() {
    this.zohoDeskService = new ZohoDeskService();
    // this.zohoSSOService = new ZohoSSOService();
  }

  // ============ AUTHENTICATION ============

  async initiateDeskAuth(req: Request, res: Response, next: NextFunction) {
    try {
      const clientId = process.env.ZOHO_DESK_CLIENT_ID!;
      const redirectUri = process.env.ZOHO_DESK_REDIRECT_URI!;
      const scope = [
        "Desk.tickets.ALL",
        "Desk.contacts.ALL",
        "Desk.basic.READ",
        "Desk.settings.READ",
        "Desk.search.READ"
      ].join(",");

      const oauthUrl = `https://accounts.zoho.in/oauth/v2/auth?scope=${scope}&client_id=${clientId}&response_type=code&access_type=offline&redirect_uri=${redirectUri}&state=-5466400890088961855`;

      ApiResponse.successResponseWithData(res, "Zoho Desk OAuth URL generated", {
        oauthUrl
      });
    } catch (error) {
      next(error);
    }
  }

  async handleDeskCallback(req: Request, res: Response, next: NextFunction) {
    try {
      const { code } = req.query;

      if (!code) {
        throw new AppError("Authorization code is required", 400);
      }

      console.log(code, "coede")

      // Exchange code for tokens
      const tokenData = await this.exchangeCodeForTokens(code as string);

      // Store tokens in database (you'll need to implement this based on your schema)
      // await this.storeTokens(tokenData);
      console.log(tokenData)

      ApiResponse.successResponseWithData(res, "Zoho Desk authentication successful", {
        accessToken: tokenData.access_token,
        refresToken: tokenData?.refresh_token,
        expiresIn: tokenData.expires_in
      });
    } catch (error) {
      next(error);
    }
  }

  private async exchangeCodeForTokens(code: string) {
    const { ZOHO_DESK_CLIENT_ID, ZOHO_DESK_CLIENT_SECRET, ZOHO_DESK_REDIRECT_URI } = process.env;

    const tokenUrl = "https://accounts.zoho.com/oauth/v2/token";
    const params = new URLSearchParams({
      grant_type: "authorization_code",
      client_id: ZOHO_DESK_CLIENT_ID!,
      client_secret: ZOHO_DESK_CLIENT_SECRET!,
      redirect_uri: ZOHO_DESK_REDIRECT_URI!,
      code: code
    });

    const response = await fetch(tokenUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: params
    });

    console.log(response)

    if (!response.ok) {
      throw new AppError("Failed to exchange code for tokens", 400);
    }

    return await response.json();
  }

  // ============ TICKET OPERATIONS ============

  async createTicket(req: Request, res: Response, next: NextFunction) {
    try {
      // Validate required fields
      if (!req.body.subject) {
        throw new AppError("Subject is required", 400);
      }
      if (!req.body.description) {
        throw new AppError("Description is required", 400);
      }

      // Instead of creating a new object, use the request body directly
      // This ensures we preserve all fields exactly as they are sent from the client
      const ticketData = { ...req.body };

      // Set default values only if not provided
      if (!ticketData.priority) ticketData.priority = 'Medium';
      if (!ticketData.status) ticketData.status = 'Open';

      console.log("Creating ticket with data:", JSON.stringify(ticketData, null, 2));

      const result = await this.zohoDeskService.createTicket(ticketData);

      // Store ticket in local database if needed
      // await this.storeTicketLocally(result, req.user?.id);

      ApiResponse.successResponseWithData(res, "Ticket created successfully", result);
    } catch (error) {
      next(error);
    }
  }

  async getTicket(req: Request, res: Response, next: NextFunction) {
    try {
      const { ticketId } = req.params;

      const ticket = await this.zohoDeskService.getTicket(ticketId);

      ApiResponse.successResponseWithData(res, "Ticket retrieved successfully", ticket);
    } catch (error) {
      next(error);
    }
  }

  async updateTicket(req: Request, res: Response, next: NextFunction) {
    try {
      const { ticketId } = req.params;

      const updateData = {
        subject: req.body.subject,
        description: req.body.description,
        priority: req.body.priority,
        status: req.body.status,
        assigneeId: req.body.assigneeId,
        category: req.body.category,
        subCategory: req.body.subCategory,
        tags: req.body.tags,
        customFields: req.body.customFields
      };

      // Remove undefined fields
      Object.keys(updateData).forEach(key =>
        updateData[key as keyof typeof updateData] === undefined && delete updateData[key as keyof typeof updateData]
      );

      const result = await this.zohoDeskService.updateTicket(ticketId, updateData);

      ApiResponse.successResponseWithData(res, "Ticket updated successfully", result);
    } catch (error) {
      next(error);
    }
  }

  async deleteTicket(req: Request, res: Response, next: NextFunction) {
    try {
      const { ticketId } = req.params;

      await this.zohoDeskService.deleteTicket(ticketId);

      ApiResponse.successResponse(res, "Ticket deleted successfully");
    } catch (error) {
      next(error);
    }
  }

  async listTickets(req: Request, res: Response, next: NextFunction) {
    try {
      const params = {
        limit: req.query.limit ? parseInt(req.query.limit as string) : 50,
        from: req.query.from ? parseInt(req.query.from as string) : 0,
        departmentId: req.query.departmentId as string,
        status: req.query.status as string,
        priority: req.query.priority as string,
        assigneeId: req.query.assigneeId as string,
        contactId: req.query.contactId as string,
        sortBy: req.query.sortBy as string
      };

      // Remove undefined params
      Object.keys(params).forEach(key =>
        params[key as keyof typeof params] === undefined && delete params[key as keyof typeof params]
      );

      const tickets = await this.zohoDeskService.listTickets(params);

      ApiResponse.successResponseWithData(res, "Tickets retrieved successfully", tickets);
    } catch (error) {
      next(error);
    }
  }

  // ============ CONTACT OPERATIONS ============

  async createContact(req: Request, res: Response, next: NextFunction) {
    try {
      const contactData: ZohoContact = {
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        email: req.body.email,
        phone: req.body.phone,
        mobile: req.body.mobile,
        description: req.body.description
      };

      const result = await this.zohoDeskService.createContact(contactData);

      ApiResponse.successResponseWithData(res, "Contact created successfully", result);
    } catch (error) {
      next(error);
    }
  }

  async getContact(req: Request, res: Response, next: NextFunction) {
    try {
      const { contactId } = req.params;

      const contact = await this.zohoDeskService.getContact(contactId);

      ApiResponse.successResponseWithData(res, "Contact retrieved successfully", contact);
    } catch (error) {
      next(error);
    }
  }

  async searchContacts(req: Request, res: Response, next: NextFunction) {
    try {
      const { email } = req.query;

      if (!email) {
        throw new AppError("Email parameter is required for contact search", 400);
      }

      const contacts = await this.zohoDeskService.searchContacts(email as string);

      ApiResponse.successResponseWithData(res, "Contacts found", contacts);
    } catch (error) {
      next(error);
    }
  }

  // ============ UTILITY OPERATIONS ============

  async getDepartments(req: Request, res: Response, next: NextFunction) {
    try {
      const departments = await this.zohoDeskService.getDepartments();

      ApiResponse.successResponseWithData(res, "Departments retrieved successfully", departments);
    } catch (error) {
      next(error);
    }
  }

  async getAgents(req: Request, res: Response, next: NextFunction) {
    try {
      const agents = await this.zohoDeskService.getAgents();

      ApiResponse.successResponseWithData(res, "Agents retrieved successfully", agents);
    } catch (error) {
      next(error);
    }
  }

  // ============ TICKET COMMENTS ============

  async addTicketComment(req: Request, res: Response, next: NextFunction) {
    try {
      const { ticketId } = req.params;

      const commentData = {
        content: req.body.content,
        contentType: req.body.contentType || 'plainText',
        isPublic: req.body.isPublic !== undefined ? req.body.isPublic : true
      };

      const result = await this.zohoDeskService.addTicketComment(ticketId, commentData);

      ApiResponse.successResponseWithData(res, "Comment added successfully", result);
    } catch (error) {
      next(error);
    }
  }

  async getTicketComments(req: Request, res: Response, next: NextFunction) {
    try {
      const { ticketId } = req.params;

      const comments = await this.zohoDeskService.getTicketComments(ticketId);

      ApiResponse.successResponseWithData(res, "Comments retrieved successfully", comments);
    } catch (error) {
      next(error);
    }
  }

  // ============ ATTACHMENTS ============

  async uploadAttachment(req: Request, res: Response, next: NextFunction) {
    try {
      const { ticketId } = req.params;

      if (!req.file) {
        throw new AppError("File is required", 400);
      }

      const result = await this.zohoDeskService.uploadAttachment(ticketId, req.file);

      ApiResponse.successResponseWithData(res, "Attachment uploaded successfully", result);
    } catch (error) {
      next(error);
    }
  }

  // ============ SSO OPERATIONS ============

  /**
   * Generate an SSO URL for the authenticated user to access Zoho Desk
   */
  // async generateSSOUrl(req: Request, res: Response, next: NextFunction) {
  //   try {
  //     // Ensure user is authenticated
  //     if (!req.user) {
  //       throw new AppError("Authentication required", 401);
  //     }

  //     const { redirectTo } = req.query;

  //     // Generate SSO URL
  //     const ssoUrl = this.zohoSSOService.generateSSOUrl(
  //       {
  //         id: req.user.id,
  //         email: req.user.email,
  //         name: req.user.name || req.user.firstName || '',
  //         lastname: req.user.lastname || req.user.lastName || ''
  //       },
  //       redirectTo as string | undefined
  //     );

  //     ApiResponse.successResponseWithData(res, "SSO URL generated successfully", { ssoUrl });
  //   } catch (error) {
  //     next(error);
  //   }
  // }
}

