import { Router } from "express";
import { AssignmentController } from "../../controllers/assignment/assignment.controller";
import { asyncHand<PERSON> } from "../../middlewares/asyncHandler";
import authMiddleware from "../../middlewares/checkAuth";
import { hasRole } from "../../middlewares/checkRole";

const router = Router();

const controller = new AssignmentController();

router.get(
  "/get",
  authMiddleware,
  hasRole("ADMIN"),
  async<PERSON>and<PERSON>(controller.getClientsWithPlans.bind(controller))
);
router.patch(
  "/assign/annotator/:id",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(controller.assignDeveloper.bind(controller))
);

router
  .route("/details/:subscriptionId")
  .get(
    // authMiddleware,
    // hasRole("ADMIN"),
    asyncHandler(controller.getQuestionariesDetails.bind(controller))
  );
// router.get(
//   "/get-package/:id",
//   asyncHandler(controller.getById.bind(controller))
// );
// router.patch(
//   "/update-package/:id",
//   authMiddleware,
//   asyncHandler(controller.update.bind(controller))
// );
// router.delete(
//   "/delete-package/:id",
//   authMiddleware,
//   asyncHandler(controller.delete.bind(controller))
// );

export default router;
