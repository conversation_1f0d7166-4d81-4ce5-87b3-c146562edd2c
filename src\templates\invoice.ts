export const invoiceTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Invoice</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      font-size: 14px;
      margin: 0;
      padding: 0;
      background: #fff;
    }
    .invoice-box {
      max-width: 900px;
      margin: auto;
      padding: 40px;
      border: 1px solid #eee;
      box-shadow: 0 0 10px rgba(0,0,0,0.15);
      color: #000;
    }
    .invoice-box table {
      width: 100%;
      border-collapse: collapse;
      line-height: 1.5;
    }
    .invoice-box table td {
      padding: 8px;
      vertical-align: top;
    }
    .invoice-title {
      font-size: 24px;
      font-weight: bold;
      text-align: center;
      margin: 20px 0;
      text-transform: uppercase;
    }
    .flex-container {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;
    }
    .company, .bill-to {
      width: 48%;
    }
    .company strong {
      font-size: 16px;
    }
    .meta {
      text-align: right;
      margin-bottom: 20px;
    }
    .meta p {
      margin: 2px 0;
    }
    .details th {
      background: #f2f2f2;
      border: 1px solid #ddd;
      text-align: left;
      padding: 8px;
    }
    .details td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    .totals {
      width: 300px;
      float: right;
      margin-top: 20px;
      border: 1px solid #ddd;
    }
    .totals td {
      border: 1px solid #ddd;
      padding: 6px 10px;
    }
    .totals tr:last-child td {
      font-weight: bold;
    }
    .bank-details {
      margin-top: 40px;
      border: 1px solid #ddd;
    }
    .bank-details td {
      border: 1px solid #ddd;
      padding: 6px 10px;
    }
    .signature {
      margin-top: 60px;
      text-align: right;
    }
  </style>
</head>
<body>
  <div class="invoice-box">
    <div class="meta">
      <p><strong>Invoice Date:</strong> 25/08/2025</p>
      <p><strong>Terms:</strong> Net 25</p>
      <p><strong>Due Date:</strong> 19/09/2025</p>
    </div>

    <div class="invoice-title">Tax Invoice</div>
    <p><strong># INV25/26-00034</strong></p>

    <div class="flex-container">
      <div class="company">
        <strong>Macgence Technologies Private Limited</strong><br>
        CN U72200UP2022PTC164392<br>
        7th Floor, Platina Heights C - 24 Sector 62<br>
        Noida Uttar Pradesh 201301, India<br>
        GSTIN: 09**********1Z9
      </div>
      <div class="bill-to">
        <strong>Bill To</strong><br>
        "Skonto Buve" SIA<br>
        4200 Regent Boulevard<br>
        Irving, 75063 Texas<br>
        United States
      </div>
    </div>

    <table class="details">
      <tr>
        <th>#</th>
        <th>Item & Description</th>
        <th>HSN/SAC</th>
        <th>Qty</th>
        <th>Rate</th>
        <th>IGST</th>
        <th>Amount</th>
      </tr>
      <tr>
        <td>1</td>
        <td>000000</td>
        <td>1.00</td>
        <td>0.00</td>
        <td>0%</td>
        <td>0.00</td>
        <td>0.00</td>
      </tr>
    </table>

    <table class="totals">
      <tr><td>Sub Total</td><td>0.00</td></tr>
      <tr><td>IGST (0%)</td><td>0.00</td></tr>
      <tr><td>Total</td><td>₹0.00</td></tr>
      <tr><td>Items in Total</td><td>1.00</td></tr>
      <tr><td>Balance Due</td><td>₹0.00</td></tr>
    </table>

    <div style="clear: both;"></div>

    <p><strong>Total in Words:</strong> Indian Rupee Zero Only</p>
    <p>Thanks for your business.</p>

    <h4>Bank Details</h4>
    <table class="bank-details">
      <tr><td>Beneficiary Name</td><td>Macgence Technologies (OPC) PVT LTD</td></tr>
      <tr><td>Bank Name</td><td>Kotak Mahindra Bank</td></tr>
      <tr><td>Account Number</td><td>**********</td></tr>
      <tr><td>Bank Address</td><td>Central Market B-138,139 Sec-50, Noida (201301)</td></tr>
      <tr><td>IFSC</td><td>KKBK0005045</td></tr>
      <tr><td>SWIFT CODE</td><td>KKBKINBBCPC</td></tr>
      <tr><td>SWIFT Branch Address</td><td>21, Infinity Park Off Western Express Malad, Mumbai (400097)</td></tr>
      <tr><td>PAN Number</td><td>**********</td></tr>
    </table>

    <div class="signature">
      <p>Harshul Arora<br>Authorized Signature</p>
    </div>
  </div>
</body>
</html>
`;
