import getMailTransporter from "./mailTransporter";

interface MailUser {
  name: string;
  email: string;
  subject: string;
  body: string;
}
const sendEmail = async (options: {
  from: string;
  to: string;
  subject: string;
  html: string;
}): Promise<boolean> => {
  try {
    await getMailTransporter.sendMail(options);
    return true;
  } catch (error) {
    console.error("Error sending email:", error);
    return false;
  }
};

export const mailSender = async ({ user }: { user: MailUser }) => {
  console.log(user, "user");
  const mailOptions = {
    from: process.env.EMAIL_FROM || "<EMAIL>",
    to: user.email,
    subject: user.subject,
    html: user.body,
  };
  return await sendEmail(mailOptions);
};
