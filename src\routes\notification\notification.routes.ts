// routes/notificationRoutes.ts
import { Router } from "express";
import { NotificationController } from "../../controllers/notification/notification.controller";
import authMiddleware from "../../middlewares/checkAuth";
import { asyncHandler } from "../../middlewares/asyncHandler";

const router = Router();
const notificationController = new NotificationController();

router.get(
  "/all-notifications",
  authMiddleware,
  asyncHandler(notificationController.getNotifications.bind(notificationController))
);

router.patch(
  "/read-notification/:notificationId",
  authMiddleware,
  asyncHandler(notificationController.markNotificationRead.bind(notificationController))
);

export default router;