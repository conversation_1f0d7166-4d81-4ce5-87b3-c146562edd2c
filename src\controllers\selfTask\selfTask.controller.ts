import { Request, Response } from "express";
import {
  successResponseWithData,
  errorResponse,
} from "../../helper/apiResponse";
import { SelfTaskService } from "../../services/selfTask/selfTask.service";
import { JwtPayload } from "jsonwebtoken";

const taskService = new SelfTaskService();

interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}

export class SelfTaskController {
  async create(req: AuthenticatedRequest, res: Response) {
    const createdById = req.user?.userId;
    console.log(createdById, "createdById");
    if (!createdById) return errorResponse(res, "Unauthorized");

    const task = await taskService.create({ ...req.body, createdById });
    return successResponseWithData(res, "Task created", task);
  }

  async getAll(req: AuthenticatedRequest, res: Response) {
    const userId = req.user?.userId;
    const tasks = await taskService.getAll({ createdById: userId });
    return successResponseWithData(res, "Tasks fetched", tasks, tasks.length);
  }

  async getPending(req: AuthenticatedRequest, res: Response) {
    const userId = req.user?.userId;
    console.log(userId, "userId");
    const tasks = await taskService.getPending({ createdById: userId });
    return successResponseWithData(res, "Tasks fetched", tasks, tasks.length);
  }

  async getInProgress(req: AuthenticatedRequest, res: Response) {
    const userId = req.user?.userId;
    const tasks = await taskService.getInProgress({ createdById: userId });
    return successResponseWithData(res, "Tasks fetched", tasks, tasks.length);
  }

  async getCompleted(req: AuthenticatedRequest, res: Response) {
    const userId = req.user?.userId;
    const tasks = await taskService.getCompleted({ createdById: userId });
    return successResponseWithData(res, "Tasks fetched", tasks, tasks.length);
  }

  async getById(req: AuthenticatedRequest, res: Response) {
    const task = await taskService.getById(req.params.id);
    if (!task) return errorResponse(res, "Task not found");
    return successResponseWithData(res, "Task fetched", task);
  }

  async update(req: AuthenticatedRequest, res: Response) {
    const task = await taskService.update(req.params.id, req.body);
    if (!task) return errorResponse(res, "Task not found");
    return successResponseWithData(res, "Task updated", task);
  }

  async delete(req: AuthenticatedRequest, res: Response) {
    await taskService.delete(req.params.id);
    return successResponseWithData(res, "Task deleted", {});
  }
}
