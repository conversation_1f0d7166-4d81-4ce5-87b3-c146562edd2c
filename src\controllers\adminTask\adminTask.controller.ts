import { Request, Response } from "express";
import { successResponseWithData, errorResponse } from "../../helper/apiResponse";
import { AdminTaskService } from "../../services/adminTask/adminTask.service";
import { JwtPayload } from "jsonwebtoken";

const taskService = new AdminTaskService();

interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
} 

export class AdminTaskController {
  async create(req: AuthenticatedRequest, res: Response) {
    try {
      const createdById = req.user?.userId;
      if (!createdById) return errorResponse(res, "Unauthorized");

      const task = await taskService.create({ ...req.body, createdById });
      return successResponseWithData(res, "Task created", task);
    } catch (err: any) {
      console.error("Create Task Error:", err);
      return errorResponse(res, err.message || "Failed to create task");
    }
  }

  async getAll(req: Request, res: Response) {
    try {
      const tasks = await taskService.getAll();
      return successResponseWithData(res, "Tasks fetched", tasks, tasks.length);
    } catch (err: any) {
      console.error("Get All Tasks Error:", err);
      return errorResponse(res, err.message || "Failed to fetch tasks");
    }
  }

  async getById(req: Request, res: Response) {
    try {
      const task = await taskService.getById(req.params.id);
      if (!task) return errorResponse(res, "Task not found");
      return successResponseWithData(res, "Task fetched", task);
    } catch (err: any) {
      console.error("Get Task Error:", err);
      return errorResponse(res, err.message || "Failed to fetch task");
    }
  }

  async update(req: AuthenticatedRequest, res: Response) {
    try {
      const task = await taskService.update(req.params.id, req.body);
      if (!task) return errorResponse(res, "Task not found");
      return successResponseWithData(res, "Task updated", task);
    } catch (error: any) {
      console.error("Update Task Error:", error);
      return errorResponse(res, error.message || "Failed to update task");
    }
  }

  async delete(req: Request, res: Response) {
    try {
      await taskService.delete(req.params.id);
      return successResponseWithData(res, "Task deleted", {});
    } catch (err: any) {
      console.error("Delete Task Error:", err);
      return errorResponse(res, err.message || "Failed to delete task");
    }
  }
}
