import { Router } from "express";
import clientRoutes from "./client.auth.route";
import annotatorRoutes from "./annotator/annotator.routes";
import messageRoutes from "./message/message.route";
import coworkerRoutes from "./coworker/coworker.routes";
import projectRoutes from "./projects/project.route";
import taskRoutes from "./tasks/task.route";
import packageRoutes from "./packages/packages.route";
import userRoutes from "./client.user.route";
import faqRoutes from "./faq/faq.routes";
import adminRoutes from "./Admin/admin.routes";
import onboardingRoutes from "./onboarding/onboarding.route";
import matchmakingRoutes from "./matchmaking/matchmaking.route";
import attendanceRoutes from "./Attendance/attendance.routes";
import adminTaskRoutes from "./adminTask/adminTask.routes";
import dashboardRoutes from "./dashboard/dashboard.routes";
import selfTaskRoutes from "./selfTask/selfTask.route";
import shiftChangeRoutes from "./shiftChange/shiftChange.routes";
import refundRoutes from "./refund/refund.route";
import chatUploadsRouter from "./chat/uploads.route";
import leaveRoutes from "./leave/leave.routes";
import zohoRoutes from "./zoho/zoho.route";
import bankTransferRoutes from "./bank-transfer/bank-transfer.route";
import zohoDeskRoutes from "./zoho/zoho-desk.route";
import billingsRoutes from "./billings/billings.route";
import subscriptionRoutes from "./subscription/subscription.route";
import notificationRoutes from "./notification/notification.routes";
import otpRoutes from "./otp/otp.routes";
import userCommunicationRoutes from "./user-communication/user-communication.routes";
import emailPreviewRoutes from "./email-preview/email-preview.route";
import emailRedirectRoutes from "./email/email-redirect.routes";

const router = Router();

router.use("/clients", clientRoutes);
router.use("/tasks", taskRoutes);
router.use("/packages", packageRoutes);
router.use("/annotator", annotatorRoutes);
router.use("/onboarding", onboardingRoutes);
router.use("/messages", messageRoutes);
router.use("/coworker", coworkerRoutes);
router.use("/projects", projectRoutes);
router.use("/users", userRoutes);
router.use("/faq", faqRoutes);
router.use("/admin", adminRoutes);
router.use("/matchmaking", matchmakingRoutes);
router.use("/attendance", attendanceRoutes);
router.use("/admin-tasks", adminTaskRoutes);
router.use("/dashboard", dashboardRoutes);
router.use("/self-tasks", selfTaskRoutes);
router.use("/shift", shiftChangeRoutes);
router.use("/refund", refundRoutes);
router.use("/chat", chatUploadsRouter);
router.use("/leave", leaveRoutes);
router.use("/zoho", zohoRoutes);
router.use("/bank-transfer", bankTransferRoutes);
router.use("/zoho-desk", zohoDeskRoutes);
router.use("/billings", billingsRoutes);
router.use("/subscription", subscriptionRoutes);
router.use("/notification", notificationRoutes);
router.use("/otp", otpRoutes);
router.use("/user-communication", userCommunicationRoutes);
router.use("/email-preview", emailPreviewRoutes);
router.use("/email", emailRedirectRoutes);

export default router;
