const axios = require('axios');

// Test the email preview system
async function testEmailPreviewSystem() {
  const baseUrl = 'http://localhost:3000/api/email-preview';
  
  console.log('🧪 Testing Email Preview System...\n');

  try {
    // Test 1: Get template list
    console.log('1. Testing template list endpoint...');
    const templatesResponse = await axios.get(`${baseUrl}/templates`);
    console.log(`✅ Found ${templatesResponse.data.data.length} templates`);
    
    const templates = templatesResponse.data.data;
    if (templates.length === 0) {
      console.log('❌ No templates found');
      return;
    }

    // Test 2: Preview first template
    const firstTemplate = templates[0];
    console.log(`\n2. Testing template preview for: ${firstTemplate.name}`);
    const previewResponse = await axios.get(`${baseUrl}/template/${firstTemplate.name}`);
    console.log(`✅ Preview generated (${previewResponse.data.length} characters)`);

    // Test 3: Get sample data
    console.log(`\n3. Testing sample data for: ${firstTemplate.name}`);
    const sampleDataResponse = await axios.get(`${baseUrl}/template/${firstTemplate.name}/sample-data`);
    console.log(`✅ Sample data retrieved (${Object.keys(sampleDataResponse.data.data).length} variables)`);

    // Test 4: Get template variables
    console.log(`\n4. Testing variables extraction for: ${firstTemplate.name}`);
    const variablesResponse = await axios.get(`${baseUrl}/template/${firstTemplate.name}/variables`);
    console.log(`✅ Variables extracted (${variablesResponse.data.data.length} variables)`);

    // Test 5: Test template rendering
    console.log(`\n5. Testing template rendering for: ${firstTemplate.name}`);
    const testData = { firstName: 'Test', lastName: 'User', companyName: 'Test Company' };
    const renderResponse = await axios.post(`${baseUrl}/template/${firstTemplate.name}/test`, testData);
    console.log(`✅ Template rendered successfully`);

    // Test 6: Dashboard access
    console.log(`\n6. Testing dashboard access...`);
    const dashboardResponse = await axios.get(baseUrl);
    console.log(`✅ Dashboard loaded (${dashboardResponse.data.length} characters)`);

    console.log('\n🎉 All tests passed! Email preview system is working correctly.');
    console.log(`\n📧 Access the dashboard at: http://localhost:3000/api/email-preview`);
    
    // Display template summary
    console.log('\n📊 Template Summary:');
    const categories = [...new Set(templates.map(t => t.category))];
    categories.forEach(category => {
      const categoryTemplates = templates.filter(t => t.category === category);
      console.log(`   ${category}: ${categoryTemplates.length} templates`);
    });

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the development server is running:');
      console.log('   npm run dev');
    } else if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

// Run the test
testEmailPreviewSystem();