import { Router } from "express";
import { SubscriptionController } from "../../controllers/subscription/subscription.controller";
import { asyncHand<PERSON> } from "../../middlewares/asyncHandler";
import authMiddleware from "../../middlewares/checkAuth";
import { hasRole } from "../../middlewares/checkRole";

const router = Router();
const controller = new SubscriptionController();

// Get current user's subscription status
router.get(
  "/status",
  authMiddleware,
  asyncHandler(controller.getSubscriptionStatus.bind(controller))
);

// Get detailed subscription information
router.get(
  "/details",
  authMiddleware,
  asyncHandler(controller.getDetailedSubscriptionInfo.bind(controller))
);

// Check access to a specific feature
router.get(
  "/feature/:feature/access",
  authMiddleware,
  asyncHandler(controller.checkFeatureAccess.bind(controller))
);

// Get all available features
router.get(
  "/features",
  authMiddleware,
  asyncHandler(controller.getAvailableFeatures.bind(controller))
);

// Update expired subscriptions (admin only)
router.post(
  "/update-expired",
  authMiddleware,
  hasRole("ADMIN"),
  as<PERSON><PERSON><PERSON><PERSON>(controller.updateExpiredSubscriptions.bind(controller))
);

export default router;