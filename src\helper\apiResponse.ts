import { Response } from "express";

export const successResponse = async (res: Response, msg: string): Promise<Response> => {
  return res.status(200).json({
    status: 1,
    message: msg,
  });
};

export const successResponseWithData = async (
  res: Response,
  msg: string,
  data: any,
  count?: number
): Promise<Response> => {
  return res.status(200).json({
    status: 1,
    message: msg,
    count,
    data,
  });
};

export const errorResponse = async (res: Response, msg: string): Promise<Response> => {
  return res.status(400).json({
    status: 0,
    message: msg,
  });
};

export const errorResponseWithData = async (
  res: Response,
  msg: string,
  data: any
): Promise<Response> => {
  return res.status(400).json({
    status: 0,
    message: msg,
    data,
  });
};

export const notFoundResponse = async (res: Response, msg: string): Promise<Response> => {
  return res.status(404).json({
    status: 0,
    message: msg,
  });
};

export const validationErrorWithData = async (
  res: Response,
  msg: string,
  data: any
): Promise<Response> => {
  return res.status(400).json({
    status: 0,
    message: msg,
    data,
  });
};

export const validationError = async (res: Response, msg: string): Promise<Response> => {
  return res.status(400).json({
    status: 0,
    message: msg,
  });
};

export const unauthorizedResponse = async (res: Response, msg: string): Promise<Response> => {
  return res.status(401).json({
    status: 0,
    message: msg,
  });
};


export class ApiResponse {
  static success(res: Response, data: any = {}, message = 'Success', statusCode: number = 200): Response {
    return res.status(statusCode).json({
      status: 1,
      message,
      data,
    });
  }

  static error(res: Response, message = 'Error', statusCode: number = 400, data?: any): Response {
    return res.status(statusCode).json({
      status: 0,
      message,
      ...(data ? { data } : {}),
    });
  }

  static notFound(res: Response, message = 'Not found'): Response {
    return res.status(404).json({
      status: 0,
      message,
    });
  }

  static validationError(res: Response, message = 'Validation failed', data?: any): Response {
    return res.status(400).json({
      status: 0,
      message,
      ...(data ? { data } : {}),
    });
  }

  static unauthorized(res: Response, message = 'Unauthorized'): Response {
    return res.status(401).json({
      status: 0,
      message,
    });
  }
}
