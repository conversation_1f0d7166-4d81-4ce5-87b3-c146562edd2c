export const teamassignmentTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Team Assignment</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #171617;
            padding: 20px;
            text-align: center;
        }
        .logo {
            max-width: 140px;
            height: auto;
            display: block;
            margin: 0 auto;
        }
        .content {
            padding: 30px;
            background-color: #ffffff;
        }
        .assignment-banner {
            background: linear-gradient(135deg, #3b82f6 0%, #1e3a8a 100%);
            border: none;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .assignment-banner:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.2);
        }
        .assignment-banner-title {
            font-size: 22px;
            font-weight: 700;
            color: #ffffff;
            padding-bottom: 10px;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }
        .assignment-banner-subtitle {
            font-size: 16px;
            color: #e0f2fe;
            margin: 0;
        }
        .cta-container {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background-color: #3b82f6;
            color: #ffffff !important;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 10px;
            transition: background-color 0.3s ease;
        }
        .cta-button:hover {
            background-color: #2563eb;
        }
        .signature {
            font-size: 14px;
            line-height: 22px;
            color: #4b5563;
            margin: 0;
        }
        .signature span {
            color: #1e3a8a;
            font-weight: 600;
        }
        .footer {
            padding: 20px;
            text-align: center;
            font-size: 13px;
            line-height: 20px;
            background-color: #171617;
            color: #ffffff;
        }
        .footer a {
            color: #e1e1e1 !important;
            text-decoration: none;
        }
        .footer span {
            color: #f0f0f0;
        }
        /* Email client compatibility */
        table {
            width: 100%;
            border-collapse: collapse;
        }
        @media only screen and (max-width: 600px) {
            .container {
                padding: 15px;
            }
            .content {
                padding: 20px;
            }
            .header {
                padding: 15px;
            }
            .assignment-banner-title {
                font-size: 18px;
            }
            .assignment-banner-subtitle {
                font-size: 14px;
            }
            .cta-button {
                width: 100%;
                box-sizing: border-box;
                margin: 10px 0;
            }
        }
        /* Accessibility */
        a:focus, .cta-button:focus {
            outline: 2px solid #1e3a8a;
            outline-offset: 2px;
        }
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #1a1a1a;
            }
            .container {
                background-color: #ffffff;
            }
            .content {
                background-color: #ffffff;
                color: #333333;
            }
            .assignment-banner {
                background: linear-gradient(135deg, #60a5fa 0%, #1e3a8a 100%);
            }
            .assignment-banner-title {
                color: #ffffff;
            }
            .assignment-banner-subtitle {
                color: #e0f2fe;
            }
            .signature {
                color: #999999;
            }
            .signature span {
                color: #1e3a8a;
            }
            .cta-button {
                background-color: #60a5fa;
            }
            .cta-button:hover {
                background-color: #3b82f6;
            }
        }
    </style>
</head>
<body>
    <table role="presentation" class="container">
        <tr>
            <td>
                <div class="header">
                    <img src="https://macgence.s3.ap-south-1.amazonaws.com/whitemode.png" alt="Logo" class="logo">
                </div>

                <div class="content">
                    <div class="assignment-banner">
                        <div class="assignment-banner-title">🎯 Your Team is Ready!</div>
                        <p class="assignment-banner-subtitle">Let's kick off your project with confidence!</p>
                    </div>

                    <p>Hi {{ params.firstName }},</p>
                    <p>We're excited to let you know that a dedicated team has been assigned to your project. We're ready to deliver high-quality results and ensure a smooth collaboration experience.</p>

                    <div class="cta-container">
                        <a href="http://app.getannotator.com/dashboard" class="cta-button">📊 View Dashboard</a>
                    </div>

                    <p class="signature">
                        Best Regards,<br>
                        <span>The GetAnnotator Team</span>
                    </p>
                </div>

                <div class="footer">
                    <span><EMAIL></span> | 
                    <a href="http://app.getannotator.com/" style="color: #e1e1e1ff; text-decoration: none;">app.getannotator.com</a> | 
                    <a href="tel:+13602098904">******-209-8904</a><br>
                    <span>Need help? Reach us via our <a href="http://app.getannotator.com/contact">contact form</a>.</span>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>

`;
