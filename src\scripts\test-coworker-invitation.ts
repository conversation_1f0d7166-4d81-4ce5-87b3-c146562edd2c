import { config } from 'dotenv';
import { CoworkerService } from '../services/coworker/coworker.service';
import { EmailService } from '../services/email/email.service';

// Load environment variables
config();

/**
 * Test script to verify coworker invitation flow
 * This script tests the email generation and URL structure
 */
async function testCoworkerInvitation() {
  console.log('🧪 Testing Coworker Invitation Flow...\n');

  try {
    // Test email service initialization
    const emailService = new EmailService();
    console.log('✅ Email service initialized successfully');

    // Test URL generation
    const testEmail = '<EMAIL>';
    const testToken = 'test-token-123';
    
    // Test backend redirect URL generation
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:3100';
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    
    const backendRedirectUrl = `${backendUrl}/api/email/redirect/coworker-invite?email=${encodeURIComponent(testEmail)}&token=${testToken}`;
    const expectedFrontendUrl = `${frontendUrl}/coworker/accept-invite?email=${encodeURIComponent(testEmail)}&token=${testToken}`;
    
    console.log('\n📧 URL Generation Test:');
    console.log('Backend Redirect URL:', backendRedirectUrl);
    console.log('Expected Frontend URL:', expectedFrontendUrl);
    
    // Test email template data
    const emailData = {
      coworkerName: "Test Coworker",
      invitationLink: backendRedirectUrl,
      role: "EDIT",
      timeLimit: 10,
      companyName: process.env.COMPANY_NAME || "GetAnnotator",
      supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
      phoneNumber: process.env.SUPPORT_PHONE || "******-567-8900",
      websiteUrl: frontendUrl,
    };
    
    console.log('\n📋 Email Template Data:');
    console.log(JSON.stringify(emailData, null, 2));
    
    // Test template rendering (without actually sending email)
    try {
      // Since renderTemplate is private, we'll just check if template file exists
      const fs = require('fs').promises;
      const path = require('path');
      const templatePath = path.join(__dirname, '../templates/email/invite-coworker.html');
      const templateExists = await fs.access(templatePath).then(() => true).catch(() => false);
      console.log('\n✅ Email template file exists:', templateExists ? '✅ Yes' : '❌ No');

      if (templateExists) {
        const templateContent = await fs.readFile(templatePath, 'utf-8');
        console.log('Template contains invitation link placeholder:', templateContent.includes('{{invitationLink}}') ? '✅ Yes' : '❌ No');
      }
    } catch (error) {
      console.error('❌ Email template check failed:', error);
    }
    
    // Test environment variables
    console.log('\n🔧 Environment Variables Check:');
    console.log('BACKEND_URL:', process.env.BACKEND_URL || '❌ Not set');
    console.log('FRONTEND_URL:', process.env.FRONTEND_URL || '❌ Not set');
    console.log('SMTP_HOST:', process.env.SMTP_HOST || '❌ Not set');
    console.log('SMTP_USER:', process.env.SMTP_USER ? '✅ Set' : '❌ Not set');
    console.log('COMPANY_NAME:', process.env.COMPANY_NAME || '❌ Not set');
    
    console.log('\n✅ Coworker invitation test completed successfully!');
    console.log('\n📝 Next Steps:');
    console.log('1. Start your backend server');
    console.log('2. Test the redirect endpoint: GET ' + backendRedirectUrl);
    console.log('3. Verify it redirects to: ' + expectedFrontendUrl);
    console.log('4. Send a real invitation to test the complete flow');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testCoworkerInvitation()
    .then(() => {
      console.log('\n🎉 Test completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test failed with error:', error);
      process.exit(1);
    });
}

export { testCoworkerInvitation };
