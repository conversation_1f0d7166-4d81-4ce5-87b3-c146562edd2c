# Email Template Preview System

A comprehensive development and testing system for email templates that makes it easy to create, preview, test, and maintain email templates.

## 🚀 Features

- **Web-based Preview Dashboard** - Visual interface to browse and preview all templates
- **CLI Tools** - Command-line interface for template management
- **Live Preview** - Real-time template rendering with sample data
- **Template Testing** - Send test emails with custom data
- **Variable Extraction** - Automatic detection of template variables
- **Template Validation** - Comprehensive validation and error checking
- **Sample Data Generation** - Automatic sample data for all templates
- **Category Organization** - Templates organized by purpose and functionality

## 📁 Project Structure

```
src/
├── controllers/email-preview/
│   └── email-preview.controller.ts    # API endpoints for preview system
├── services/email-preview/
│   └── email-preview.service.ts       # Core preview functionality
├── routes/email-preview/
│   └── email-preview.route.ts         # Route definitions
├── scripts/
│   └── email-template-cli.ts          # CLI tool for template management
└── templates/
    ├── index.ts                       # Template exports and renderer
    ├── *.ts                          # TypeScript template files
    └── email/
        └── *.html                    # HTML template files
```

## 🌐 Web Preview Dashboard

Access the web-based preview dashboard at:
```
http://localhost:3000/api/email-preview
```

### Dashboard Features:
- **Template Grid** - Visual cards showing all available templates
- **Category Filtering** - Filter templates by category
- **Search Functionality** - Search by name, category, or variables
- **Quick Actions** - Preview, sample data, variables, and test email buttons
- **Statistics** - Overview of total templates, categories, and variables

### API Endpoints:

#### Get Template List
```http
GET /api/email-preview/templates
```

#### Preview Template
```http
GET /api/email-preview/template/{templateName}
POST /api/email-preview/template/{templateName}
```

#### Get Sample Data
```http
GET /api/email-preview/template/{templateName}/sample-data
```

#### Get Template Variables
```http
GET /api/email-preview/template/{templateName}/variables
```

#### Test Template Rendering
```http
POST /api/email-preview/template/{templateName}/test
```

#### Send Test Email
```http
POST /api/email-preview/template/{templateName}/send-test
```

## 🖥️ CLI Tool Usage

The CLI tool provides powerful command-line functionality for template development:

### Installation
```bash
npm install  # Installs commander dependency
```

### Available Commands

#### List All Templates
```bash
npm run email-cli list
npm run email-cli list --category "Authentication"
```

#### Preview Template
```bash
# Preview with sample data
npm run email-cli preview welcome-mail

# Preview with custom data
npm run email-cli preview welcome-mail --data '{"firstName":"John","companyName":"Acme Corp"}'

# Save preview to file
npm run email-cli preview welcome-mail --output preview.html
```

#### Get Template Information
```bash
npm run email-cli info welcome-mail
```

#### Generate Sample Data
```bash
# Display sample data
npm run email-cli sample-data welcome-mail

# Save to file
npm run email-cli sample-data welcome-mail --output sample.json
```

#### Test Template Rendering
```bash
# Test with sample data
npm run email-cli test welcome-mail

# Test with custom data
npm run email-cli test welcome-mail --data '{"firstName":"Jane"}'

# Test with data from file
npm run email-cli test welcome-mail --file test-data.json
```

#### Create New Template
```bash
npm run email-cli create new-template --category "Marketing" --description "New marketing email"
```

#### Validate All Templates
```bash
npm run email-cli validate
```

## 📧 Template Development

### Template Structure

Templates are stored in two formats:
1. **TypeScript files** (`src/templates/*.ts`) - For programmatic access
2. **HTML files** (`src/templates/email/*.html`) - For email service

### Creating a New Template

1. **Using CLI** (Recommended):
```bash
npm run email-cli create my-new-template --category "General" --description "My new email template"
```

2. **Manual Creation**:
   - Create `src/templates/my-new-template.ts`
   - Create `src/templates/email/my-new-template.html`
   - Add to `src/templates/index.ts`

### Template Variables

Use double curly braces for variables:
```html
<h1>Hello {{firstName}}!</h1>
<p>Welcome to {{companyName}}</p>
```

### Conditional Content

Support for conditional blocks:
```html
{{#hasPromotion}}
<div class="promotion">
    <p>Special offer: {{promotionText}}</p>
</div>
{{/hasPromotion}}
```

### Nested Objects

Access nested object properties:
```html
<p>{{user.firstName}} {{user.lastName}}</p>
<p>{{company.name}} - {{company.address}}</p>
```

## 🧪 Testing Templates

### Web Testing
1. Go to `http://localhost:3000/api/email-preview`
2. Click "Preview" on any template
3. Use "Test Email" to send to your email
4. View sample data and variables

### CLI Testing
```bash
# Quick validation
npm run email-cli validate

# Test specific template
npm run email-cli test welcome-mail

# Preview in browser
npm run email-cli preview welcome-mail --output temp.html && open temp.html
```

### Custom Test Data

Create a JSON file with test data:
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "companyName": "Test Company",
  "meetingLink": "https://example.com/meeting"
}
```

Test with custom data:
```bash
npm run email-cli test welcome-mail --file test-data.json
```

## 📊 Template Categories

Templates are organized into the following categories:

- **Authentication** - Login, signup, password reset
- **Onboarding** - Welcome and getting started
- **Team Management** - Invitations and assignments
- **Account Management** - Account status and security
- **Billing** - Payments and subscriptions
- **Usage Alerts** - Usage warnings and limits
- **Project Management** - Project updates
- **System Notifications** - Maintenance and updates
- **Marketing** - Newsletters and promotions
- **General** - General purpose templates

## 🔧 Configuration

Template system configuration is stored in `email-template-config.json`:

```json
{
  "development": {
    "previewServer": {
      "enabled": true,
      "port": 3000,
      "basePath": "/api/email-preview"
    },
    "testEmail": {
      "defaultRecipient": "<EMAIL>",
      "subjectPrefix": "[DEV] "
    }
  },
  "templateDefaults": {
    "companyName": "DataAnnotator Pro",
    "supportEmail": "<EMAIL>",
    "websiteUrl": "https://dataannotator.com"
  }
}
```

## 🚀 Quick Start

1. **Start the development server**:
```bash
npm run dev
```

2. **Open the preview dashboard**:
```
http://localhost:3000/api/email-preview
```

3. **List available templates**:
```bash
npm run email-cli list
```

4. **Preview a template**:
```bash
npm run email-cli preview welcome-mail
```

5. **Test template rendering**:
```bash
npm run email-cli test welcome-mail
```

## 🔍 Troubleshooting

### Common Issues

1. **Template not found**
   - Check template name spelling
   - Ensure template is added to `src/templates/index.ts`

2. **Variables not resolving**
   - Check variable names match exactly
   - Use `npm run email-cli info <template>` to see available variables

3. **Preview not loading**
   - Ensure development server is running
   - Check console for errors

4. **CLI commands not working**
   - Run `npm install` to ensure dependencies are installed
   - Check Node.js version compatibility

### Validation Errors

Run validation to check for issues:
```bash
npm run email-cli validate
```

Common validation issues:
- Missing DOCTYPE declaration
- Invalid HTML structure
- Unresolved template variables
- Missing required meta tags

## 📝 Best Practices

1. **Template Design**:
   - Use responsive design principles
   - Include fallback fonts
   - Test across email clients
   - Keep file size under 500KB

2. **Variable Naming**:
   - Use descriptive names
   - Follow camelCase convention
   - Group related variables

3. **Testing**:
   - Test with various data scenarios
   - Validate HTML structure
   - Check email client compatibility
   - Test unsubscribe links

4. **Maintenance**:
   - Regular validation runs
   - Update sample data
   - Document template purposes
   - Version control templates

## 🤝 Contributing

When adding new templates:

1. Use the CLI to create templates: `npm run email-cli create <name>`
2. Follow existing naming conventions
3. Add comprehensive sample data
4. Test thoroughly before committing
5. Update documentation if needed

## 📚 Additional Resources

- [Email Template Best Practices](https://docs.example.com/email-best-practices)
- [HTML Email Guidelines](https://docs.example.com/html-email)
- [Template Variable Reference](https://docs.example.com/template-variables)
- [Email Client Compatibility](https://docs.example.com/email-clients)

---

**Happy templating! 📧✨**