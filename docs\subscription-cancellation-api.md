# Subscription Cancellation API

This document describes the subscription cancellation functionality that allows both clients and administrators to cancel subscriptions with proper external provider integration.

## Overview

The subscription cancellation API provides:
- Client-initiated subscription cancellation
- Admin-initiated subscription cancellation
- Integration with external payment providers (PayPal, Dodo)
- Proper status management and validation
- Detailed logging and audit trail

## API Endpoints

### Client Subscription Management

#### Get Subscription Details
```
GET /api/billings/subscription/:id
```

**Description**: Retrieves detailed information about a specific subscription for the authenticated client.

**Authentication**: Required (CLIENT role)

**Parameters**:
- `id` (path parameter): The subscription ID

**Response**:
```json
{
  "success": true,
  "message": "Subscription details fetched successfully",
  "data": {
    "id": "sub_123456",
    "status": "ACTIVE",
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": null,
    "next_billing_date": "2024-02-01T00:00:00.000Z",
    "user": {
      "id": "user_123",
      "name": "<PERSON>",
      "email": "<EMAIL>"
    },
    "package": {
      "id": "pkg_123",
      "name": "Premium Plan",
      "description": "Premium subscription plan",
      "price": 29.99,
      "billingType": "MONTHLY",
      "currency": "USD"
    },
    "Payment": [
      {
        "id": "pay_123",
        "paymentId": "pi_123456",
        "amount": 29.99,
        "status": "SUCCESS",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "provider": "PAYPAL"
      }
    ]
  }
}
```

#### Cancel Subscription
```
POST /api/billings/subscription/:id/cancel
```

**Description**: Cancels a subscription for the authenticated client.

**Authentication**: Required (CLIENT role)

**Parameters**:
- `id` (path parameter): The subscription ID

**Request Body**:
```json
{
  "reason": "No longer needed"
}
```

**Fields**:
- `reason` (optional, string): Reason for cancellation

**Response**:
```json
{
  "success": true,
  "message": "Subscription cancelled successfully",
  "data": {
    "subscription": {
      "id": "sub_123456",
      "status": "CANCELLED",
      "endDate": "2024-01-15T10:30:00.000Z",
      "user": {
        "id": "user_123",
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "package": {
        "id": "pkg_123",
        "name": "Premium Plan",
        "price": 29.99
      }
    },
    "cancellationDetails": {
      "success": true,
      "provider": "PayPal",
      "externalId": "I-BW452GLLEP1G",
      "rawResponse": {
        "status": "CANCELLED"
      }
    },
    "message": "Subscription cancelled successfully"
  }
}
```

### Admin Subscription Management

#### Get Subscription Details (Admin)
```
GET /api/billings/admin/subscription/:id
```

**Description**: Retrieves detailed information about any subscription (admin access).

**Authentication**: Required (ADMIN role)

**Parameters**:
- `id` (path parameter): The subscription ID

**Response**: Same format as client endpoint but can access any subscription

#### Cancel Subscription (Admin)
```
POST /api/billings/admin/subscription/:id/cancel
```

**Description**: Cancels any subscription (admin access).

**Authentication**: Required (ADMIN role)

**Parameters**:
- `id` (path parameter): The subscription ID

**Request Body**:
```json
{
  "reason": "Administrative cancellation"
}
```

**Response**: Same format as client cancellation endpoint

## External Provider Integration

### PayPal Integration

The system integrates with PayPal's subscription API to cancel subscriptions:

**API Call**:
```
POST /v1/billing/subscriptions/{subscription_id}/cancel
```

**Request Body**:
```json
{
  "reason": "User requested cancellation"
}
```

### Dodo Integration

The system integrates with Dodo's subscription API:

**API Call**:
```
POST /subscriptions/{subscription_id}/cancel
```

**Request Body**:
```json
{
  "reason": "User requested cancellation"
}
```

## Business Logic

### Subscription Status Validation

Before cancellation, the system validates:
1. Subscription exists and user has access
2. Subscription is not already cancelled
3. Subscription status allows cancellation (not EXPIRED or FAILED)

### Cancellation Process

1. **Validate Access**: Verify user can cancel the subscription
2. **Status Check**: Ensure subscription can be cancelled
3. **External API Call**: Cancel with payment provider
4. **Database Update**: Update local subscription status
5. **Logging**: Record cancellation details
6. **Response**: Return cancellation confirmation

### Provider Detection

The system determines the payment provider based on subscription data:
- If `paypal_subscription_id` exists → PayPal cancellation
- If `subscription_id` exists → Dodo cancellation
- Graceful handling if external API fails

## Error Handling

### Common Error Responses

#### 400 Bad Request
```json
{
  "success": false,
  "message": "Subscription ID is required"
}
```

#### 403 Forbidden
```json
{
  "success": false,
  "message": "Subscription not found or access denied"
}
```

#### 409 Conflict
```json
{
  "success": false,
  "message": "Subscription is already cancelled"
}
```

#### 422 Unprocessable Entity
```json
{
  "success": false,
  "message": "Cannot cancel subscription with status: EXPIRED"
}
```

#### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Failed to cancel subscription"
}
```

### External API Failure Handling

If external provider API fails:
- Local cancellation still proceeds
- Error details are logged
- Response includes failure information
- System maintains data consistency

## Database Operations

### Subscription Update
```sql
UPDATE "Subscription" 
SET 
  "status" = 'CANCELLED',
  "endDate" = NOW(),
  "rawResponse" = ?,
  "updatedAt" = NOW()
WHERE "id" = ?
```

### Status Transitions
```
ACTIVE → CANCELLED
PENDING → CANCELLED
INACTIVE → CANCELLED
PAUSED → CANCELLED

EXPIRED → Cannot cancel
FAILED → Cannot cancel
CANCELLED → Already cancelled
```

## Security Features

### Access Control
- **Client Access**: Users can only cancel their own subscriptions
- **Admin Access**: Admins can cancel any subscription
- **JWT Authentication**: Required for all endpoints
- **Role Validation**: Proper role-based access control

### Data Protection
- Subscription data is filtered based on user access
- Sensitive payment information is not exposed
- Audit logging for all cancellation actions

## Logging and Monitoring

### Console Logs
```javascript
console.log(`Subscription ${subscriptionId} cancelled for user ${userId}. Reason: ${reason}`);
console.log(`Cancelling PayPal subscription: ${paypalSubscriptionId}`);
console.error("Error cancelling PayPal subscription:", error);
```

### Audit Trail
- All cancellation attempts are logged
- External API responses are stored
- Error scenarios are tracked
- User actions are recorded

## Usage Examples

### Using cURL

**Get subscription details:**
```bash
curl -X GET "http://localhost:3000/api/billings/subscription/sub_123456" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Cancel subscription:**
```bash
curl -X POST "http://localhost:3000/api/billings/subscription/sub_123456/cancel" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"reason": "No longer needed"}'
```

**Admin cancellation:**
```bash
curl -X POST "http://localhost:3000/api/billings/admin/subscription/sub_123456/cancel" \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"reason": "Administrative cancellation"}'
```

### Using JavaScript/Axios

```javascript
const axios = require('axios');

const headers = {
  'Authorization': 'Bearer YOUR_JWT_TOKEN',
  'Content-Type': 'application/json'
};

// Get subscription details
const getSubscription = async (subscriptionId) => {
  try {
    const response = await axios.get(`/api/billings/subscription/${subscriptionId}`, { headers });
    console.log('Subscription:', response.data.data);
  } catch (error) {
    console.error('Error:', error.response.data);
  }
};

// Cancel subscription
const cancelSubscription = async (subscriptionId, reason) => {
  try {
    const response = await axios.post(
      `/api/billings/subscription/${subscriptionId}/cancel`,
      { reason },
      { headers }
    );
    console.log('Cancellation result:', response.data);
  } catch (error) {
    console.error('Error:', error.response.data);
  }
};
```

## Testing

### Test Scenarios
1. **Valid Cancellation**: Active subscription cancellation
2. **Duplicate Cancellation**: Attempt to cancel already cancelled subscription
3. **Invalid Subscription**: Non-existent subscription ID
4. **Access Control**: User trying to cancel another user's subscription
5. **External API Failure**: Provider API unavailable
6. **Status Validation**: Attempt to cancel expired subscription

### Test Script
```bash
# Set environment variables
export TEST_AUTH_TOKEN="your_client_jwt_token"
export TEST_ADMIN_TOKEN="your_admin_jwt_token"
export TEST_SUBSCRIPTION_ID="subscription_id_to_test"

# Run the test
node test-subscription-cancellation.js
```

## Configuration

### Environment Variables
- `PAYPAL_API`: PayPal API base URL
- `PAYPAL_CLIENT_ID`: PayPal client ID
- `PAYPAL_CLIENT_SECRET`: PayPal client secret
- `DODO_BASE_URL`: Dodo API base URL
- `DODO_PAYMENTS_API_KEY`: Dodo API key

### Provider Endpoints
- **PayPal**: `/v1/billing/subscriptions/{id}/cancel`
- **Dodo**: `/subscriptions/{id}/cancel`

## Performance Considerations

- **External API Calls**: May add 1-3 seconds to response time
- **Database Queries**: Optimized with proper indexing
- **Error Handling**: Graceful degradation if provider APIs fail
- **Caching**: Subscription details can be cached for better performance

## Future Enhancements

1. **Cancellation Scheduling**: Allow future-dated cancellations
2. **Partial Refunds**: Integration with refund processing
3. **Cancellation Surveys**: Collect feedback on cancellation reasons
4. **Retention Offers**: Present alternatives before cancellation
5. **Bulk Cancellation**: Admin bulk cancellation functionality
6. **Cancellation Analytics**: Detailed reporting on cancellation patterns

## Troubleshooting

### Common Issues

1. **External API Timeout**: Check provider API status
2. **Invalid Subscription ID**: Verify subscription exists
3. **Permission Denied**: Check user role and subscription ownership
4. **Already Cancelled**: Verify current subscription status
5. **Provider Integration**: Check API credentials and endpoints

### Debug Steps

1. Check subscription status in database
2. Verify external provider credentials
3. Review application logs for errors
4. Test provider API endpoints directly
5. Validate JWT token and user permissions