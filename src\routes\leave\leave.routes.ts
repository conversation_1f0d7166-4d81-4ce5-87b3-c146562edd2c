import express from "express";
import authMiddleware from "../../middlewares/checkAuth";
import { hasMultipleRole, hasRole } from "../../middlewares/checkRole";
import { asyncHandler } from "../../middlewares/asyncHandler";
import * as leaveController from "../../controllers/leave/leave.controller";

const router = express.Router();

router.post(
  "/request",
  authMiddleware,
  hasMultipleRole(["ADMIN", "PROJECT_COORDINATOR", "ANNOTATOR"]),
  async<PERSON>andler(leaveController.requestLeave)
);

router.post(
  "/approve/:requestId",
  authMiddleware,
  hasMultipleRole(["ADMIN", "PROJECT_COORDINATOR", "CLIENT"]),
  async<PERSON>andler(leaveController.approveLeave)
);

router.post(
  "/reject/:requestId",
  authMiddleware,
  hasMultipleRole(["ADMIN", "PROJECT_COORDINATOR", "CLIENT"]),
  as<PERSON><PERSON><PERSON><PERSON>(leaveController.rejectLeave)
);


router.get(
  "/pending",
  authMiddleware,
  hasMultipleRole(["ADMIN", "PROJECT_COORDINATOR", "CLIENT"]),
  asyncHandler(leaveController.getPendingLeaveRequests)
);

router.get(
  "/history/:annotatorId",
  authMiddleware,
  asyncHandler(leaveController.getAnnotatorLeaveHistory)
);

router.get(
  "/all-leave-requests",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(leaveController.getAllLeaveRequests)
);

router.get(
  "/leave-requests",
  authMiddleware,
  hasMultipleRole(["CLIENT","ANNOTATOR", "PROJECT_COORDINATOR", "COWORKER"]),
  asyncHandler(leaveController.getRelevantLeaveRequests)
);

export default router;