/**
 * Test script to verify the /me endpoint plan flag functionality
 * This script tests different user scenarios and their plan status
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

class MeEndpointTestSuite {
  constructor() {
    this.testResults = [];
    this.testUsers = [];
  }

  async runTests() {
    console.log('🧪 Testing /me Endpoint Plan Flag...\n');

    try {
      // Test 1: CLIENT user with active subscription
      await this.testClientWithActivePlan();

      // Test 2: CLIENT user without subscription
      await this.testClientWithoutPlan();

      // Test 3: ADMIN user (should have plan = false)
      await this.testAdminUser();

      // Test 4: COWORKER user (should have plan = false)
      await this.testCoworkerUser();

      // Test 5: ANNOTATOR user (should have plan = false)
      await this.testAnnotatorUser();

      // Display results
      this.displayResults();

    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
    } finally {
      // Cleanup test data
      await this.cleanup();
      await prisma.$disconnect();
    }
  }

  async testClientWithActivePlan() {
    console.log('📝 Test 1: CLIENT user with active subscription...');

    try {
      // Create test package
      const testPackage = await prisma.package.create({
        data: {
          name: `Test Package ${Date.now()}`,
          description: 'Test package for plan testing',
          price: 99.99,
          billingType: 'MONTHLY',
          currency: 'USD'
        }
      });

      // Create CLIENT user
      const hashedPassword = await bcrypt.hash('testpassword123', 10);
      const clientUser = await prisma.user.create({
        data: {
          name: 'Test Client',
          email: `client-${Date.now()}@example.com`,
          passwordHash: hashedPassword,
          role: 'CLIENT',
          emailVerified: new Date(),
        },
      });

      // Create active subscription
      await prisma.subscription.create({
        data: {
          userId: clientUser.id,
          packageId: testPackage.id,
          status: 'ACTIVE',
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        }
      });

      // Test getUserById method
      const { ClientAuthService } = require('./src/services/auth.service.ts');
      const authService = new ClientAuthService();
      const result = await authService.getUserById(clientUser.id);

      if (result.plan === true) {
        this.addResult('✅ CLIENT with active subscription has plan = true', 'PASS');
      } else {
        this.addResult('❌ CLIENT with active subscription should have plan = true', 'FAIL', `Got plan = ${result.plan}`);
      }

      this.testUsers.push({ id: clientUser.id, packageId: testPackage.id });

    } catch (error) {
      this.addResult('❌ CLIENT with active subscription test failed', 'FAIL', error.message);
    }

    console.log('');
  }

  async testClientWithoutPlan() {
    console.log('📝 Test 2: CLIENT user without subscription...');

    try {
      // Create CLIENT user without subscription
      const hashedPassword = await bcrypt.hash('testpassword123', 10);
      const clientUser = await prisma.user.create({
        data: {
          name: 'Test Client No Plan',
          email: `client-noplan-${Date.now()}@example.com`,
          passwordHash: hashedPassword,
          role: 'CLIENT',
          emailVerified: new Date(),
        },
      });

      // Test getUserById method
      const { ClientAuthService } = require('./src/services/auth.service.ts');
      const authService = new ClientAuthService();
      const result = await authService.getUserById(clientUser.id);

      if (result.plan === false) {
        this.addResult('✅ CLIENT without subscription has plan = false', 'PASS');
      } else {
        this.addResult('❌ CLIENT without subscription should have plan = false', 'FAIL', `Got plan = ${result.plan}`);
      }

      this.testUsers.push({ id: clientUser.id });

    } catch (error) {
      this.addResult('❌ CLIENT without subscription test failed', 'FAIL', error.message);
    }

    console.log('');
  }

  async testAdminUser() {
    console.log('📝 Test 3: ADMIN user...');

    try {
      // Create ADMIN user
      const hashedPassword = await bcrypt.hash('testpassword123', 10);
      const adminUser = await prisma.user.create({
        data: {
          name: 'Test Admin',
          email: `admin-${Date.now()}@example.com`,
          passwordHash: hashedPassword,
          role: 'ADMIN',
          emailVerified: new Date(),
        },
      });

      // Test getUserById method
      const { ClientAuthService } = require('./src/services/auth.service.ts');
      const authService = new ClientAuthService();
      const result = await authService.getUserById(adminUser.id);

      if (result.plan === false) {
        this.addResult('✅ ADMIN user has plan = false', 'PASS');
      } else {
        this.addResult('❌ ADMIN user should have plan = false', 'FAIL', `Got plan = ${result.plan}`);
      }

      this.testUsers.push({ id: adminUser.id });

    } catch (error) {
      this.addResult('❌ ADMIN user test failed', 'FAIL', error.message);
    }

    console.log('');
  }

  async testCoworkerUser() {
    console.log('📝 Test 4: COWORKER user...');

    try {
      // Create COWORKER user
      const hashedPassword = await bcrypt.hash('testpassword123', 10);
      const coworkerUser = await prisma.user.create({
        data: {
          name: 'Test Coworker',
          email: `coworker-${Date.now()}@example.com`,
          passwordHash: hashedPassword,
          role: 'COWORKER',
          emailVerified: new Date(),
        },
      });

      // Test getUserById method
      const { ClientAuthService } = require('./src/services/auth.service.ts');
      const authService = new ClientAuthService();
      const result = await authService.getUserById(coworkerUser.id);

      if (result.plan === false) {
        this.addResult('✅ COWORKER user has plan = false', 'PASS');
      } else {
        this.addResult('❌ COWORKER user should have plan = false', 'FAIL', `Got plan = ${result.plan}`);
      }

      this.testUsers.push({ id: coworkerUser.id });

    } catch (error) {
      this.addResult('❌ COWORKER user test failed', 'FAIL', error.message);
    }

    console.log('');
  }

  async testAnnotatorUser() {
    console.log('📝 Test 5: ANNOTATOR user...');

    try {
      // Create ANNOTATOR user
      const hashedPassword = await bcrypt.hash('testpassword123', 10);
      const annotatorUser = await prisma.user.create({
        data: {
          name: 'Test Annotator',
          email: `annotator-${Date.now()}@example.com`,
          passwordHash: hashedPassword,
          role: 'ANNOTATOR',
          emailVerified: new Date(),
        },
      });

      // Test getUserById method
      const { ClientAuthService } = require('./src/services/auth.service.ts');
      const authService = new ClientAuthService();
      const result = await authService.getUserById(annotatorUser.id);

      if (result.plan === false) {
        this.addResult('✅ ANNOTATOR user has plan = false', 'PASS');
      } else {
        this.addResult('❌ ANNOTATOR user should have plan = false', 'FAIL', `Got plan = ${result.plan}`);
      }

      this.testUsers.push({ id: annotatorUser.id });

    } catch (error) {
      this.addResult('❌ ANNOTATOR user test failed', 'FAIL', error.message);
    }

    console.log('');
  }

  async cleanup() {
    console.log('🧹 Cleaning up test data...');
    
    try {
      // Delete test subscriptions
      await prisma.subscription.deleteMany({
        where: {
          userId: {
            in: this.testUsers.map(u => u.id)
          }
        }
      });

      // Delete test users
      await prisma.user.deleteMany({
        where: {
          id: {
            in: this.testUsers.map(u => u.id)
          }
        }
      });

      // Delete test packages
      const packageIds = this.testUsers.filter(u => u.packageId).map(u => u.packageId);
      if (packageIds.length > 0) {
        await prisma.package.deleteMany({
          where: {
            id: {
              in: packageIds
            }
          }
        });
      }
      
      console.log('   Test data cleaned up successfully');
    } catch (error) {
      console.warn('   Warning: Failed to cleanup test data:', error.message);
    }
  }

  addResult(test, status, details = '') {
    this.testResults.push({ test, status, details });
  }

  displayResults() {
    console.log('📋 Test Results Summary:');
    console.log('=' .repeat(60));

    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;

    this.testResults.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${icon} ${result.test}`);
      if (result.details) {
        console.log(`   └─ ${result.details}`);
      }
    });

    console.log('=' .repeat(60));
    console.log(`📊 Summary: ${passed} passed, ${failed} failed`);
    
    if (failed === 0) {
      console.log('🎉 All tests passed! /me endpoint plan flag is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please review the implementation.');
    }

    console.log('\n📝 Implementation Summary:');
    console.log('✅ CLIENT users: plan = true if they have ACTIVE or PENDING subscriptions');
    console.log('✅ All other roles (ADMIN, COWORKER, ANNOTATOR, PROJECT_COORDINATOR): plan = false');
    console.log('✅ Subscription expiry check: endDate must be null or in the future');
    console.log('✅ Error handling: defaults to plan = false on errors');
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  const testSuite = new MeEndpointTestSuite();
  testSuite.runTests().catch(console.error);
}

module.exports = MeEndpointTestSuite;