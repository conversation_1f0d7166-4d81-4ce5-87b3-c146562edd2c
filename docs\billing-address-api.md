# Billing Address API

This API allows clients to manage their billing addresses for payment processing.

## Endpoints

### Get User Billing Address
```
GET /api/auth/getaddress
```

**Description**: Retrieves the billing address for the authenticated user.

**Authentication**: Required (CLIENT role)

**Response**:
- Success (200): Returns the billing address data
- Success (200) with null data: No billing address found for the user

**Example Response**:
```json
{
  "success": true,
  "message": "Billing address fetched successfully",
  "data": {
    "id": "clx1234567890",
    "country": "United States",
    "address": "123 Main Street, Apt 4B",
    "state": "California",
    "postalCodel": "90210",
    "street": "Main Street",
    "user": {
      "id": "clx0987654321",
      "name": "<PERSON>",
      "email": "<EMAIL>"
    }
  }
}
```

**Example Response (No Address)**:
```json
{
  "success": true,
  "message": "No billing address found",
  "data": null
}
```

### Create Billing Address
```
POST /api/auth/address
```

**Description**: Creates a new billing address for the authenticated user.

**Authentication**: Required (CLIENT role)

**Request Body**:
```json
{
  "country": "United States",
  "address": "123 Main Street, Apt 4B",
  "state": "California",
  "postalCodel": "90210",
  "street": "Main Street"
}
```

**Required Fields**:
- `country` (string): Country name
- `address` (string): Full address line
- `state` (string): State or province
- `postalCodel` (string): Postal/ZIP code
- `street` (string): Street name

**Response**:
- Success (200): Returns the created billing address
- Error (400): Validation error for missing fields
- Error (401): Unauthorized

### Update Billing Address
```
PUT /api/auth/address
```

**Description**: Updates the existing billing address for the authenticated user. If no address exists, creates a new one.

**Authentication**: Required (CLIENT role)

**Request Body**: Same as POST request

**Response**: Same as POST request

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "message": "All address fields are required: country, address, state, postalCodel, street"
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Unauthorized"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Failed to fetch billing address"
}
```

## Database Schema

The billing address is stored in the `BillingAddress` table with the following structure:

```sql
model BillingAddress {
  id          String @id @default(cuid())
  userId      String @unique
  user        User   @relation(fields: [userId], references: [id])
  country     String
  address     String
  state       String
  postalCodel String
  street      String
}
```

**Note**: There's a typo in the schema where `postalCodel` should be `postalCode`. The API maintains consistency with the existing schema.

## Usage Examples

### Using cURL

**Get billing address:**
```bash
curl -X GET "http://localhost:3000/api/auth/getaddress" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Create billing address:**
```bash
curl -X POST "http://localhost:3000/api/auth/address" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "country": "United States",
    "address": "123 Main Street, Apt 4B",
    "state": "California",
    "postalCodel": "90210",
    "street": "Main Street"
  }'
```

**Update billing address:**
```bash
curl -X PUT "http://localhost:3000/api/auth/address" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "country": "Canada",
    "address": "456 Oak Avenue, Suite 10",
    "state": "Ontario",
    "postalCodel": "K1A 0A6",
    "street": "Oak Avenue"
  }'
```

### Using JavaScript/Axios

```javascript
const axios = require('axios');

const headers = {
  'Authorization': 'Bearer YOUR_JWT_TOKEN',
  'Content-Type': 'application/json'
};

// Get billing address
const getAddress = async () => {
  try {
    const response = await axios.get('/api/auth/getaddress', { headers });
    console.log('Address:', response.data);
  } catch (error) {
    console.error('Error:', error.response.data);
  }
};

// Create/Update billing address
const saveAddress = async () => {
  const addressData = {
    country: 'United States',
    address: '123 Main Street, Apt 4B',
    state: 'California',
    postalCodel: '90210',
    street: 'Main Street'
  };

  try {
    const response = await axios.post('/api/auth/address', addressData, { headers });
    console.log('Saved:', response.data);
  } catch (error) {
    console.error('Error:', error.response.data);
  }
};
```

## Security Features

- **Authentication Required**: All endpoints require valid JWT authentication
- **Role-based Access**: Only users with CLIENT role can access these endpoints
- **User Isolation**: Users can only access and modify their own billing addresses
- **Input Validation**: All required fields are validated before processing

## Testing

Use the provided test script to validate the API:

```bash
# Set environment variables
export TEST_AUTH_TOKEN="your_jwt_token"
export API_BASE_URL="http://localhost:3000"

# Run the test
node test-billing-address.js
```

## Integration with Payment Systems

The billing address is used by:
- Payment processing systems (Dodo, PayPal)
- Invoice generation
- Tax calculation
- Fraud prevention

## Notes

1. **Unique Constraint**: Each user can have only one billing address
2. **Upsert Operation**: The create/update endpoints use upsert logic - if an address exists, it's updated; otherwise, a new one is created
3. **Schema Typo**: The field `postalCodel` contains a typo but is maintained for database consistency
4. **Required Fields**: All address fields are required and validated