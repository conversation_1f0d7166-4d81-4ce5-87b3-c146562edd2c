{"info": {"name": "Zoho Desk API Collection", "description": "Complete collection for testing Zoho Desk ticket management integration", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:3000/api", "type": "string"}, {"key": "zoho_desk_base", "value": "{{base_url}}/zoho-desk", "type": "string"}], "item": [{"name": "🔐 Authentication", "item": [{"name": "1. Initiate OAuth Flow", "request": {"method": "GET", "header": [], "url": {"raw": "{{zoho_desk_base}}/auth/initiate", "host": ["{{zoho_desk_base}}"], "path": ["auth", "initiate"]}, "description": "Start the OAuth flow - this will return a URL to visit for authorization"}, "response": []}, {"name": "2. <PERSON><PERSON><PERSON> (Auto-handled)", "request": {"method": "GET", "header": [], "url": {"raw": "{{zoho_desk_base}}/auth/callback?code=AUTHORIZATION_CODE&state=desk_auth", "host": ["{{zoho_desk_base}}"], "path": ["auth", "callback"], "query": [{"key": "code", "value": "AUTHORIZATION_CODE", "description": "Replace with actual authorization code"}, {"key": "state", "value": "desk_auth"}]}, "description": "This endpoint handles the OAuth callback automatically"}, "response": []}], "description": "OAuth authentication endpoints"}, {"name": "🎫 Ticket Management", "item": [{"name": "Create Ticket", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"subject\": \"Login Issue - Cannot Access Dashboard\",\n  \"description\": \"User is unable to login to the system. Getting 'Invalid credentials' error even with correct password.\",\n  \"priority\": \"High\",\n  \"status\": \"Open\",\n  \"category\": \"Technical Support\",\n  \"subCategory\": \"Login Issues\",\n  \"tags\": [\"login\", \"urgent\", \"dashboard\"],\n  \"customFields\": {\n    \"cf_user_type\": \"Premium\",\n    \"cf_browser\": \"Chrome\"\n  }\n}"}, "url": {"raw": "{{zoho_desk_base}}/tickets", "host": ["{{zoho_desk_base}}"], "path": ["tickets"]}, "description": "Create a new support ticket"}, "response": []}, {"name": "List All Tickets", "request": {"method": "GET", "header": [], "url": {"raw": "{{zoho_desk_base}}/tickets", "host": ["{{zoho_desk_base}}"], "path": ["tickets"]}, "description": "Get all tickets without filters"}, "response": []}, {"name": "List Tickets with Filters", "request": {"method": "GET", "header": [], "url": {"raw": "{{zoho_desk_base}}/tickets?status=Open&priority=High&limit=10&sortBy=createdTime", "host": ["{{zoho_desk_base}}"], "path": ["tickets"], "query": [{"key": "status", "value": "Open", "description": "Filter by status"}, {"key": "priority", "value": "High", "description": "Filter by priority"}, {"key": "limit", "value": "10", "description": "Number of tickets to return"}, {"key": "sortBy", "value": "createdTime", "description": "Sort field"}]}, "description": "Get tickets with specific filters applied"}, "response": []}, {"name": "Get Specific Ticket", "request": {"method": "GET", "header": [], "url": {"raw": "{{zoho_desk_base}}/tickets/{{ticket_id}}", "host": ["{{zoho_desk_base}}"], "path": ["tickets", "{{ticket_id}}"], "variable": [{"key": "ticket_id", "value": "123456789", "description": "Replace with actual ticket ID"}]}, "description": "Get details of a specific ticket"}, "response": []}, {"name": "Update Ticket", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"In Progress\",\n  \"priority\": \"Medium\",\n  \"assigneeId\": \"agent123\",\n  \"tags\": [\"login\", \"resolved\", \"dashboard\"]\n}"}, "url": {"raw": "{{zoho_desk_base}}/tickets/{{ticket_id}}", "host": ["{{zoho_desk_base}}"], "path": ["tickets", "{{ticket_id}}"], "variable": [{"key": "ticket_id", "value": "123456789", "description": "Replace with actual ticket ID"}]}, "description": "Update an existing ticket"}, "response": []}, {"name": "Delete Ticket", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{zoho_desk_base}}/tickets/{{ticket_id}}", "host": ["{{zoho_desk_base}}"], "path": ["tickets", "{{ticket_id}}"], "variable": [{"key": "ticket_id", "value": "123456789", "description": "Replace with actual ticket ID"}]}, "description": "Delete a specific ticket"}, "response": []}], "description": "All ticket-related operations"}, {"name": "💬 Comments", "item": [{"name": "Add Comment to Ticket", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"content\": \"I've investigated this issue and found that it's related to a recent password policy update. The user needs to reset their password.\",\n  \"contentType\": \"plainText\",\n  \"isPublic\": true\n}"}, "url": {"raw": "{{zoho_desk_base}}/tickets/{{ticket_id}}/comments", "host": ["{{zoho_desk_base}}"], "path": ["tickets", "{{ticket_id}}", "comments"], "variable": [{"key": "ticket_id", "value": "123456789", "description": "Replace with actual ticket ID"}]}, "description": "Add a comment to a specific ticket"}, "response": []}, {"name": "Add Private Comment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"content\": \"Internal note: This user has had similar issues before. Check previous tickets for pattern.\",\n  \"contentType\": \"plainText\",\n  \"isPublic\": false\n}"}, "url": {"raw": "{{zoho_desk_base}}/tickets/{{ticket_id}}/comments", "host": ["{{zoho_desk_base}}"], "path": ["tickets", "{{ticket_id}}", "comments"], "variable": [{"key": "ticket_id", "value": "123456789", "description": "Replace with actual ticket ID"}]}, "description": "Add a private comment (internal note) to a ticket"}, "response": []}, {"name": "Get Ticket Comments", "request": {"method": "GET", "header": [], "url": {"raw": "{{zoho_desk_base}}/tickets/{{ticket_id}}/comments", "host": ["{{zoho_desk_base}}"], "path": ["tickets", "{{ticket_id}}", "comments"], "variable": [{"key": "ticket_id", "value": "123456789", "description": "Replace with actual ticket ID"}]}, "description": "Get all comments for a specific ticket"}, "response": []}], "description": "Comment management for tickets"}, {"name": "📎 Attachments", "item": [{"name": "Upload Attachment", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Select a file to upload"}]}, "url": {"raw": "{{zoho_desk_base}}/tickets/{{ticket_id}}/attachments", "host": ["{{zoho_desk_base}}"], "path": ["tickets", "{{ticket_id}}", "attachments"], "variable": [{"key": "ticket_id", "value": "123456789", "description": "Replace with actual ticket ID"}]}, "description": "Upload a file attachment to a ticket"}, "response": []}], "description": "File attachment operations"}, {"name": "👥 Contacts", "item": [{"name": "Create Contact", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Doe\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"******-123-4567\",\n  \"mobile\": \"******-987-6543\",\n  \"description\": \"Premium customer - priority support\"\n}"}, "url": {"raw": "{{zoho_desk_base}}/contacts", "host": ["{{zoho_desk_base}}"], "path": ["contacts"]}, "description": "Create a new contact in Zoho Desk"}, "response": []}, {"name": "Search Contacts by Email", "request": {"method": "GET", "header": [], "url": {"raw": "{{zoho_desk_base}}/contacts?email=<EMAIL>", "host": ["{{zoho_desk_base}}"], "path": ["contacts"], "query": [{"key": "email", "value": "<EMAIL>", "description": "Email to search for"}]}, "description": "Search for contacts by email address"}, "response": []}, {"name": "Get Specific Contact", "request": {"method": "GET", "header": [], "url": {"raw": "{{zoho_desk_base}}/contacts/{{contact_id}}", "host": ["{{zoho_desk_base}}"], "path": ["contacts", "{{contact_id}}"], "variable": [{"key": "contact_id", "value": "123456789", "description": "Replace with actual contact ID"}]}, "description": "Get details of a specific contact"}, "response": []}], "description": "Contact management operations"}, {"name": "🏢 Utilities", "item": [{"name": "Get Departments", "request": {"method": "GET", "header": [], "url": {"raw": "{{zoho_desk_base}}/departments", "host": ["{{zoho_desk_base}}"], "path": ["departments"]}, "description": "Get all departments in your Zoho Desk"}, "response": []}, {"name": "Get Agents", "request": {"method": "GET", "header": [], "url": {"raw": "{{zoho_desk_base}}/agents", "host": ["{{zoho_desk_base}}"], "path": ["agents"]}, "description": "Get all agents/users in your Zoho Desk"}, "response": []}], "description": "Utility endpoints for getting system information"}, {"name": "🧪 Test Scenarios", "item": [{"name": "Complete Ticket Workflow", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"subject\": \"Test Ticket - Complete Workflow\",\n  \"description\": \"This is a test ticket to verify the complete workflow: Create → Comment → Update → Close\",\n  \"priority\": \"Medium\",\n  \"status\": \"Open\",\n  \"category\": \"General\",\n  \"tags\": [\"test\", \"workflow\"]\n}"}, "url": {"raw": "{{zoho_desk_base}}/tickets", "host": ["{{zoho_desk_base}}"], "path": ["tickets"]}, "description": "Create a test ticket for workflow testing"}, "response": []}, {"name": "Bulk Ticket Creation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"subject\": \"Bulk Test Ticket {{$randomInt}}\",\n  \"description\": \"This is test ticket number {{$randomInt}} for bulk testing\",\n  \"priority\": \"{{$randomArrayElement(['Low', 'Medium', 'High'])}\",\n  \"status\": \"Open\",\n  \"category\": \"Testing\",\n  \"tags\": [\"bulk\", \"test\", \"{{$randomWord}}\"]\n}"}, "url": {"raw": "{{zoho_desk_base}}/tickets", "host": ["{{zoho_desk_base}}"], "path": ["tickets"]}, "description": "Create multiple test tickets with random data"}, "response": []}], "description": "Test scenarios and workflows"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set variables if needed", "if (!pm.environment.get('base_url')) {", "    pm.environment.set('base_url', 'http://localhost:3000/api');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Auto-extract ticket ID from create responses", "if (pm.response.code === 200 || pm.response.code === 201) {", "    try {", "        const response = pm.response.json();", "        if (response.data && response.data.id) {", "            pm.environment.set('ticket_id', response.data.id);", "            console.log('Ticket ID saved:', response.data.id);", "        }", "        if (response.data && response.data.contactId) {", "            pm.environment.set('contact_id', response.data.contactId);", "            console.log('Contact ID saved:', response.data.contactId);", "        }", "    } catch (e) {", "        console.log('Could not parse response for ID extraction');", "    }", "}"]}}]}