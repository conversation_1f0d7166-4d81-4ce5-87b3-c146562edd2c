import { Router } from "express";
import TaskController from "../../controllers/tasks/task.controller";
import { asyncHand<PERSON> } from "../../middlewares/asyncHandler";
import authMiddleware from "../../middlewares/checkAuth";
import { hasRole } from "../../middlewares/checkRole";
import { subscriptionMiddleware } from "../../middlewares/subscription.middleware";

const router = Router();
const controller = TaskController;

router.post(
  "/create-task/:projectId",
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  asyncHandler(controller.create.bind(controller))
);
router.post(
  "/cooworker/create-task/:projectId",
  authMiddleware,
  hasRole("COOWORKER"),
  subscriptionMiddleware.requireActiveSubscription,
  asyncHandler(controller.create.bind(controller))
);

router.get("/get-all-tasks", asyncHandler(controller.getAll.bind(controller)));
router.get(
  "/cooworker/get-all-tasks",
  asyncHandler(controller.getCooworkerAll.bind(controller))
);

router.get(
  "/task-by-id/:id",
  async<PERSON>and<PERSON>(controller.getById.bind(controller))
);

router.put(
  "/update-task/:id",
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  asyncHandler(controller.update.bind(controller))
);

router.delete(
  "/delete-task/:id",
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  asyncHandler(controller.delete.bind(controller))
);
router.get(
  "/tasks-by-status",
  authMiddleware,
  asyncHandler(controller.getByStatus.bind(controller))
);
router.get(
  "/project-stats/:projectId", 
  asyncHandler(controller.getProjectStats.bind(controller))
);


export default router;
