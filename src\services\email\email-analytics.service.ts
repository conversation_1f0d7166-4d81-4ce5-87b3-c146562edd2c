import prisma from "../../prisma";

export interface EmailMetrics {
  id: string;
  to: string;
  subject: string;
  template: string | null;
  status: 'SENT' | 'DELIVERED' | 'OPENED' | 'CLICKED' | 'FAILED' | 'BOUNCED';
  sentAt: Date;
  deliveredAt?: Date;
  openedAt?: Date;
  clickedAt?: Date;
  failureReason?: string;
  metadata?: any;
}

export interface EmailAnalytics {
  totalSent: number;
  totalDelivered: number;
  totalOpened: number;
  totalClicked: number;
  totalFailed: number;
  deliveryRate: number;
  openRate: number;
  clickRate: number;
  bounceRate: number;
}

export class EmailAnalyticsService {
  /**
   * Log email sending attempt
   */
  async logEmailSent(data: {
    to: string;
    subject: string;
    template: string;
    metadata?: any;
  }): Promise<string> {
    try {
      const emailLog = await prisma.emailLog.create({
        data: {
          to: data.to,
          subject: data.subject,
          template: data.template,
          status: 'SENT',
          sentAt: new Date(),
          metadata: data.metadata,
        },
      });
      return emailLog.id;
    } catch (error) {
      console.error('Error logging email sent:', error);
      throw error;
    }
  }

  /**
   * Update email delivery status
   */
  async updateEmailStatus(
    emailId: string,
    status: EmailMetrics['status'],
    additionalData?: {
      deliveredAt?: Date;
      openedAt?: Date;
      clickedAt?: Date;
      failureReason?: string;
    }
  ): Promise<void> {
    try {
      await prisma.emailLog.update({
        where: { id: emailId },
        data: {
          status,
          ...additionalData,
        },
      });
    } catch (error) {
      console.error('Error updating email status:', error);
      throw error;
    }
  }

  /**
   * Track email open
   */
  async trackEmailOpen(emailId: string): Promise<void> {
    await this.updateEmailStatus(emailId, 'OPENED', {
      openedAt: new Date(),
    });
  }

  /**
   * Track email click
   */
  async trackEmailClick(emailId: string): Promise<void> {
    await this.updateEmailStatus(emailId, 'CLICKED', {
      clickedAt: new Date(),
    });
  }

  /**
   * Mark email as delivered
   */
  async markEmailDelivered(emailId: string): Promise<void> {
    await this.updateEmailStatus(emailId, 'DELIVERED', {
      deliveredAt: new Date(),
    });
  }

  /**
   * Mark email as failed
   */
  async markEmailFailed(emailId: string, reason: string): Promise<void> {
    await this.updateEmailStatus(emailId, 'FAILED', {
      failureReason: reason,
    });
  }

  /**
   * Get email analytics for a date range
   */
  async getEmailAnalytics(
    startDate: Date,
    endDate: Date,
    template?: string | null
  ): Promise<EmailAnalytics> {
    try {
      const whereClause: any = {
        sentAt: {
          gte: startDate,
          lte: endDate,
        },
      };

      if (template) {
        whereClause.template = template;
      }

      const [
        totalSent,
        totalDelivered,
        totalOpened,
        totalClicked,
        totalFailed,
      ] = await Promise.all([
        prisma.emailLog.count({ where: whereClause }),
        prisma.emailLog.count({
          where: { ...whereClause, status: { in: ['DELIVERED', 'OPENED', 'CLICKED'] } },
        }),
        prisma.emailLog.count({
          where: { ...whereClause, status: { in: ['OPENED', 'CLICKED'] } },
        }),
        prisma.emailLog.count({
          where: { ...whereClause, status: 'CLICKED' },
        }),
        prisma.emailLog.count({
          where: { ...whereClause, status: { in: ['FAILED', 'BOUNCED'] } },
        }),
      ]);

      const deliveryRate = totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0;
      const openRate = totalDelivered > 0 ? (totalOpened / totalDelivered) * 100 : 0;
      const clickRate = totalOpened > 0 ? (totalClicked / totalOpened) * 100 : 0;
      const bounceRate = totalSent > 0 ? (totalFailed / totalSent) * 100 : 0;

      return {
        totalSent,
        totalDelivered,
        totalOpened,
        totalClicked,
        totalFailed,
        deliveryRate: Math.round(deliveryRate * 100) / 100,
        openRate: Math.round(openRate * 100) / 100,
        clickRate: Math.round(clickRate * 100) / 100,
        bounceRate: Math.round(bounceRate * 100) / 100,
      };
    } catch (error) {
      console.error('Error getting email analytics:', error);
      throw error;
    }
  }

  /**
   * Get template performance analytics
   */
  async getTemplateAnalytics(
    startDate: Date,
    endDate: Date
  ): Promise<Array<EmailAnalytics & { template: string | null }>> {
    try {
      const templates = await prisma.emailLog.findMany({
        where: {
          sentAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: { template: true },
        distinct: ['template'],
      });

      const analytics = await Promise.all(
        templates.map(async ({ template }) => {
          const templateAnalytics = await this.getEmailAnalytics(
            startDate,
            endDate,
            template
          );
          return { ...templateAnalytics, template };
        })
      );

      return analytics.sort((a, b) => b.totalSent - a.totalSent);
    } catch (error) {
      console.error('Error getting template analytics:', error);
      throw error;
    }
  }

  /**
   * Get recent email activity
   */
  async getRecentEmailActivity(limit: number = 50): Promise<EmailMetrics[]> {
    try {
      const emails = await prisma.emailLog.findMany({
        orderBy: { sentAt: 'desc' },
        take: limit,
      });

      return emails.map(email => ({
        id: email.id,
        to: email.to,
        subject: email.subject,
        template: email.template,
        status: email.status as EmailMetrics['status'],
        sentAt: email.sentAt,
        deliveredAt: email.deliveredAt || undefined,
        openedAt: email.openedAt || undefined,
        clickedAt: email.clickedAt || undefined,
        failureReason: email.failureReason || undefined,
        metadata: email.metadata,
      }));
    } catch (error) {
      console.error('Error getting recent email activity:', error);
      throw error;
    }
  }

  /**
   * Get email performance by user
   */
  async getUserEmailMetrics(
    userEmail: string,
    startDate: Date,
    endDate: Date
  ): Promise<EmailAnalytics> {
    return this.getEmailAnalytics(startDate, endDate);
  }

  /**
   * Clean up old email logs
   */
  async cleanupOldLogs(daysToKeep: number = 90): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const result = await prisma.emailLog.deleteMany({
        where: {
          sentAt: {
            lt: cutoffDate,
          },
        },
      });

      console.log(`🧹 Cleaned up ${result.count} old email logs`);
      return result.count;
    } catch (error) {
      console.error('Error cleaning up old email logs:', error);
      throw error;
    }
  }

  /**
   * Generate email tracking pixel URL
   */
  generateTrackingPixelUrl(emailId: string): string {
    return `${process.env.BACKEND_URL || 'http://localhost:3000'}/api/email/track/open/${emailId}`;
  }

  /**
   * Generate click tracking URL
   */
  generateClickTrackingUrl(emailId: string, originalUrl: string): string {
    const encodedUrl = encodeURIComponent(originalUrl);
    return `${process.env.BACKEND_URL || 'http://localhost:3000'}/api/email/track/click/${emailId}?url=${encodedUrl}`;
  }
}

export default EmailAnalyticsService;