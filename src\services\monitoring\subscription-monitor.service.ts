import prisma from "../../prisma";
import { SubscriptionStatus } from "@prisma/client";
import { NotificationService } from "../notification/notification.service";
import { EmailService } from "../email/email.service";
import { EnhancedEmailService } from "../email/enhanced-email.service";
import { SubscriptionService } from "../subscription/subscription.service";

export interface SubscriptionAlert {
  type: 'EXPIRING_SOON' | 'EXPIRED' | 'PAYMENT_FAILED' | 'USAGE_LIMIT' | 'FEATURE_BLOCKED';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  userId: string;
  subscriptionId?: string;
  message: string;
  data?: any;
  createdAt: Date;
}

export interface MonitoringConfig {
  expiryWarningDays: number[];
  checkIntervalMinutes: number;
  enableEmailNotifications: boolean;
  enableInAppNotifications: boolean;
  enableSlackNotifications: boolean;
  adminEmails: string[];
}

export class SubscriptionMonitorService {
  private subscriptionService: SubscriptionService;
  private notificationService: NotificationService;
  private emailService: EmailService;
  private enhancedEmailService: EnhancedEmailService;
  private config: MonitoringConfig;
  private monitoringInterval?: NodeJS.Timeout;

  constructor() {
    this.subscriptionService = new SubscriptionService();
    this.notificationService = new NotificationService();
    this.emailService = new EmailService();
    this.enhancedEmailService = new EnhancedEmailService();
    
    // Default configuration
    this.config = {
      expiryWarningDays: [30, 14, 7, 3, 1], // Days before expiry to send warnings
      checkIntervalMinutes: 60, // Check every hour
      enableEmailNotifications: true,
      enableInAppNotifications: true,
      enableSlackNotifications: false,
      adminEmails: process.env.ADMIN_EMAILS?.split(',') || []
    };
  }

  /**
   * Start automated monitoring
   */
  startMonitoring(): void {
    console.log('🔍 Starting subscription monitoring service...');
    
    // Initial check
    this.performMonitoringCheck();
    
    // Set up recurring checks
    this.monitoringInterval = setInterval(() => {
      this.performMonitoringCheck();
    }, this.config.checkIntervalMinutes * 60 * 1000);

    console.log(`✅ Subscription monitoring started (checking every ${this.config.checkIntervalMinutes} minutes)`);
  }

  /**
   * Stop automated monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
      console.log('🛑 Subscription monitoring stopped');
    }
  }

  /**
   * Perform comprehensive monitoring check
   */
  async performMonitoringCheck(): Promise<void> {
    try {
      console.log('🔍 Performing subscription monitoring check...');
      
      const startTime = Date.now();
      
      // Run all monitoring tasks
      await Promise.all([
        this.checkExpiringSubscriptions(),
        this.checkExpiredSubscriptions(),
        this.checkPaymentFailures(),
        this.checkUsageLimits(),
        this.updateSubscriptionStatuses(),
        this.cleanupOldAlerts()
      ]);

      const duration = Date.now() - startTime;
      console.log(`✅ Monitoring check completed in ${duration}ms`);

    } catch (error) {
      console.error('❌ Error during monitoring check:', error);
      await this.sendAdminAlert('MONITORING_ERROR', {
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date()
      });
    }
  }

  /**
   * Check for subscriptions expiring soon
   */
  async checkExpiringSubscriptions(): Promise<void> {
    try {
      const now = new Date();
      
      for (const days of this.config.expiryWarningDays) {
        const warningDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
        const nextDay = new Date(warningDate.getTime() + 24 * 60 * 60 * 1000);
        
        const expiringSubscriptions = await prisma.subscription.findMany({
          where: {
            status: SubscriptionStatus.ACTIVE,
            endDate: {
              gte: warningDate,
              lt: nextDay
            }
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true
              }
            },
            package: {
              select: {
                name: true,
                price: true,
                currency: true
              }
            }
          }
        });

        for (const subscription of expiringSubscriptions) {
          await this.handleExpiringSubscription(subscription, days);
        }
      }
    } catch (error) {
      console.error('Error checking expiring subscriptions:', error);
    }
  }

  /**
   * Check for expired subscriptions
   */
  async checkExpiredSubscriptions(): Promise<void> {
    try {
      const now = new Date();
      
      const expiredSubscriptions = await prisma.subscription.findMany({
        where: {
          status: SubscriptionStatus.ACTIVE,
          endDate: {
            lt: now
          }
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          },
          package: {
            select: {
              name: true,
              price: true,
              currency: true
            }
          }
        }
      });

      for (const subscription of expiredSubscriptions) {
        await this.handleExpiredSubscription(subscription);
      }

      if (expiredSubscriptions.length > 0) {
        console.log(`📋 Processed ${expiredSubscriptions.length} expired subscriptions`);
      }
    } catch (error) {
      console.error('Error checking expired subscriptions:', error);
    }
  }

  /**
   * Check for payment failures
   */
  async checkPaymentFailures(): Promise<void> {
    try {
      const recentFailures = await prisma.payment.findMany({
        where: {
          status: 'FAILED',
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          subscription: {
            include: {
              package: {
                select: {
                  name: true
                }
              }
            }
          }
        }
      });

      for (const payment of recentFailures) {
        await this.handlePaymentFailure(payment);
      }
    } catch (error) {
      console.error('Error checking payment failures:', error);
    }
  }

  /**
   * Check usage limits and feature access
   */
  async checkUsageLimits(): Promise<void> {
    try {
      // Get users with active subscriptions
      const activeUsers = await prisma.user.findMany({
        where: {
          role: 'CLIENT',
          Subscription: {
            some: {
              status: SubscriptionStatus.ACTIVE
            }
          }
        },
        include: {
          Subscription: {
            where: {
              status: SubscriptionStatus.ACTIVE
            },
            include: {
              package: {
                include: {
                  features: {
                    include: {
                      feature: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      for (const user of activeUsers) {
        await this.checkUserUsageLimits(user);
      }
    } catch (error) {
      console.error('Error checking usage limits:', error);
    }
  }

  /**
   * Update subscription statuses
   */
  async updateSubscriptionStatuses(): Promise<void> {
    try {
      const result = await this.subscriptionService.updateExpiredSubscriptions();
      if (result.count > 0) {
        console.log(`📊 Updated ${result.count} expired subscription statuses`);
      }
    } catch (error) {
      console.error('Error updating subscription statuses:', error);
    }
  }

  /**
   * Handle expiring subscription
   */
  private async handleExpiringSubscription(subscription: any, daysUntilExpiry: number): Promise<void> {
    const alert: SubscriptionAlert = {
      type: 'EXPIRING_SOON',
      severity: daysUntilExpiry <= 3 ? 'HIGH' : daysUntilExpiry <= 7 ? 'MEDIUM' : 'LOW',
      userId: subscription.userId,
      subscriptionId: subscription.id,
      message: `Your ${subscription.package.name} subscription expires in ${daysUntilExpiry} day(s)`,
      data: {
        daysUntilExpiry,
        packageName: subscription.package.name,
        endDate: subscription.endDate
      },
      createdAt: new Date()
    };

    await this.processAlert(alert, subscription.user);
  }

  /**
   * Handle expired subscription
   */
  private async handleExpiredSubscription(subscription: any): Promise<void> {
    // Update subscription status
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: { status: SubscriptionStatus.EXPIRED }
    });

    const alert: SubscriptionAlert = {
      type: 'EXPIRED',
      severity: 'CRITICAL',
      userId: subscription.userId,
      subscriptionId: subscription.id,
      message: `Your ${subscription.package.name} subscription has expired`,
      data: {
        packageName: subscription.package.name,
        endDate: subscription.endDate,
        expiredDays: Math.floor((Date.now() - subscription.endDate.getTime()) / (24 * 60 * 60 * 1000))
      },
      createdAt: new Date()
    };

    await this.processAlert(alert, subscription.user);
  }

  /**
   * Handle payment failure
   */
  private async handlePaymentFailure(payment: any): Promise<void> {
    const alert: SubscriptionAlert = {
      type: 'PAYMENT_FAILED',
      severity: 'HIGH',
      userId: payment.userId,
      subscriptionId: payment.subscriptionId,
      message: `Payment failed for ${payment.subscription?.package?.name || 'subscription'}`,
      data: {
        paymentId: payment.id,
        amount: payment.amount,
        currency: payment.currency,
        errorMessage: payment.error_message
      },
      createdAt: new Date()
    };

    await this.processAlert(alert, payment.user);
  }

  /**
   * Check individual user usage limits
   */
  private async checkUserUsageLimits(user: any): Promise<void> {
    try {
      // Get user's current usage
      const [projectCount, taskCount, coworkerCount] = await Promise.all([
        prisma.project.count({ where: { createdById: user.id } }),
        prisma.task.count({ where: { createdById: user.id } }),
        prisma.user.count({ where: { clientOwnerId: user.id } })
      ]);

      // Check against subscription limits (this would be configured per package)
      const limits = this.getSubscriptionLimits(user.Subscription);
      
      if (limits.maxProjects && projectCount >= limits.maxProjects * 0.9) {
        await this.createUsageLimitAlert(user, 'projects', projectCount, limits.maxProjects);
      }
      
      if (limits.maxTasks && taskCount >= limits.maxTasks * 0.9) {
        await this.createUsageLimitAlert(user, 'tasks', taskCount, limits.maxTasks);
      }
      
      if (limits.maxCoworkers && coworkerCount >= limits.maxCoworkers * 0.9) {
        await this.createUsageLimitAlert(user, 'coworkers', coworkerCount, limits.maxCoworkers);
      }
    } catch (error) {
      console.error(`Error checking usage limits for user ${user.id}:`, error);
    }
  }

  /**
   * Get subscription limits based on active subscriptions
   */
  private getSubscriptionLimits(subscriptions: any[]): any {
    // Default limits
    let limits = {
      maxProjects: 10,
      maxTasks: 100,
      maxCoworkers: 5
    };

    // Get the highest limits from all active subscriptions
    subscriptions.forEach(sub => {
      const packageName = sub.package.name.toLowerCase();
      
      if (packageName.includes('premium') || packageName.includes('enterprise')) {
        limits.maxProjects = Math.max(limits.maxProjects, 1000);
        limits.maxTasks = Math.max(limits.maxTasks, 10000);
        limits.maxCoworkers = Math.max(limits.maxCoworkers, 50);
      } else if (packageName.includes('standard')) {
        limits.maxProjects = Math.max(limits.maxProjects, 50);
        limits.maxTasks = Math.max(limits.maxTasks, 1000);
        limits.maxCoworkers = Math.max(limits.maxCoworkers, 15);
      }
    });

    return limits;
  }

  /**
   * Create usage limit alert
   */
  private async createUsageLimitAlert(user: any, resource: string, current: number, limit: number): Promise<void> {
    const alert: SubscriptionAlert = {
      type: 'USAGE_LIMIT',
      severity: current >= limit ? 'CRITICAL' : 'MEDIUM',
      userId: user.id,
      message: `You're approaching your ${resource} limit (${current}/${limit})`,
      data: {
        resource,
        current,
        limit,
        percentage: Math.round((current / limit) * 100)
      },
      createdAt: new Date()
    };

    await this.processAlert(alert, user);
  }

  /**
   * Process alert and send notifications
   */
  private async processAlert(alert: SubscriptionAlert, user: any): Promise<void> {
    try {
      // Store alert in database
      await this.storeAlert(alert);

      // Send notifications based on configuration
      if (this.config.enableInAppNotifications) {
        await this.sendInAppNotification(alert, user);
      }

      if (this.config.enableEmailNotifications) {
        await this.sendEmailNotification(alert, user);
      }

      // Send admin notifications for critical alerts
      if (alert.severity === 'CRITICAL') {
        await this.sendAdminAlert(alert.type, alert);
      }

      console.log(`📢 Alert processed: ${alert.type} for user ${user.email}`);
    } catch (error) {
      console.error('Error processing alert:', error);
    }
  }

  /**
   * Store alert in database
   */
  private async storeAlert(alert: SubscriptionAlert): Promise<void> {
    await prisma.subscriptionAlert.create({
      data: {
        type: alert.type,
        severity: alert.severity,
        userId: alert.userId,
        subscriptionId: alert.subscriptionId,
        message: alert.message,
        data: alert.data,
        isRead: false,
        createdAt: alert.createdAt
      }
    });
  }

  /**
   * Send in-app notification
   */
  private async sendInAppNotification(alert: SubscriptionAlert, user: any): Promise<void> {
    await this.notificationService.createNotification({
      userId: user.id,
      type: 'SUBSCRIPTION_ALERT',
      title: this.getAlertTitle(alert.type),
      message: alert.message,
      data: alert.data,
      priority: alert.severity === 'CRITICAL' ? 'HIGH' : 'MEDIUM'
    });
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(alert: SubscriptionAlert, user: any): Promise<void> {
    const template = this.getEmailTemplate(alert.type);
    
    // Use enhanced email service with proper template data
    await this.enhancedEmailService.sendEmail({
      to: user.email,
      subject: this.getAlertTitle(alert.type),
      template,
      data: {
        firstName: user.name,
        userName: user.name,
        alert,
        dashboardUrl: process.env.FRONTEND_URL + '/dashboard',
        companyName: process.env.COMPANY_NAME || 'Our Platform',
        supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
        // Additional data based on alert type
        ...this.getAlertSpecificData(alert)
      }
    });
  }

  /**
   * Send admin alert
   */
  private async sendAdminAlert(type: string, data: any): Promise<void> {
    for (const adminEmail of this.config.adminEmails) {
      await this.emailService.sendEmail({
        to: adminEmail,
        subject: `[ADMIN ALERT] Subscription ${type}`,
        template: 'admin-alert',
        data: {
          type,
          data,
          timestamp: new Date()
        }
      });
    }
  }

  /**
   * Clean up old alerts
   */
  private async cleanupOldAlerts(): Promise<void> {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    
    const result = await prisma.subscriptionAlert.deleteMany({
      where: {
        createdAt: {
          lt: thirtyDaysAgo
        },
        isRead: true
      }
    });

    if (result.count > 0) {
      console.log(`🧹 Cleaned up ${result.count} old alerts`);
    }
  }

  /**
   * Get alert title based on type
   */
  private getAlertTitle(type: string): string {
    const titles: Record<string, string> = {
      'EXPIRING_SOON': 'Subscription Expiring Soon',
      'EXPIRED': 'Subscription Expired',
      'PAYMENT_FAILED': 'Payment Failed',
      'USAGE_LIMIT': 'Usage Limit Warning',
      'FEATURE_BLOCKED': 'Feature Access Blocked'
    };
    return titles[type] || 'Subscription Alert';
  }

  /**
   * Get email template based on alert type
   */
  private getEmailTemplate(type: string): string {
    const templates: Record<string, string> = {
      'EXPIRING_SOON': 'subscription-expiring',
      'EXPIRED': 'subscription-expired',
      'PAYMENT_FAILED': 'payment-failed',
      'USAGE_LIMIT': 'usage-limit-warning',
      'FEATURE_BLOCKED': 'feature-blocked'
    };
    return templates[type] || 'subscription-alert';
  }

  /**
   * Get alert-specific data for email templates
   */
  private getAlertSpecificData(alert: SubscriptionAlert): Record<string, any> {
    const baseData = {
      alertType: alert.type,
      alertMessage: alert.message,
      alertSeverity: alert.severity,
    };

    switch (alert.type) {
      case 'EXPIRING_SOON':
        return {
          ...baseData,
          daysUntilExpiry: alert.data?.daysUntilExpiry || 0,
          packageName: alert.data?.packageName || 'Your subscription',
          endDate: alert.data?.endDate ? new Date(alert.data.endDate).toLocaleDateString() : '',
        };
      
      case 'EXPIRED':
        return {
          ...baseData,
          packageName: alert.data?.packageName || 'Your subscription',
          endDate: alert.data?.endDate ? new Date(alert.data.endDate).toLocaleDateString() : '',
          expiredDays: alert.data?.expiredDays || 0,
        };
      
      case 'USAGE_LIMIT':
        return {
          ...baseData,
          resource: alert.data?.resource || 'resource',
          current: alert.data?.current || 0,
          limit: alert.data?.limit || 0,
          percentage: alert.data?.percentage || 0,
        };
      
      case 'PAYMENT_FAILED':
        return {
          ...baseData,
          amount: alert.data?.amount || 0,
          currency: alert.data?.currency || 'USD',
          errorMessage: alert.data?.errorMessage || 'Payment processing failed',
        };
      
      default:
        return baseData;
    }
  }

  /**
   * Get monitoring statistics
   */
  async getMonitoringStats(): Promise<any> {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const [
      alertsLast24h,
      alertsLast7d,
      activeSubscriptions,
      expiringSubscriptions,
      expiredSubscriptions
    ] = await Promise.all([
      prisma.subscriptionAlert.count({
        where: { createdAt: { gte: last24Hours } }
      }),
      prisma.subscriptionAlert.count({
        where: { createdAt: { gte: last7Days } }
      }),
      prisma.subscription.count({
        where: { status: SubscriptionStatus.ACTIVE }
      }),
      prisma.subscription.count({
        where: {
          status: SubscriptionStatus.ACTIVE,
          endDate: {
            gte: now,
            lte: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      prisma.subscription.count({
        where: { status: SubscriptionStatus.EXPIRED }
      })
    ]);

    return {
      monitoring: {
        isActive: !!this.monitoringInterval,
        checkInterval: this.config.checkIntervalMinutes,
        lastCheck: new Date()
      },
      alerts: {
        last24Hours: alertsLast24h,
        last7Days: alertsLast7d
      },
      subscriptions: {
        active: activeSubscriptions,
        expiringSoon: expiringSubscriptions,
        expired: expiredSubscriptions
      }
    };
  }

  /**
   * Update monitoring configuration
   */
  updateConfig(newConfig: Partial<MonitoringConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('📝 Monitoring configuration updated');
  }
}

export default SubscriptionMonitorService;