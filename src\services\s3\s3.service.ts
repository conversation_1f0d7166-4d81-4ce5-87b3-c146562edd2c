import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

const s3Config = {
  s3_bucket: process.env.S3_BUCKET_NAME,
  s3_secret_key: process.env.S3_SECRET_KEY,
  s3_access_key: process.env.S3_ACCESS_KEY,
  s3_region: process.env.S3_REGION || 'ap-southeast-2'
};

// Log S3 configuration (without exposing full secret key)
console.log('S3 Configuration:', {
  bucket: s3Config.s3_bucket,
  region: s3Config.s3_region,
  accessKeyProvided: !!s3Config.s3_access_key,
  secretKeyProvided: !!s3Config.s3_secret_key,
  accessKeyPrefix: s3Config.s3_access_key ? s3Config.s3_access_key.substring(0, 5) + '...' : 'not provided'
});

// Add more detailed logging
console.log('Creating S3 client with region:', s3Config.s3_region);

const s3Client = new S3Client({
  region: s3Config.s3_region,
  endpoint: `https://s3.${s3Config.s3_region}.amazonaws.com`,
  credentials: {
    accessKeyId: s3Config.s3_access_key || '',
    secretAccessKey: s3Config.s3_secret_key || ''
  },
  forcePathStyle: false
});

class S3Service {
  /**
   * Upload a file to S3 from a local path
   */
  async uploadFile(filePath: string, folder = 'uploads'): Promise<string> {
    const fileContent = fs.readFileSync(filePath);
    const fileName = path.basename(filePath);
    const key = `${folder}/${uuidv4()}-${fileName}`;

    const params = {
      Bucket: s3Config.s3_bucket,
      Key: key,
      Body: fileContent,
      ContentType: this.getContentType(fileName)
    };

    const command = new PutObjectCommand(params);
    await s3Client.send(command);
    return `https://${s3Config.s3_bucket}.s3.amazonaws.com/${key}`;
  }

  /**
   * Upload a buffer directly to S3
   */
  async uploadBuffer(buffer: Buffer, fileName: string, folder = 'uploads'): Promise<string> {
    const key = `${folder}/${uuidv4()}-${fileName}`;

    const params = {
      Bucket: s3Config.s3_bucket,
      Key: key,
      Body: buffer,
      ContentType: this.getContentType(fileName)
    };

    const command = new PutObjectCommand(params);
    await s3Client.send(command);
    return `https://${s3Config.s3_bucket}.s3.amazonaws.com/${key}`;
  }

  /**
   * Upload a base64 data URL to S3
   */
  async uploadBase64(dataUrl: string, fileName: string, folder = 'chat-files'): Promise<{ fileUrl: string, fileType: string }> {
    // Extract the content type and base64 data
    const matches = dataUrl.match(/^data:([A-Za-z-+/]+);base64,(.+)$/);

    if (!matches || matches.length !== 3) {
      throw new Error('Invalid base64 data URL format');
    }

    const contentType = matches[1];
    const base64Data = matches[2];
    const buffer = Buffer.from(base64Data, 'base64');

    // Generate a unique filename if not provided
    if (!fileName) {
      const extension = contentType.split('/')[1] || 'bin';
      fileName = `${uuidv4()}.${extension}`;
    }

    const key = `${folder}/${uuidv4()}-${fileName}`;

    const params = {
      Bucket: s3Config.s3_bucket,
      Key: key,
      Body: buffer,
      ContentType: contentType,
      ContentEncoding: 'base64'
    };

    const command = new PutObjectCommand(params);
    await s3Client.send(command);

    return {
      fileUrl: `https://${s3Config.s3_bucket}.s3.amazonaws.com/${key}`,
      fileType: contentType
    };
  }

  /**
   * Get a file from S3
   */
  async getFile(key: string): Promise<any> {
    const params = {
      Bucket: s3Config.s3_bucket,
      Key: key
    };

    const command = new GetObjectCommand(params);
    return await s3Client.send(command);
  }

  /**
   * Delete a file from S3
   */
  async deleteFile(key: string): Promise<any> {
    const params = {
      Bucket: s3Config.s3_bucket,
      Key: key
    };

    const command = new DeleteObjectCommand(params);
    return await s3Client.send(command);
  }

  /**
   * Extract S3 key from a full URL
   */
  extractKeyFromUrl(url: string): string {
    const bucketUrlPattern = new RegExp(`https?://${s3Config.s3_bucket}.s3.[a-z0-9-]+.amazonaws.com/(.+)`);
    const matches = url.match(bucketUrlPattern);

    if (!matches || matches.length < 2) {
      throw new Error('Invalid S3 URL format');
    }

    return matches[1];
  }

  /**
   * Get content type based on file extension
   */
  private getContentType(fileName: string): string {
    const ext = path.extname(fileName).toLowerCase();

    const mimeTypes: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.ppt': 'application/vnd.ms-powerpoint',
      '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      '.zip': 'application/zip',
      '.rar': 'application/x-rar-compressed',
      '.mp4': 'video/mp4',
      '.mov': 'video/quicktime',
      '.mp3': 'audio/mpeg',
      '.wav': 'audio/wav',
      '.txt': 'text/plain'
    };

    return mimeTypes[ext] || 'application/octet-stream';
  }
}

export default new S3Service();









