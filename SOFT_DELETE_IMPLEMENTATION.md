# Enhanced Deletion System for Coordinators and Annotators

## Overview

This implementation provides a three-tier deletion system for coordinators and annotators with strict client assignment protection. The system prevents deletion of users assigned to clients, performs soft deletion for users with other relationships, and allows hard deletion only for users without any relationships.

## Features

### Smart Deletion Logic
- **Blocked Deletion**: Users assigned to clients cannot be deleted (throws error with client names)
- **Soft Delete**: Users with other relationships are marked as deleted but preserved in the database
- **Hard Delete**: Users without any relationships are permanently removed
- **Client Assignment Protection**: Primary protection against deleting active assignments

### Safety Measures
- Prevents deletion of users assigned to any clients
- Prevents deletion of already deleted users
- Role-based deletion restrictions (only ANNOTATOR and PROJECT_COORDINATOR)
- Email anonymization during soft delete to prevent conflicts

### Query Filtering
- Soft-deleted users are excluded from normal queries by default
- Optional `includeDeleted=true` parameter to include soft-deleted users
- Maintains data integrity while preserving historical relationships

## Deletion Flow

### 1. Client Assignment Check (BLOCKS DELETION)
The system first checks if the user is assigned to any clients through the Assignment table:

**For PROJECT_COORDINATOR:**
- Checks `Assignment.coordinatorId`

**For ANNOTATOR:**
- Checks `Assignment.developerId`

If any client assignments exist, deletion is **completely blocked** and an error is thrown with client names.

### 2. Other Relationship Check (TRIGGERS SOFT DELETE)
If no client assignments exist, the system checks for other relationships:

**For PROJECT_COORDINATOR:**
- Projects as coordinator (`Project.coordinatorId`)
- Project assignments (`ProjectCoordinatorAssignment.projectCoordinatorId`)

**For ANNOTATOR:**
- Annotator projects (`Project.annotators`)
- Annotated tasks (`Task.annotators`)
- Created tasks (`Task.createdById`)
- Time logs (`TimeLog.userId`)

If any of these relationships exist, the user is **soft deleted**.

### 3. Hard Delete (NO RELATIONSHIPS)
If no relationships exist at all, the user is **permanently deleted**.

## API Response Examples

### Blocked Deletion (Client Assignment)
```json
{
  "message": "Cannot delete project_coordinator as they are currently assigned to 2 client(s): John Doe, Jane Smith. Please unassign them from all clients before deletion."
}
```

### Soft Delete Response
```json
{
  "message": "User is linked with other entities (1 project(s) as coordinator, 3 time log(s)) and has been soft deleted",
  "deletionType": "soft",
  "relationshipDetails": [
    "1 project(s) as coordinator",
    "3 time log(s)"
  ],
  "user": {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "role": "PROJECT_COORDINATOR",
    "isDeleted": true,
    "accountStatus": "DELETED",
    "createdAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Hard Delete Response
```json
{
  "message": "User has been permanently deleted as no relationships were found",
  "deletionType": "hard",
  "relationshipDetails": [],
  "user": {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "role": "ANNOTATOR",
    "createdAt": "2023-01-01T00:00:00.000Z"
  }
}
```

## Implementation Details

### Services Modified

#### 1. Onboarding Service (`src/services/onboarding/onboarding.service.ts`)
- Enhanced `deleteAnnotator()` method with client assignment protection
- Updated `getAnnotator()` method to exclude soft-deleted users

#### 2. Annotator Service (`src/services/annotator/annotator.service.ts`)
- Enhanced `deleteAnnotator()` method with client assignment protection
- Updated `getAnnotator()` and `getAllCoordinator()` methods to exclude soft-deleted users

### Controllers Modified

#### 1. Onboarding Controller (`src/controllers/onboarding/onboarding.controller.ts`)
- Updated `deleteById()` method to handle new response format

#### 2. Annotator Controller (`src/controllers/annotator/annotator.controller.ts`)
- Updated `deleteAnnotator()` method with proper error handling

## Usage Examples

### 1. Delete User (Admin Only)

```javascript
// Onboarding endpoint
DELETE /api/onboarding/delete/:id

// Annotator endpoint  
DELETE /api/annotators/delete-annotator/:id
```

### 2. Get Users (Excluding Soft-Deleted)

```javascript
// Default behavior - excludes soft-deleted users
GET /api/onboarding/all?page=1&limit=10

// Include soft-deleted users
GET /api/onboarding/all?page=1&limit=10&includeDeleted=true
```

## Error Handling

### Common Error Scenarios:

1. **User Assigned to Clients**
   ```json
   {
     "message": "Cannot delete annotator as they are currently assigned to 1 client(s): ABC Corp. Please unassign them from all clients before deletion."
   }
   ```

2. **User Not Found**
   ```json
   {
     "message": "User not found"
   }
   ```

3. **User Already Deleted**
   ```json
   {
     "message": "User is already deleted"
   }
   ```

4. **Invalid Role**
   ```json
   {
     "message": "Only annotators and coordinators can be deleted"
   }
   ```

5. **Permission Denied**
   ```json
   {
     "message": "Only admins can delete annotators"
   }
   ```

## Testing

Run the test script to verify functionality:

```bash
node test-soft-delete.js
```

This script will:
- Find users assigned to clients (cannot be deleted)
- Find users with other relationships (soft delete)
- Find users without relationships (hard delete)
- Show current soft-deleted users

## Workflow for Deleting Assigned Users

To delete a user assigned to clients:

1. **Identify Client Assignments**: Check which clients the user is assigned to
2. **Unassign from Clients**: Remove all assignments from the Assignment table
3. **Attempt Deletion**: Now the user can be deleted (soft or hard based on other relationships)

```sql
-- Example: Remove all assignments for a coordinator
DELETE FROM Assignment WHERE coordinatorId = 'user_id';

-- Example: Remove all assignments for an annotator
DELETE FROM Assignment WHERE developerId = 'user_id';
```

## Benefits

1. **Client Protection**: Prevents accidental deletion of users actively working with clients
2. **Data Integrity**: Preserves historical relationships and audit trails
3. **Flexibility**: Allows recovery of accidentally deleted users (soft delete)
4. **Safety**: Multiple layers of protection against data loss
5. **Transparency**: Clear indication of deletion type and blocking reasons

## Migration Considerations

- Existing queries will automatically exclude soft-deleted users
- Use `includeDeleted=true` parameter when you need to access soft-deleted users
- Consider implementing a user unassignment workflow before deletion
- Monitor client assignments to understand deletion blockers

## Future Enhancements

- Add bulk unassignment functionality
- Implement user restoration functionality
- Add audit logging for deletion attempts
- Create admin interface for managing assignments and deletions
- Add automated cleanup of old soft-deleted records