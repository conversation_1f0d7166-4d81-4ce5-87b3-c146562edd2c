-- Create<PERSON><PERSON>
CREATE TYPE "ArrivalStatus" AS ENUM ('ON_TIME', 'MINUTES_LATE', 'HOURS_LATE', 'ABSENT');

-- CreateEnum
CREATE TYPE "LogStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'ABSENT');

-- CreateTable
CREATE TABLE "AttendanceSummary" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "timeIn" TIMESTAMP(3),
    "timeOut" TIMESTAMP(3),
    "status" "ArrivalStatus" NOT NULL,
    "breakMinutes" INTEGER NOT NULL DEFAULT 0,
    "workingMinutes" INTEGER NOT NULL DEFAULT 0,
    "totalLeave" INTEGER NOT NULL DEFAULT 0,
    "availableLeave" INTEGER NOT NULL DEFAULT 0,
    "consumedLeave" INTEGER NOT NULL DEFAULT 0,
    "totalBreak" INTEGER NOT NULL DEFAULT 60,
    "availableBreak" INTEGER NOT NULL DEFAULT 60,
    "consumedBreak" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AttendanceSummary_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "AttendanceSummary" ADD CONSTRAINT "AttendanceSummary_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
