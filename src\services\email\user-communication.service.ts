import { EmailService } from './email.service';

export interface OTPEmailData {
  customerName: string;
  otpCode: string;
  timeLimit: number;
  companyName: string;
  supportEmail: string;
  phoneNumber: string;
  website: string;
}

export interface SignupOTPEmailData {
  firstName: string;
  otpCode: string;
  timeLimit: number;
  companyName: string;
  supportEmail: string;
  phoneNumber: string;
  websiteUrl: string;
}

export interface InviteCoworkerEmailData {
  coworkerName: string;
  clientName: string;
  platformName: string;
  role: string;
  invitationLink: string;
  companyName: string;
  supportEmail: string;
  websiteUrl: string;
}

export interface PasswordCreationEmailData {
  firstName: string;
  passwordCreationLink: string;
  timeLimit: string;
  companyName: string;
  supportEmail: string;
  websiteUrl: string;
}

export interface AccountSuspendedEmailData {
  firstName: string;
  platformName: string;
  suspensionDate: string;
  suspensionReason: string;
  suspensionDuration?: string;
  companyName: string;
  supportEmail: string;
  supportPhone: string;
  websiteUrl: string;
}

export interface AccountReactivatedEmailData {
  firstName: string;
  platformName: string;
  loginLink: string;
  companyName: string;
  supportEmail: string;
  websiteUrl: string;
}

export interface WelcomeEmailData {
  firstName: string;
  meetingLink: string;
  companyName: string;
  yourName?: string;
  teamName?: string;
  supportEmail: string;
  websiteUrl: string;
}

export interface PasswordChangedEmailData {
  firstName: string;
  platformName: string;
  dateTime: string;
  email: string;
  ipAddress: string;
  resetLink: string;
  companyName: string;
  supportEmail: string;
  websiteUrl: string;
}

export interface TeamAssignmentEmailData {
  firstName: string;
  platformName: string;
  projectName: string;
  projectId: string;
  startDate: string;
  annotatorName: string;
  annotatorInitials: string;
  annotatorExpertise: string;
  annotatorEmail: string;
  annotatorPhone?: string;
  coordinatorName: string;
  coordinatorInitials: string;
  coordinatorEmail: string;
  coordinatorPhone?: string;
  companyName: string;
  supportEmail: string;
  websiteUrl: string;
}

export class UserCommunicationService {
  private emailService: EmailService;

  constructor() {
    this.emailService = new EmailService();
  }

  /**
   * Send OTP verification email
   */
  async sendOTPVerification(to: string, data: OTPEmailData): Promise<void> {
    await this.emailService.sendEmail({
      to,
      subject: `Your OTP Code: ${data.otpCode}`,
      template: 'otp-verification',
      data
    });
  }

  /**
   * Send signup OTP email
   */
  async sendSignupOTP(to: string, data: SignupOTPEmailData): Promise<void> {
    await this.emailService.sendEmail({
      to,
      subject: `Welcome to ${data.companyName} - Verify Your Email`,
      template: 'otp-signup',
      data
    });
  }

  /**
   * Send coworker invitation email
   */
  async sendCoworkerInvitation(to: string, data: InviteCoworkerEmailData): Promise<void> {
    await this.emailService.sendEmail({
      to,
      subject: `You're invited to join ${data.clientName} on ${data.platformName}`,
      template: 'invite-coworker',
      data
    });
  }

  /**
   * Send password creation link email
   */
  async sendPasswordCreationLink(to: string, data: PasswordCreationEmailData): Promise<void> {
    await this.emailService.sendEmail({
      to,
      subject: `Create Your Password - ${data.companyName}`,
      template: 'password-creation-link',
      data
    });
  }

  /**
   * Send account suspended email
   */
  async sendAccountSuspended(to: string, data: AccountSuspendedEmailData): Promise<void> {
    await this.emailService.sendEmail({
      to,
      subject: `Account Suspended - ${data.platformName}`,
      template: 'account-suspended',
      data
    });
  }

  /**
   * Send account reactivated email
   */
  async sendAccountReactivated(to: string, data: AccountReactivatedEmailData): Promise<void> {
    await this.emailService.sendEmail({
      to,
      subject: `Account Reactivated - Welcome Back!`,
      template: 'account-reactivated',
      data
    });
  }

  /**
   * Send welcome email with meeting link
   */
  async sendWelcomeEmail(to: string, data: WelcomeEmailData): Promise<void> {
    await this.emailService.sendEmail({
      to,
      subject: `Welcome to ${data.companyName} - Let's Schedule Your Discovery Meeting`,
      template: 'welcome-mail',
      data
    });
  }

  /**
   * Send password changed confirmation email
   */
  async sendPasswordChanged(to: string, data: PasswordChangedEmailData): Promise<void> {
    await this.emailService.sendEmail({
      to,
      subject: `Password Changed Successfully - ${data.platformName}`,
      template: 'password-changed',
      data
    });
  }

  /**
   * Send team assignment notification email
   */
  async sendTeamAssignment(to: string, data: TeamAssignmentEmailData): Promise<void> {
    await this.emailService.sendEmail({
      to,
      subject: `Your Team is Ready - Annotator and Coordinator Assigned`,
      template: 'team-assignment',
      data
    });
  }

  /**
   * Generate OTP code
   */
  generateOTP(length: number = 6): string {
    const digits = '0123456789';
    let otp = '';
    for (let i = 0; i < length; i++) {
      otp += digits[Math.floor(Math.random() * digits.length)];
    }
    return otp;
  }

  /**
   * Generate secure token for password reset/creation
   */
  generateSecureToken(length: number = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let token = '';
    for (let i = 0; i < length; i++) {
      token += chars[Math.floor(Math.random() * chars.length)];
    }
    return token;
  }

  /**
   * Get user initials from name
   */
  getUserInitials(name: string): string {
    return name
      .split(' ')
      .map(part => part.charAt(0).toUpperCase())
      .slice(0, 2)
      .join('');
  }

  /**
   * Format date for email display
   */
  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    });
  }

  /**
   * Test email service connection
   */
  async testConnection(): Promise<boolean> {
    return await this.emailService.testConnection();
  }
}

export default UserCommunicationService;