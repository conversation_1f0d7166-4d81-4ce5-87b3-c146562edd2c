export const projectCompletionTemplate = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Completed</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #6f42c1;
            margin-bottom: 10px;
        }
        .completion-banner {
            background: linear-gradient(135deg, #6f42c1, #e83e8c);
            color: white;
            padding: 25px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .project-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #6f42c1;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-item {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #6f42c1;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
        .cta-button {
            display: inline-block;
            background: #6f42c1;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px 5px;
            text-align: center;
        }
        .cta-container {
            text-align: center;
            margin: 30px 0;
        }
        .team-section {
            background: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        .contact-info {
            margin-top: 20px;
        }
        .contact-info a {
            color: #6f42c1;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{companyName}}</div>
        </div>

        <div class="completion-banner">
            <h2>🎉 Project Completed Successfully!</h2>
        </div>

        <p>Hi {{firstName}},</p>

        <p>Congratulations! Your project <strong>"{{projectName}}"</strong> has been completed successfully.</p>

        <div class="project-details">
            <h3>Project Summary:</h3>
            <p><strong>Project:</strong> {{projectName}}</p>
            <p><strong>Started:</strong> {{startDate}}</p>
            <p><strong>Completed:</strong> {{completionDate}}</p>
            <p><strong>Duration:</strong> {{projectDuration}}</p>
            <p><strong>Status:</strong> ✅ Completed</p>
        </div>

        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">{{totalTasks}}</div>
                <div class="stat-label">Tasks Completed</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{totalHours}}</div>
                <div class="stat-label">Hours Worked</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{qualityScore}}%</div>
                <div class="stat-label">Quality Score</div>
            </div>
        </div>

        <div class="team-section">
            <h4>👥 Team Members:</h4>
            <p><strong>Annotator:</strong> {{annotatorName}} ({{annotatorEmail}})</p>
            <p><strong>Coordinator:</strong> {{coordinatorName}} ({{coordinatorEmail}})</p>
        </div>

        <p>The project deliverables are now ready for your review. You can access all completed work and download the final results from your dashboard.</p>

        <div class="cta-container">
            <a href="{{projectUrl}}" class="cta-button">📁 View Project</a>
            <a href="{{downloadUrl}}" class="cta-button">⬇️ Download Results</a>
        </div>

        <p>We hope you're satisfied with the quality of work delivered. If you have any feedback or questions, please don't hesitate to reach out to our team.</p>

        <div class="footer">
            <p><strong>Thank you for choosing {{companyName}}!</strong><br>
            – The <strong>{{companyName}}</strong> Team</p>
            
            <div class="contact-info">
                <a href="mailto:{{supportEmail}}">{{supportEmail}}</a> | 
                <a href="{{websiteUrl}}">{{websiteUrl}}</a>
            </div>
        </div>
    </div>
</body>
</html>
`;