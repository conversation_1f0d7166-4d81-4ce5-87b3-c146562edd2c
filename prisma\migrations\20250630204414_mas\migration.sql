/*
  Warnings:

  - The `priority` column on the `EmailQueue` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `status` column on the `EmailQueue` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- CreateEnum
CREATE TYPE "EmailPriority" AS ENUM ('LOW', 'NORMAL', 'HIGH', 'URGENT');

-- CreateEnum
CREATE TYPE "EmailTStatus" AS ENUM ('PENDING', 'PROCESSING', 'SENT', 'FAILED', 'CANCELLED');

-- AlterTable
ALTER TABLE "EmailQueue" DROP COLUMN "priority",
ADD COLUMN     "priority" "EmailPriority" NOT NULL DEFAULT 'NORMAL',
DROP COLUMN "status",
ADD COLUMN     "status" "EmailTStatus" NOT NULL DEFAULT 'PENDING';

-- CreateIndex
CREATE INDEX "EmailQueue_status_idx" ON "EmailQueue"("status");

-- CreateIndex
CREATE INDEX "EmailQueue_priority_idx" ON "EmailQueue"("priority");
