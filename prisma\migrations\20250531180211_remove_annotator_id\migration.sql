/*
  Warnings:

  - You are about to drop the column `annotatorId` on the `Project` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "Project" DROP CONSTRAINT "Project_annotatorId_fkey";

-- AlterTable
ALTER TABLE "Project" DROP COLUMN "annotatorId";

-- CreateTable
CREATE TABLE "_AnnotatorProjects" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_AnnotatorProjects_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_AnnotatorProjects_B_index" ON "_AnnotatorProjects"("B");

-- AddForeignKey
ALTER TABLE "_AnnotatorProjects" ADD CONSTRAINT "_AnnotatorProjects_A_fkey" FOREIGN KEY ("A") REFERENCES "Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddF<PERSON><PERSON><PERSON>ey
ALTER TABLE "_AnnotatorProjects" ADD CONSTRAINT "_AnnotatorProjects_B_fkey" FOREIGN KEY ("B") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
