import { Router } from "express";
import { asyncHandler } from "../../middlewares/asyncHandler";
import authMiddleware from "../../middlewares/checkAuth";
import { hasRole } from "../../middlewares/checkRole";
import { MessageController } from "../../controllers/message/message.controller";
import { subscriptionMiddleware } from "../../middlewares/subscription.middleware";
// import { ReactionController } from "../../controllers/message/reaction.controller";
// import { ReadStatusController } from "../../controllers/message/read-status.controller";
// import { GroupController } from "../../controllers/message/group.controller";
// import { GroupMemberController } from "../../controllers/message/group-member.controller";

const router = Router();

const messageController = new MessageController();
// const reactionController = new ReactionController();
// const readStatusController = new ReadStatusController();
// const groupController = new GroupController();
// const memberController = new GroupMemberController();

// 🔹 Messages

// router.post(
//   "/messages",
//   authMiddleware,
//   asyncHandler(messageController.sendMessage)
// );

router
  .route("/chats/sidebar")
  .get(
    authMiddleware, 
    subscriptionMiddleware.checkSubscriptionWithWarning,
    asyncHandler(messageController.getSidebar)
  );


router.get(
  "/messages/conversation/:userId",
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  asyncHandler(messageController.getConversation)
);
// router.get(
//   "/messages/group/:groupId",
//   authMiddleware,
//   asyncHandler(messageController.getGroupMessages)
// );
router.patch(
  "/messages/:messageId",
  authMiddleware,
  asyncHandler(messageController.editMessage)
);
router.delete(
  "/messages/:messageId",
  authMiddleware,
  asyncHandler(messageController.deleteMessage)
);

router.get(
  "/get-all-message/:otherUserId",
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  asyncHandler(messageController.getMessagesBetweenUsers)
);

router.get('/group-messages/:groupId',
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  asyncHandler(messageController.getGroupMessages));

router.get(
  "/media/:conversationId",
  authMiddleware,
  asyncHandler(messageController.getConversationMedia)
);

router.get('/members/:groupId', authMiddleware,asyncHandler(messageController.getGroupMembers));


// router.post(
//   "/messages/:messageId/reply",
//   authMiddleware,
//   asyncHandler(messageController.replyToMessage)
// );
// router.post(
//   "/messages/:messageId/forward",
//   authMiddleware,
//   asyncHandler(messageController.forwardMessage)
// );
// router.patch(
//   "/messages/:messageId/pin",
//   authMiddleware,
//   asyncHandler(messageController.togglePin)
// );

// 🔹 Reactions
// router.post(
//   "/reactions",
//   authMiddleware,
//   asyncHandler(reactionController.addReaction)
// );
// router.delete(
//   "/reactions",
//   authMiddleware,
//   asyncHandler(reactionController.removeReaction)
// );

// 🔹 Read Receipts
// router.post(
//   "/messages/:messageId/read",
//   authMiddleware,
//   asyncHandler(readStatusController.markAsRead)
// );
// router.get(
//   "/messages/:messageId/readers",
//   authMiddleware,
//   asyncHandler(readStatusController.getReadStatus)
// );

// 🔹 Group Chats
// router.post(
//   "/groups",
//   authMiddleware,
//   hasRole("ADMIN"),
//   asyncHandler(groupController.createGroup)
// );
// router.patch(
//   "/groups/:groupId",
//   authMiddleware,
//   hasRole("ADMIN"),
//   asyncHandler(groupController.updateGroup)
// );
// router.delete(
//   "/groups/:groupId",
//   authMiddleware,
//   hasRole("ADMIN"),
//   asyncHandler(groupController.deleteGroup)
// );
// router.get(
//   "/groups/:groupId",
//   authMiddleware,
//   asyncHandler(groupController.getGroupInfo)
// );
// router.get(
//   "/groups/user/:userId",
//   authMiddleware,
//   asyncHandler(groupController.getUserGroups)
// );

// 🔹 Group Members
// router.post(
//   "/groups/:groupId/members",
//   authMiddleware,
//   hasRole("ADMIN"),
//   asyncHandler(memberController.addMember)
// );
// router.delete(
//   "/groups/:groupId/members/:userId",
//   authMiddleware,
//   hasRole("ADMIN"),
//   asyncHandler(memberController.removeMember)
// );
// router.patch(
//   "/groups/:groupId/members/:userId/role",
//   authMiddleware,
//   hasRole("ADMIN"),
//   asyncHandler(memberController.updateMemberRole)
// );
// router.get(
//   "/groups/:groupId/members",
//   authMiddleware,
//   asyncHandler(memberController.getGroupMembers)
// );

export default router;
