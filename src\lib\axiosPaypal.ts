import axios, { AxiosInstance, InternalAxiosRequestConfig } from "axios";
import dotenv from "dotenv";

dotenv.config();

const ZOHO_CLIENT_ID = process.env.ZOHO_CLIENT_ID!;
const ZOHO_CLIENT_SECRET = process.env.ZOHO_CLIENT_SECRET!;
const ZOHO_REFRESH_TOKEN = process.env.ZOHO_REFRESH_TOKEN!;
const ZOHO_ORGANIZATION_ID = process.env.ZOHO_ORGANIZATION_ID!;
const ZOHO_API_DOMAIN = process.env.ZOHO_API_DOMAIN!; // e.g. https://www.zohoapis.com
const ZOHO_BASE_AUTH_URL=process.env.ZOHO_BASE_AUTH_URL!;

interface TokenCache {
  token: string | null;
  expiresAt: number | null;
}

let tokenCache: TokenCache = {
  token: null,
  expiresAt: null,
};

async function fetchZohoAccessToken(): Promise<string> {
  const response = await axios.post(
    `${ZOHO_BASE_AUTH_URL}/token`,
    null,
    {
      params: {
        refresh_token: ZOHO_REFRESH_TOKEN,
        client_id: ZOHO_CLIENT_ID,
        client_secret: ZOHO_CLIENT_SECRET,
        grant_type: "refresh_token",
      },
    }
  );

  const { access_token, expires_in } = response.data;

  tokenCache.token = access_token;
  tokenCache.expiresAt = Date.now() + expires_in * 1000 - 60000; // Refresh 60s early
  console.log(tokenCache.token,"mangaread")
  return tokenCache.token!;
}

const zohoClient: AxiosInstance = axios.create({
  baseURL: `${ZOHO_API_DOMAIN}/billing/v1/`,
  headers: {
    "Content-Type": "application/json",
    "X-com-zoho-subscriptions-organizationid": ZOHO_ORGANIZATION_ID,
  },
});

zohoClient.interceptors.request.use(
  async (config: InternalAxiosRequestConfig): Promise<InternalAxiosRequestConfig> => {
    if (!tokenCache.token || !tokenCache.expiresAt || Date.now() >= tokenCache.expiresAt) {
      await fetchZohoAccessToken();
    }

    config.headers.set("Authorization", `Zoho-oauthtoken ${tokenCache.token}`);
    return config;
  },
  (error) => Promise.reject(error)
);

export default zohoClient;
