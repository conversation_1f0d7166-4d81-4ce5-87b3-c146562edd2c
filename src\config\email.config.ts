/**
 * Email Configuration
 * Handles different email providers and their specific settings
 */

export interface EmailConfig {
  provider: 'brevo' | 'gmail' | 'sendgrid' | 'other';
  smtp: {
    host: string;
    port: number;
    secure: boolean;
    auth: {
      user: string;
      pass: string;
    };
  };
  from: string;
  features: {
    trackingDisabled: boolean;
    customHeaders: Record<string, any>;
  };
}

/**
 * Get email configuration based on environment variables
 */
export function getEmailConfig(): EmailConfig {
  const smtpHost = process.env.SMTP_HOST || process.env.MAIL_HOST || 'smtp.gmail.com';
  const smtpUser = process.env.SMTP_USER || process.env.MAIL_USER || '';
  const smtpPass = process.env.SMTP_PASS || process.env.MAIL_PASS || '';
  const emailFrom = process.env.SMTP_FROM || process.env.EMAIL_FROM || smtpUser;

  // Detect provider based on SMTP host
  let provider: EmailConfig['provider'] = 'other';
  let customHeaders: Record<string, any> = {};
  let trackingDisabled = false;

  if (smtpHost.includes('brevo.com') || smtpHost.includes('sendinblue.com')) {
    provider = 'brevo';
    trackingDisabled = true; // Disable tracking for Brevo by default
    customHeaders = {
      'X-Mailin-Tag': 'no-tracking',
      'X-Mailin-Custom': JSON.stringify({ 
        'DISABLE_TRACKING': true,
        'DISABLE_LINK_TRACKING': true 
      })
    };
  } else if (smtpHost.includes('gmail.com')) {
    provider = 'gmail';
  } else if (smtpHost.includes('sendgrid.net')) {
    provider = 'sendgrid';
  }

  return {
    provider,
    smtp: {
      host: smtpHost,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: smtpUser,
        pass: smtpPass,
      },
    },
    from: emailFrom,
    features: {
      trackingDisabled,
      customHeaders,
    },
  };
}

/**
 * Get headers for specific email templates
 */
export function getEmailHeaders(template: string, provider: EmailConfig['provider']): Record<string, any> | undefined {
  const config = getEmailConfig();
  
  // For invitation emails, always try to disable tracking
  if (template === 'invite-coworker' || template.includes('invite')) {
    switch (provider) {
      case 'brevo':
        return {
          'X-Mailin-Tag': 'invitation-no-tracking',
          'X-Mailin-Custom': JSON.stringify({ 
            'DISABLE_TRACKING': true,
            'DISABLE_LINK_TRACKING': true,
            'DISABLE_OPEN_TRACKING': true
          }),
          // Additional Brevo headers to prevent tracking
          'X-Mailin-IP': 'pool',
          'X-Mailin-Tracking': 'false'
        };
      case 'sendgrid':
        return {
          'X-SMTPAPI': JSON.stringify({
            'filters': {
              'clicktrack': { 'settings': { 'enable': 0 } },
              'opentrack': { 'settings': { 'enable': 0 } }
            }
          })
        };
      default:
        return config.features.customHeaders;
    }
  }

  return undefined;
}

/**
 * Check if link tracking should be disabled for a template
 */
export function shouldDisableTracking(template: string): boolean {
  const trackingDisabledTemplates = [
    'invite-coworker',
    'password-reset',
    'email-verification',
    'account-activation'
  ];
  
  return trackingDisabledTemplates.includes(template) || template.includes('invite');
}

export default {
  getEmailConfig,
  getEmailHeaders,
  shouldDisableTracking,
};
