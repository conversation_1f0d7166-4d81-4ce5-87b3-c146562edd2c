import { Request, Response, NextFunction } from "express";
import { AuthenticatedRequest } from "./checkAuth";
import { SubscriptionService } from "../services/subscription/subscription.service";
import { errorResponse } from "../helper/apiResponse";

export interface SubscriptionValidatedRequest extends AuthenticatedRequest {
  userSubscriptions?: any[];
  hasActiveSubscription?: boolean;
}

export class SubscriptionMiddleware {
  private subscriptionService: SubscriptionService;

  constructor() {
    this.subscriptionService = new SubscriptionService();
  }

  /**
   * Helper method to determine subscription status and generate appropriate error message
   */
  private getSubscriptionErrorMessage(subscriptions: any[], isClient: boolean = true): string {
    const now = new Date();
    const expiredCount = subscriptions.filter((sub: any) => 
      sub.status === 'EXPIRED' || (sub.endDate && sub.endDate < now)
    ).length;
    
    const prefix = isClient ? "Access denied." : "Access denied. The client";
    const contactInfo = isClient 
      ? "Please renew your subscription to continue using this service."
      : "Please contact your client to renew the subscription.";
    
    if (expiredCount > 0) {
      return `${prefix} ${isClient ? 'You have' : 'has'} ${expiredCount} expired subscription(s). ${contactInfo}`;
    } else if (subscriptions.length > 0) {
      const inactiveInfo = isClient 
        ? "Your subscription is inactive. Please contact support or renew your subscription."
        : "'s subscription is inactive. Please contact your client or support.";
      return `${prefix} ${inactiveInfo}`;
    } else {
      const noSubInfo = isClient
        ? "No subscription found. Please purchase a subscription to access this service."
        : " has no subscription. Please contact your client to purchase a subscription.";
      return `${prefix} ${noSubInfo}`;
    }
  }

  /**
   * Middleware to check if user has any active subscription
   * Blocks access if all subscriptions are expired/cancelled/inactive
   */
  requireActiveSubscription = async (
    req: SubscriptionValidatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        errorResponse(res, "User authentication required");
      }

      // Skip subscription check for ADMIN users
      if (req.user?.userRole === "ADMIN") {
        req.hasActiveSubscription = true;
        return next();
      }

      // For CLIENT users, check subscription status
      if (req.user?.userRole === "CLIENT") {
        const subscriptionStatus =
          await this.subscriptionService.getUserSubscriptionStatus(userId!);

        if (!subscriptionStatus.hasActiveSubscription) {
          const message = this.getSubscriptionErrorMessage(subscriptionStatus.subscriptions, true);
          console.log(`Subscription access denied for CLIENT user ${userId}: ${message}`);
          errorResponse(res, message);
        }

        req.userSubscriptions = subscriptionStatus.subscriptions;
        req.hasActiveSubscription = true;
        return next();
      }

      // For COWORKER, ANNOTATOR, PROJECT_COORDINATOR - check their client's subscription
      if (
        ["COWORKER", "ANNOTATOR", "PROJECT_COORDINATOR"].includes(
          req.user?.userRole || ""
        )
      ) {
        const clientSubscriptionStatus =
          await this.subscriptionService.getClientSubscriptionForUser(userId!);

        if (!clientSubscriptionStatus.hasActiveSubscription) {
          const message = this.getSubscriptionErrorMessage(clientSubscriptionStatus.subscriptions, false);
          console.log(`Subscription access denied for ${req.user?.userRole} user ${userId}: ${message}`);
          errorResponse(res, message);
        }

        req.userSubscriptions = clientSubscriptionStatus.subscriptions;
        req.hasActiveSubscription = true;
        return next();
      }

      // Default: deny access for unknown roles
      errorResponse(res, "Access denied. Invalid user role.");
    } catch (error: any) {
      console.error("Subscription middleware error:", error);
      errorResponse(res, "Failed to validate subscription status");
    }
  };

  /**
   * Middleware to check subscription but allow access with warnings
   * Useful for read-only operations or notifications
   */
  checkSubscriptionWithWarning = async (
    req: SubscriptionValidatedRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        return next();
      }

      // Skip for ADMIN users
      if (req.user?.userRole === "ADMIN") {
        req.hasActiveSubscription = true;
        return next();
      }

      let subscriptionStatus;

      if (req.user?.userRole === "CLIENT") {
        subscriptionStatus =
          await this.subscriptionService.getUserSubscriptionStatus(userId);
      } else if (
        ["COWORKER", "ANNOTATOR", "PROJECT_COORDINATOR"].includes(
          req.user?.userRole || ""
        )
      ) {
        subscriptionStatus =
          await this.subscriptionService.getClientSubscriptionForUser(userId);
      } else {
        // For unknown roles, set default values and continue
        req.userSubscriptions = [];
        req.hasActiveSubscription = false;
        return next();
      }

      req.userSubscriptions = subscriptionStatus.subscriptions;
      req.hasActiveSubscription = subscriptionStatus.hasActiveSubscription;

      next();
    } catch (error: any) {
      console.error("Subscription warning middleware error:", error);
      // Continue even if subscription check fails
      next();
    }
  };
}

// Export singleton instance
export const subscriptionMiddleware = new SubscriptionMiddleware();
