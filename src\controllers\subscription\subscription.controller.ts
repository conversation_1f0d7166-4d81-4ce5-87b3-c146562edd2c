import { Request, Response } from "express";
import { SubscriptionService } from "../../services/subscription/subscription.service";
import { successResponse, errorResponse } from "../../helper/apiResponse";
import { AuthenticatedRequest } from "../../middlewares/checkAuth";

export class SubscriptionController {
  private subscriptionService: SubscriptionService;

  constructor() {
    this.subscriptionService = new SubscriptionService();
  }

  /**
   * Get current user's subscription status and available features
   */
  async getSubscriptionStatus(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        return errorResponse(res, "User authentication required");
      }

      const subscriptionSummary = await this.subscriptionService.getSubscriptionSummary(userId);
      
      return successResponse(res, "Subscription status retrieved successfully");
    } catch (error: any) {
      console.error("Error getting subscription status:", error);
      return errorResponse(res, "Failed to retrieve subscription status");
    }
  }

  /**
   * Get detailed subscription information including expiring subscriptions
   */
  async getDetailedSubscriptionInfo(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        return errorResponse(res, "User authentication required");
      }

      // Only allow clients to view their own detailed info, or admins
      if (req.user?.userRole !== "CLIENT" && req.user?.userRole !== "ADMIN") {
        return errorResponse(res, "Access denied. Only clients can view detailed subscription information.");
      }

      let targetUserId = userId;
      
      // If admin is requesting for another user
      if (req.user?.userRole === "ADMIN" && req.query.userId) {
        targetUserId = req.query.userId as string;
      }

      const detailedInfo = await this.subscriptionService.getDetailedSubscriptionInfo(targetUserId);
      
      return successResponse(res, "Detailed subscription information retrieved successfully");
    } catch (error: any) {
      console.error("Error getting detailed subscription info:", error);
      return errorResponse(res, "Failed to retrieve detailed subscription information");
    }
  }

  /**
   * Check if user has access to a specific feature
   */
  async checkFeatureAccess(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      const { feature } = req.params;

      if (!userId) {
        return errorResponse(res, "User authentication required");
      }

      if (!feature) {
        return errorResponse(res, "Feature name is required");
      }

      const hasAccess = await this.subscriptionService.checkFeatureAccess(userId, feature);
      const subscriptionSummary = await this.subscriptionService.getSubscriptionSummary(userId);
      
      return successResponse(res, "Feature access check completed");
    } catch (error: any) {
      console.error("Error checking feature access:", error);
      return errorResponse(res, "Failed to check feature access");
    }
  }

  /**
   * Get all available features (for admin or feature discovery)
   */
  async getAvailableFeatures(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        return errorResponse(res, "User authentication required");
      }

      const subscriptionSummary = await this.subscriptionService.getSubscriptionSummary(userId);
      
      // Get all features from database
      const allFeatures = await this.subscriptionService.getAllFeatures();
      
      return successResponse(res, "Available features retrieved successfully");
    } catch (error: any) {
      console.error("Error getting available features:", error);
      return errorResponse(res, "Failed to retrieve available features");
    }
  }

  /**
   * Update expired subscriptions (admin only)
   */
  async updateExpiredSubscriptions(req: AuthenticatedRequest, res: Response) {
    try {
      if (req.user?.userRole !== "ADMIN") {
        return errorResponse(res, "Access denied. Admin access required.");
      }

      const result = await this.subscriptionService.updateExpiredSubscriptions();
      
      return successResponse(res, "Expired subscriptions updated successfully");
    } catch (error: any) {
      console.error("Error updating expired subscriptions:", error);
      return errorResponse(res, "Failed to update expired subscriptions");
    }
  }
}