// validators/auth.ts
import { z } from "zod";

const passwordRegex =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,16}$/;

export const createOnBoardingSchema = z.object({
  name: z.string().min(1, "Name is required"),
  lastname: z.string().optional(),
  email: z.string().email("Invalid email address"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters long")
    .max(16, "Password must be at most 16 characters long")
    .regex(
      passwordRegex,
      "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
    )
    .nullable()
    .optional(),
  domain: z.string().optional(),
  packageId: z.string().optional(),
  role: z.enum(["PROJECT_COORDINATOR", "ANNOTATOR"]).default("ANNOTATOR"),
  // Add any other fields you need to validate
});
