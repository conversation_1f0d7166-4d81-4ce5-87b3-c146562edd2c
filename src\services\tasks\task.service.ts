import prisma from "../../prisma";
import { AppError } from "../../utils/ApiError";


interface CreateTaskInput {
  name: string;
  description: string;
  color: string;
  priority: "LOW" | "MEDIUM" | "HIGH";
  projectId: string;
  createdById: string;
  annotatorIds?: string[];
  startDate: Date;
  dueDate: Date;
}

interface UpdateTaskInput {
  name?: string;
  description?: string;
  color?: string;
  priority?: "LOW" | "MEDIUM" | "HIGH";
  projectId?: string;
  assignedToId?: string;
  status?: "PENDING" | "IN_PROGRESS" | "COMPLETED";
  startDate?: Date;
  dueDate?: Date;
}

class TaskService {
async createTask(data: CreateTaskInput) {
  return prisma.task.create({
    data: {
      name: data.name,
      description: data.description,
      color: data.color,
      priority: data.priority,
      projectId: data.projectId,
      createdById: data.createdById,
      startDate: data.startDate,
      dueDate: data.dueDate,
      status: "PENDING",
      annotators: data.annotatorIds
        ? {
            connect: data.annotatorIds.map((id) => ({ id })),
          }
        : undefined,
    },
    include: {
      annotators: true,
    },
  });
}

  async getAllTasks(projectId?: string) {
    const whereClause = projectId ? { projectId } : {};

    return prisma.task.findMany({
      where: whereClause,
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  async getTaskById(id: string) {
    return prisma.task.findUnique({
      where: { id },
    });
  }

  async updateTask(id: string, data: UpdateTaskInput) {
    const existingTask = await prisma.task.findUnique({ where: { id } });
    if (!existingTask) {
      throw new Error("Task not found");
    }

    return prisma.task.update({
      where: { id },
      data,
    });
  }

  async deleteTask(id: string) {
    return prisma.task.delete({
      where: { id },
    });
  }

  async getTasksByStatus(clientId: string) {
    // Step 1: Get all project IDs created by this client
    const projects = await prisma.project.findMany({
      where: { createdById: clientId },
      select: { id: true },
    });

    const projectIds = projects.map(p => p.id);

    if (projectIds.length === 0) {
      return {
        todo: [],
        inProgress: [],
        completed: [],
      };
    }

    // Step 2: Get tasks for those projects, grouped by status
    const [todo, inProgress, completed] = await Promise.all([
      prisma.task.findMany({ where: { projectId: { in: projectIds }, status: "PENDING" } }),
      prisma.task.findMany({ where: { projectId: { in: projectIds }, status: "IN_PROGRESS" } }),
      prisma.task.findMany({ where: { projectId: { in: projectIds }, status: "COMPLETED" } }),
    ]);

    return {
      todo,
      inProgress,
      completed,
    };
  }

async createCooworkerTask(data: CreateTaskInput) {
  const coworker = await prisma.user.findFirst({
    where: {
      id: data.createdById,
    },
  });

  if (!coworker) {
    throw new AppError("No Cooworker Found", 404);
  }

  if (coworker.coworkerPermission !== "EDIT") {
    throw new AppError("No Authority to create the Task", 401);
  }

  return prisma.task.create({
    data: {
      name: data.name,
      description: data.description,
      color: data.color,
      priority: data.priority,
      projectId: data.projectId,
      createdById: coworker.clientOwnerId!,
      startDate: data.startDate,
      dueDate: data.dueDate,
      status: "PENDING",
      annotators: data.annotatorIds
        ? {
            connect: data.annotatorIds.map((id) => ({ id })),
          }
        : undefined,
    },
    include: {
      annotators: true,
    },
  });
}


  async getProjectTaskStats(projectId: string) {
    const [pending, inProgress, completed, total] = await Promise.all([
      prisma.task.count({
        where: {
          projectId,
          status: "PENDING"
        }
      }),
      prisma.task.count({
        where: {
          projectId,
          status: "IN_PROGRESS"
        }
      }),
      prisma.task.count({
        where: {
          projectId,
          status: "COMPLETED"
        }
      }),
      prisma.task.count({
        where: {
          projectId
        }
      })
    ]);

    console.log('Project Task counts:', { pending, inProgress, completed, total });

    return {
      total,
      completed,
      inProgress,
      toDo: pending
    };
  }

}

export default new TaskService();
