import { Router } from "express";
import { RefundController } from "../../controllers/refund/refund.controller";
import { asyncHandler } from "../../middlewares/asyncHandler";
import authMiddleware from "../../middlewares/checkAuth";
import { hasRole } from "../../middlewares/checkRole";

const router = Router();
const controller = new RefundController();

router.post(
  "/create-refund",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(controller.create.bind(controller))
);
router.get(
  "/get-all-refunds",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHand<PERSON>(controller.getAll.bind(controller))
);
router.get(
  "/refund-by-id/:id",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(controller.getById.bind(controller))
);

export default router;
