import prisma from "../../prisma";
import { AppError } from "../../utils/ApiError";

class DashboardService {
  async getSummary(clientId: string) {
    // Get annotators assigned to this client
    const assignments = await prisma.assignment.findMany({
      where: { clientId },
      select: { developerId: true },
    });

    const annotatorIds = assignments.map((a) => a.developerId);

    // Count active annotators
    const activeAnnotators = await prisma.user.count({
      where: {
        id: { in: annotatorIds },
        role: "ANNOTATOR",
        // annotatorStatus: "ACTIVE",
      },
    });

    // Count active projects created by this client and assigned to their annotators
    const activeProjects = await prisma.project.count({
      where: {
        createdById: clientId,
        annotators: {
          some: { id: { in: annotatorIds } },
        },
        status: { in: ["PENDING", "IN_PROGRESS"] },
      },
    });

    // Nearest expiring subscription
    const expiringSubscription = await prisma.subscription.findFirst({
      where: {
        userId: clientId,
        status: "ACTIVE",
        endDate: {
          not: null,
          gt: new Date(),
        },
      },
      orderBy: { endDate: "asc" },
      select: { endDate: true },
    });

    const addOnPackagesCount = await prisma.clientPackageDetails.count({
      where: {
        clientId,
      },
    });

    return {
      activeAnnotators,
      activeProjects,
      expiringOn: expiringSubscription?.endDate || null,
      addOnPackages: addOnPackagesCount,
    };
  }

  async getCooworkerSummary(coowrokerID: string) {
    const coworkerDetails = await prisma.user.findFirst({
      where: {
        id: coowrokerID,
      },
    });

    const clientId = coworkerDetails?.clientOwnerId;

    if (!clientId) {
      throw new AppError("No Client is assigned", 404);
    }
    // Get annotators assigned to this client
    const assignments = await prisma.assignment.findMany({
      where: { clientId },
      select: { developerId: true },
    });

    const annotatorIds = assignments.map((a) => a.developerId);

    // Count active annotators
    const activeAnnotators = await prisma.user.count({
      where: {
        id: { in: annotatorIds },
        role: "ANNOTATOR",
        // annotatorStatus: "ACTIVE",
      },
    });

    // Count active projects created by this client and assigned to their annotators
    const activeProjects = await prisma.project.count({
      where: {
        createdById: clientId,
        annotators: {
          some: { id: { in: annotatorIds } },
        },
        status: { in: ["PENDING", "IN_PROGRESS"] },
      },
    });

    // Nearest expiring subscription
    const expiringSubscription = await prisma.subscription.findFirst({
      where: {
        userId: clientId,
        status: "ACTIVE",
        endDate: {
          not: null,
          gt: new Date(),
        },
      },
      orderBy: { endDate: "asc" },
      select: { endDate: true },
    });

    const addOnPackagesCount = await prisma.clientPackageDetails.count({
      where: {
        clientId,
      },
    });

    return {
      activeAnnotators,
      activeProjects,
      expiringOn: expiringSubscription?.endDate || null,
      addOnPackages: addOnPackagesCount,
    };
  }

  async getCoworkerSummary(clientId: string) {
    const cooworkerData = await prisma.user.findUnique({
      where: {
        id: clientId,
      },
    });

    if (!cooworkerData) {
      throw new AppError("No user found", 404);
    }
    // Get annotators assigned to this client
    const assignments = await prisma.assignment.findMany({
      where: { clientId: cooworkerData.clientOwnerId! },
      select: { developerId: true },
    });

    const annotatorIds = assignments.map((a) => a.developerId);

    // Count active annotators
    const activeAnnotators = await prisma.user.count({
      where: {
        id: { in: annotatorIds },
        role: "ANNOTATOR",
        annotatorStatus: "ACTIVE",
      },
    });

    // Count active projects created by this client and assigned to their annotators
    const activeProjects = await prisma.project.count({
      where: {
        createdById: clientId,
        annotators: {
          some: { id: { in: annotatorIds } },
        },
        status: { in: ["PENDING", "IN_PROGRESS"] },
      },
    });

    // Nearest expiring subscription
    const expiringSubscription = await prisma.subscription.findFirst({
      where: {
        userId: clientId,
        status: "ACTIVE",
        endDate: {
          not: null,
          gt: new Date(),
        },
      },
      orderBy: { endDate: "asc" },
      select: { endDate: true },
    });

    const addOnPackagesCount = await prisma.clientPackageDetails.count({
      where: {
        clientId,
      },
    });

    return {
      activeAnnotators,
      activeProjects,
      expiringOn: expiringSubscription?.endDate || null,
      addOnPackages: addOnPackagesCount,
    };
  }

  async getAnnotatorsByClientId(clientId: string) {
    return prisma.assignment.findMany({
      where: { clientId },
      include: {
        developer: {
          include: {
            _count: {
              select: {
                annotatorProjects: true,
              },
            },
          },
        },
      },
    });
  }

  async getAnnotatorsByCoWorkersId(clientId: string) {
    const cooworker = await prisma.user.findFirst({
      where: {
        id: clientId,
      },
    });

    if (!cooworker) {
      throw new AppError("No Cooworker found", 404);
    }
    return prisma.assignment.findMany({
      where: { clientId: cooworker.clientOwnerId! },
      include: {
        developer: {
          include: {
            _count: {
              select: {
                annotatorProjects: true,
              },
            },
          },
        },
      },
    });
  }

  async getCoordinatorSummary(coordinatorId: string) {
    // Get clients assigned to this coordinator
    const clientAssignments = await prisma.assignment.findMany({
      where: { coordinatorId },
      select: { clientId: true },
    });

    const clientIds = clientAssignments.map((a) => a.clientId);

    // Get annotators assigned to this coordinator
    const annotatorAssignments = await prisma.assignment.findMany({
      where: { coordinatorId },
      select: { developerId: true },
    });

    const annotatorIds = annotatorAssignments.map((a) => a.developerId);

    // Count unique clients
    const clientCount = new Set(clientIds).size;

    // Count active annotators
    const activeAnnotators = await prisma.user.count({
      where: {
        id: { in: annotatorIds },
        role: "ANNOTATOR",
        annotatorStatus: "ACTIVE",
      },
    });

    // Count active projects for this coordinator's clients
    const activeProjects = await prisma.project.count({
      where: {
        createdById: { in: clientIds },
        status: { in: ["PENDING", "IN_PROGRESS"] },
      },
    });

    return {
      clientCount,
      activeAnnotators,
      activeProjects,
    };
  }

  async getAdminSummary() {
    // Count all projects
    const totalProjects = await prisma.project.count();

    // Count active projects
    const activeProjects = await prisma.project.count({
      where: {
        status: { in: ["PENDING", "IN_PROGRESS"] },
      },
    });

    // Count coordinators
    const coordinatorCount = await prisma.user.count({
      where: {
        role: "PROJECT_COORDINATOR",
        accountStatus: "ACTIVE",
      },
    });

    // Count clients
    const clientCount = await prisma.user.count({
      where: {
        role: "CLIENT",
        accountStatus: "ACTIVE",
        isDeleted: false,
      },
    });

    // Count annotators
    const annotatorCount = await prisma.user.count({
      where: {
        role: "ANNOTATOR",
        accountStatus: "ACTIVE",
      },
    });

    // Count active annotators
    const activeAnnotators = await prisma.user.count({
      where: {
        role: "ANNOTATOR",
        accountStatus: "ACTIVE",
        annotatorStatus: "ACTIVE",
      },
    });

    return {
      totalProjects,
      activeProjects,
      coordinatorCount,
      clientCount,
      annotatorCount,
      activeAnnotators,
    };
  }
}

export const dashboardService = new DashboardService();
