import { Router } from 'express';
import OTPController from '../../controllers/otp/otp.controller';
import checkAuth from '../../middlewares/checkAuth';
import { otpRateLimiter } from '../../middlewares/otp-rate-limiter';

const router = Router();
const otpController = new OTPController();

/**
 * @swagger
 * /api/otp/send-verification:
 *   post:
 *     summary: Send OTP for email verification
 *     tags: [OTP]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address
 *               type:
 *                 type: string
 *                 enum: [verification, signup]
 *                 default: verification
 *                 description: Type of OTP verification
 *     responses:
 *       200:
 *         description: OTP sent successfully
 *       400:
 *         description: Invalid request
 *       404:
 *         description: User not found
 */
router.post('/send-verification', otpRateLimiter, otpController.sendVerificationOTP);

/**
 * @swagger
 * /api/otp/send-signup:
 *   post:
 *     summary: Send OTP for signup verification
 *     tags: [OTP]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - firstName
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address
 *               firstName:
 *                 type: string
 *                 description: User's first name
 *     responses:
 *       200:
 *         description: Signup OTP sent successfully
 *       400:
 *         description: Invalid request
 */
router.post('/send-signup', otpRateLimiter, otpController.sendSignupOTP);

/**
 * @swagger
 * /api/otp/verify:
 *   post:
 *     summary: Verify OTP code
 *     tags: [OTP]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - otpCode
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address
 *               otpCode:
 *                 type: string
 *                 description: 6-digit OTP code
 *     responses:
 *       200:
 *         description: OTP verified successfully
 *       400:
 *         description: Invalid or expired OTP
 */
router.post('/verify', otpRateLimiter, otpController.verifyOTP);

/**
 * @swagger
 * /api/otp/resend:
 *   post:
 *     summary: Resend OTP code
 *     tags: [OTP]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address
 *               type:
 *                 type: string
 *                 enum: [verification, signup]
 *                 default: verification
 *                 description: Type of OTP verification
 *     responses:
 *       200:
 *         description: OTP resent successfully
 *       400:
 *         description: Invalid request
 *       404:
 *         description: User not found
 *       429:
 *         description: Too many requests
 */
router.post('/resend', otpRateLimiter, otpController.resendOTP);

/**
 * @swagger
 * /api/otp/invite-coworker:
 *   post:
 *     summary: Send coworker invitation email
 *     tags: [OTP]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - coworkerEmail
 *               - coworkerName
 *               - invitationToken
 *             properties:
 *               coworkerEmail:
 *                 type: string
 *                 format: email
 *                 description: Coworker's email address
 *               coworkerName:
 *                 type: string
 *                 description: Coworker's name
 *               role:
 *                 type: string
 *                 default: Coworker
 *                 description: Role for the coworker
 *               invitationToken:
 *                 type: string
 *                 description: Unique invitation token
 *     responses:
 *       200:
 *         description: Invitation sent successfully
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Authentication required
 */
router.post('/invite-coworker', checkAuth, otpController.sendCoworkerInvitation);

export default router;