export const usagelimitwarningTemplate = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Usage Limit Warning</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; background: white; }
        .header { background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); color: white; padding: 30px 20px; text-align: center; }
        .content { padding: 30px 20px; }
        .alert-box { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 10px 0; }
        .button:hover { background: #0056b3; }
        .button.upgrade { background: #28a745; }
        .button.upgrade:hover { background: #218838; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; border-top: 1px solid #dee2e6; }
        .warning { color: #856404; }
        .usage-bar { background: #e9ecef; border-radius: 10px; height: 20px; margin: 10px 0; overflow: hidden; }
        .usage-fill { background: linear-gradient(90deg, #28a745 0%, #ffc107 70%, #dc3545 100%); height: 100%; transition: width 0.3s ease; }
        .usage-info { background: #e9ecef; padding: 15px; border-radius: 6px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Usage Limit Warning</h1>
        </div>
        <div class="content">
            <p>Hello {{userName}},</p>
            
            <div class="alert-box">
                <h3 class="warning">⚠️ Approaching Limit</h3>
                <p><strong>{{alert.message}}</strong></p>
            </div>

            <div class="usage-info">
                <h4>Current Usage:</h4>
                <p><strong>{{alert.data.resource}}:</strong> {{alert.data.current}} / {{alert.data.limit}}</p>
                <div class="usage-bar">
                    <div class="usage-fill" style="width: {{alert.data.percentage}}%"></div>
                </div>
                <p><small>{{alert.data.percentage}}% of your limit used</small></p>
            </div>

            <h4>What happens when you reach the limit?</h4>
            <ul>
                <li>🚫 You won't be able to create new {{alert.data.resource}}</li>
                <li>⚠️ Existing {{alert.data.resource}} will continue to work</li>
                <li>📈 Consider upgrading for higher limits</li>
            </ul>

            <h4>Recommended Actions:</h4>
            <ol>
                <li><strong>Upgrade your plan</strong> for higher limits</li>
                <li><strong>Review and clean up</strong> unused {{alert.data.resource}}</li>
                <li><strong>Contact support</strong> if you need assistance</li>
            </ol>

            <p style="text-align: center;">
                <a href="{{dashboardUrl}}/packages" class="button upgrade">Upgrade Plan</a>
                <a href="{{dashboardUrl}}/{{alert.data.resource}}" class="button">Manage {{alert.data.resource}}</a>
            </p>

            <p>Need help choosing the right plan? Our team can help you find the perfect solution for your needs.</p>
        </div>
        <div class="footer">
            <p>Best regards,<br><strong>Your Subscription Team</strong></p>
            <p><small>This is an automated notification. Please do not reply to this email.</small></p>
        </div>
    </div>
</body>
</html>
`;
