const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000/api';

// Test configuration
const testConfig = {
  userToken: 'your-user-jwt-token-here', // Replace with actual user token
  adminToken: 'your-admin-jwt-token-here', // Replace with actual admin token
  packageId: 'your-package-id-here', // Replace with actual package ID
  screenshotPath: './test-screenshot.png' // Create a test image file
};

async function testBankTransferFlow() {
  console.log('🏦 Testing Bank Transfer Payment Flow\n');

  try {
    // Test 1: Get bank transfer information
    console.log('1. Testing bank transfer info endpoint...');
    const infoResponse = await axios.get(`${BASE_URL}/bank-transfer/info`);
    console.log('✅ Bank transfer info retrieved:', infoResponse.data.data);
    console.log('');

    // Test 2: Submit bank transfer payment (requires user authentication)
    console.log('2. Testing bank transfer payment submission...');
    
    // Create form data for file upload
    const formData = new FormData();
    formData.append('packageId', testConfig.packageId);
    formData.append('amount', '99.99');
    formData.append('currency', 'USD');
    formData.append('transactionId', 'TXN' + Date.now());
    formData.append('bankHolderName', 'John Doe');
    formData.append('accountNumber', '**********');
    formData.append('ifscCode', 'BANK0001234');
    formData.append('bankName', 'Test Bank');
    
    // Add a test screenshot (create a dummy file if it doesn't exist)
    if (!fs.existsSync(testConfig.screenshotPath)) {
      console.log('Creating dummy screenshot file for testing...');
      fs.writeFileSync(testConfig.screenshotPath, 'dummy image content');
    }
    formData.append('screenshot', fs.createReadStream(testConfig.screenshotPath));

    const submitResponse = await axios.post(
      `${BASE_URL}/bank-transfer/submit`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${testConfig.userToken}`
        }
      }
    );
    console.log('✅ Bank transfer payment submitted:', submitResponse.data);
    
    const paymentId = submitResponse.data.data.bankTransferPayment.paymentId;
    const bankTransferPaymentId = submitResponse.data.data.bankTransferPayment.id;
    console.log('');

    // Test 3: Get user's bank transfer payments
    console.log('3. Testing user bank transfer payments retrieval...');
    const userPaymentsResponse = await axios.get(
      `${BASE_URL}/bank-transfer/my-payments`,
      {
        headers: {
          'Authorization': `Bearer ${testConfig.userToken}`
        }
      }
    );
    console.log('✅ User payments retrieved:', userPaymentsResponse.data.data.length, 'payments');
    console.log('');

    // Test 4: Get specific payment details
    console.log('4. Testing payment details retrieval...');
    const paymentDetailsResponse = await axios.get(
      `${BASE_URL}/bank-transfer/payment/${paymentId}`,
      {
        headers: {
          'Authorization': `Bearer ${testConfig.userToken}`
        }
      }
    );
    console.log('✅ Payment details retrieved:', paymentDetailsResponse.data.data.status);
    console.log('');

    // Test 5: Admin - Get pending payments
    console.log('5. Testing admin pending payments retrieval...');
    const pendingPaymentsResponse = await axios.get(
      `${BASE_URL}/bank-transfer/admin/pending?page=1&limit=10`,
      {
        headers: {
          'Authorization': `Bearer ${testConfig.adminToken}`
        }
      }
    );
    console.log('✅ Pending payments retrieved:', pendingPaymentsResponse.data.data.payments.length, 'payments');
    console.log('');

    // Test 6: Admin - Verify payment (approve)
    console.log('6. Testing admin payment verification (approve)...');
    const verifyResponse = await axios.post(
      `${BASE_URL}/bank-transfer/admin/verify/${bankTransferPaymentId}`,
      {
        status: 'VERIFIED',
        adminNotes: 'Payment verified successfully. Transaction confirmed in bank records.'
      },
      {
        headers: {
          'Authorization': `Bearer ${testConfig.adminToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    console.log('✅ Payment verified:', verifyResponse.data.message);
    console.log('');

    // Test 7: Get analytics (admin only)
    console.log('7. Testing bank transfer analytics...');
    const analyticsResponse = await axios.get(
      `${BASE_URL}/bank-transfer/admin/analytics`,
      {
        headers: {
          'Authorization': `Bearer ${testConfig.adminToken}`
        }
      }
    );
    console.log('✅ Analytics retrieved:', analyticsResponse.data.data);
    console.log('');

    // Test 7.5: Get verified users list (admin only)
    console.log('7.5. Testing verified users list...');
    const verifiedUsersResponse = await axios.get(
      `${BASE_URL}/bank-transfer/admin/verified-users?page=1&limit=5`,
      {
        headers: {
          'Authorization': `Bearer ${testConfig.adminToken}`
        }
      }
    );
    console.log('✅ Verified users retrieved:', verifiedUsersResponse.data.data.verifiedUsers.length, 'users');
    console.log('✅ Total unique users:', verifiedUsersResponse.data.data.pagination.totalUniqueUsers);
    console.log('');

    // Test 8: Check updated payment status
    console.log('8. Testing updated payment status...');
    const updatedPaymentResponse = await axios.get(
      `${BASE_URL}/bank-transfer/payment/${paymentId}`,
      {
        headers: {
          'Authorization': `Bearer ${testConfig.userToken}`
        }
      }
    );
    console.log('✅ Updated payment status:', updatedPaymentResponse.data.data.status);
    console.log('✅ Verification date:', updatedPaymentResponse.data.data.verifiedAt);
    console.log('');

    console.log('🎉 All bank transfer tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('\n💡 Note: Make sure to update the JWT tokens in testConfig');
    }
    
    if (error.response?.status === 400) {
      console.log('\n💡 Note: Make sure to update the packageId in testConfig');
    }
  }
}

async function testBankTransferRejection() {
  console.log('\n🚫 Testing Bank Transfer Payment Rejection Flow\n');

  try {
    // Submit another payment for rejection testing
    console.log('1. Submitting payment for rejection test...');
    
    const formData = new FormData();
    formData.append('packageId', testConfig.packageId);
    formData.append('amount', '149.99');
    formData.append('currency', 'USD');
    formData.append('transactionId', 'TXN_REJECT_' + Date.now());
    formData.append('bankHolderName', 'Jane Smith');
    formData.append('accountNumber', '**********');
    formData.append('ifscCode', 'BANK0005678');
    formData.append('bankName', 'Another Test Bank');
    formData.append('screenshot', fs.createReadStream(testConfig.screenshotPath));

    const submitResponse = await axios.post(
      `${BASE_URL}/bank-transfer/submit`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${testConfig.userToken}`
        }
      }
    );
    
    const bankTransferPaymentId = submitResponse.data.data.bankTransferPayment.id;
    console.log('✅ Payment submitted for rejection test');
    console.log('');

    // Reject the payment
    console.log('2. Testing admin payment rejection...');
    const rejectResponse = await axios.post(
      `${BASE_URL}/bank-transfer/admin/verify/${bankTransferPaymentId}`,
      {
        status: 'REJECTED',
        adminNotes: 'Payment screenshot is unclear. Please resubmit with a clearer image showing all transaction details.'
      },
      {
        headers: {
          'Authorization': `Bearer ${testConfig.adminToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    console.log('✅ Payment rejected:', rejectResponse.data.message);
    console.log('');

    console.log('🎉 Bank transfer rejection test completed successfully!');

  } catch (error) {
    console.error('❌ Rejection test failed:', error.response?.data || error.message);
  }
}

// Run tests
async function runAllTests() {
  console.log('🚀 Starting Bank Transfer Payment System Tests\n');
  console.log('📋 Test Configuration:');
  console.log('- Base URL:', BASE_URL);
  console.log('- Package ID:', testConfig.packageId);
  console.log('- Screenshot Path:', testConfig.screenshotPath);
  console.log('- User Token Set:', !!testConfig.userToken);
  console.log('- Admin Token Set:', !!testConfig.adminToken);
  console.log('\n' + '='.repeat(60) + '\n');

  await testBankTransferFlow();
  await testBankTransferRejection();

  console.log('\n' + '='.repeat(60));
  console.log('🏁 All tests completed!');
  console.log('\n💡 Next steps:');
  console.log('1. Check email notifications sent to users and admins');
  console.log('2. Verify subscription status changes in database');
  console.log('3. Test frontend integration with these APIs');
  console.log('4. Set up proper file upload handling for production');
}

// Cleanup function
function cleanup() {
  if (fs.existsSync(testConfig.screenshotPath)) {
    fs.unlinkSync(testConfig.screenshotPath);
    console.log('🧹 Cleaned up test files');
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Tests interrupted');
  cleanup();
  process.exit(0);
});

process.on('exit', cleanup);

// Run the tests
runAllTests().catch(console.error);