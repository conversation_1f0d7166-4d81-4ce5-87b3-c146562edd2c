-- CreateTable
CREATE TABLE "PackageFeatures" (
    "id" TEXT NOT NULL,
    "packageId" TEXT NOT NULL,
    "rule" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PackageFeatures_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "PackageFeatures" ADD CONSTRAINT "PackageFeatures_packageId_fkey" FOREIGN KEY ("packageId") REFERENCES "Package"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
