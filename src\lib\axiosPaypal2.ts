// paypalClient.ts
import axios, { InternalAxiosRequestConfig } from "axios";
import dotenv from "dotenv";

dotenv.config();

const PAYPAL_API = process.env.PAYPAL_API!;
const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID!;
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET!;

const basicAuth = Buffer.from(
  `${PAYPAL_CLIENT_ID}:${PAYPAL_CLIENT_SECRET}`
).toString("base64");

let tokenCache = {
  token: "",
  expiresAt: 0,
};

export async function fetchAccessToken(): Promise<string> {
  const response = await axios.post(
    `${PAYPAL_API}/v1/oauth2/token`,
    "grant_type=client_credentials",
    {
      headers: {
        Authorization: `Basic ${basicAuth}`,
        "Content-Type": "application/x-www-form-urlencoded",
      },
    }
  );

  const { access_token, expires_in } = response.data;

  tokenCache.token = access_token;
  tokenCache.expiresAt = Date.now() + expires_in * 1000 - 60_000; // expire 1 min early

  return access_token;
}

const paypalClient2 = axios.create({
  baseURL: PAYPAL_API,
  headers: { "Content-Type": "application/json" },
});

paypalClient2.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    if (!tokenCache.token || Date.now() >= tokenCache.expiresAt) {
      await fetchAccessToken();
    }

    config.headers.set("Authorization", `Bearer ${tokenCache.token}`);

    return config;
  },
  (error) => Promise.reject(error)
);

export async function verifyPaypalWebhook(req: any): Promise<boolean> {
  const token = await fetchAccessToken();

  const {
    "paypal-transmission-id": transmissionId,
    "paypal-transmission-time": timestamp,
    "paypal-transmission-sig": signature,
    "paypal-cert-url": certUrl,
    "paypal-auth-algo": authAlgo,
  } = req.headers;

  const webhookId = process.env.PAYPAL_WEBHOOK_ID!;
  const body = {
    auth_algo: authAlgo,
    cert_url: certUrl,
    transmission_id: transmissionId,
    transmission_sig: signature,
    transmission_time: timestamp,
    webhook_id: webhookId,
    webhook_event: req.body,
  };

  const res = await paypalClient2.post(
    "/v1/notifications/verify-webhook-signature",
    body,
    {
      headers: { Authorization: `Bearer ${token}` },
    }
  );

  return res.data.verification_status === "SUCCESS";
}

export default paypalClient2;
