
import { Prisma } from "@prisma/client";
import prisma from "../../prisma";

export interface Notification {
  id: string;
  userId: string;
  type: string;
  message: string;
  metadata: {
    groupId?: string;
    messageId?: string;
    senderName?: string;
    groupName?: string;
    messageText?: string;
    isMention?: boolean;
  };
  isRead: boolean;
  createdAt: string;
}

export class NotificationService {
  async getNotifications(
    userId: string,
    query: {
      page?: number;
      limit?: number;
      unreadOnly?: boolean;
      search?: string;
    }
  ): Promise<{
    notifications: Notification[];
    totalCount: number;
    totalPages: number;
    currentPage: number;
  }> {
    const { page = 1, limit = 10, unreadOnly = false, search } = query;
    const skip = (page - 1) * limit;
    const take = limit;

    const where: Prisma.NotificationWhereInput = {
      userId,
      isRead: unreadOnly ? false : undefined,
      ...(search && {
        OR: [
          { message: { contains: search, mode: Prisma.QueryMode.insensitive } },
          {
            metadata: {
              path: ["senderName"],
              string_contains: search,
              mode: Prisma.QueryMode.insensitive,
            },
          },
          {
            metadata: {
              path: ["groupName"],
              string_contains: search,
              mode: Prisma.QueryMode.insensitive,
            },
          },
          {
            metadata: {
              path: ["messageText"],
              string_contains: search,
              mode: Prisma.QueryMode.insensitive,
            },
          },
        ],
      }),
    };

    const [notifications, totalCount] = await Promise.all([
      prisma.notification.findMany({
        where,
        skip,
        take,
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          userId: true,
          type: true,
          message: true,
          metadata: true,
          isRead: true,
          createdAt: true,
        },
      }),
      prisma.notification.count({ where }),
    ]);

    return {
      notifications: notifications.map((n) => ({
        ...n,
        metadata: typeof n.metadata === "object" && n.metadata !== null && !Array.isArray(n.metadata)
          ? n.metadata as Notification["metadata"]
          : {},
        createdAt: n.createdAt instanceof Date ? n.createdAt.toISOString() : n.createdAt,
      })),
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
    };
  }

  async markNotificationRead(notificationId: string, userId: string): Promise<void> {
    await prisma.notification.update({
      where: {
        id: notificationId,
        userId,
      },
      data: { isRead: true },
    });
  }

  async createNotification(data: {
    userId: string;
    type: string;
    title?: string;
    message: string;
    data?: any;
    priority?: string;
  }): Promise<void> {
    await prisma.notification.create({
      data: {
        userId: data.userId,
        type: data.type as any,
        message: data.message,
        metadata: data.data || {},
        isRead: false,
      },
    });
  }
}