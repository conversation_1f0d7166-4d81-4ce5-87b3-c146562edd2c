/**
 * Test script for soft delete functionality
 * This script demonstrates the enhanced delete behavior for coordinators and annotators
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testSoftDelete() {
  console.log('🧪 Testing Soft Delete Functionality\n');

  try {
    // Test case 1: Try to delete a coordinator assigned to clients
    console.log('📋 Test Case 1: Coordinator assigned to clients');
    
    // Find a coordinator with client assignments
    const coordinatorWithAssignments = await prisma.user.findFirst({
      where: {
        role: 'PROJECT_COORDINATOR',
        assignmentsAsCoordinator: {
          some: {}
        }
      },
      include: {
        assignmentsAsCoordinator: {
          include: {
            client: {
              select: {
                name: true,
                email: true
              }
            }
          }
        },
        projectsAsCoordinator: true
      }
    });

    if (coordinatorWithAssignments) {
      console.log(`Found coordinator: ${coordinatorWithAssignments.name} (${coordinatorWithAssignments.email})`);
      console.log(`- Client assignments: ${coordinatorWithAssignments.assignmentsAsCoordinator.length}`);
      coordinatorWithAssignments.assignmentsAsCoordinator.forEach(assignment => {
        console.log(`  - Assigned to client: ${assignment.client.name}`);
      });
      console.log(`- Projects as coordinator: ${coordinatorWithAssignments.projectsAsCoordinator.length}`);
      console.log('❌ This coordinator CANNOT be deleted - will throw error due to client assignments\n');
    } else {
      console.log('❌ No coordinator with client assignments found\n');
    }

    // Test case 2: Try to delete an annotator assigned to clients
    console.log('📋 Test Case 2: Annotator assigned to clients');
    
    const annotatorWithAssignments = await prisma.user.findFirst({
      where: {
        role: 'ANNOTATOR',
        assignmentsAsDeveloper: {
          some: {}
        }
      },
      include: {
        assignmentsAsDeveloper: {
          include: {
            client: {
              select: {
                name: true,
                email: true
              }
            }
          }
        },
        annotatorProjects: true,
        annotatedTasks: true,
        createdTasks: true,
        timeLogs: true
      }
    });

    if (annotatorWithAssignments) {
      console.log(`Found annotator: ${annotatorWithAssignments.name} (${annotatorWithAssignments.email})`);
      console.log(`- Client assignments: ${annotatorWithAssignments.assignmentsAsDeveloper.length}`);
      annotatorWithAssignments.assignmentsAsDeveloper.forEach(assignment => {
        console.log(`  - Assigned to client: ${assignment.client.name}`);
      });
      console.log(`- Annotator projects: ${annotatorWithAssignments.annotatorProjects.length}`);
      console.log(`- Annotated tasks: ${annotatorWithAssignments.annotatedTasks.length}`);
      console.log(`- Created tasks: ${annotatorWithAssignments.createdTasks.length}`);
      console.log(`- Time logs: ${annotatorWithAssignments.timeLogs.length}`);
      console.log('❌ This annotator CANNOT be deleted - will throw error due to client assignments\n');
    } else {
      console.log('❌ No annotator with client assignments found\n');
    }

    // Test case 3: Find users without relationships
    console.log('📋 Test Case 3: Users without relationships');
    
    const coordinatorWithoutRelationships = await prisma.user.findFirst({
      where: {
        role: 'PROJECT_COORDINATOR',
        assignmentsAsCoordinator: {
          none: {}
        },
        projectsAsCoordinator: {
          none: {}
        },
        assignedProjects: {
          none: {}
        }
      }
    });

    if (coordinatorWithoutRelationships) {
      console.log(`Found coordinator without relationships: ${coordinatorWithoutRelationships.name}`);
      console.log('✅ This coordinator should be hard deleted when deletion is attempted\n');
    } else {
      console.log('❌ No coordinator without relationships found\n');
    }

    const annotatorWithoutRelationships = await prisma.user.findFirst({
      where: {
        role: 'ANNOTATOR',
        assignmentsAsDeveloper: {
          none: {}
        },
        annotatorProjects: {
          none: {}
        },
        annotatedTasks: {
          none: {}
        },
        createdTasks: {
          none: {}
        },
        timeLogs: {
          none: {}
        }
      }
    });

    if (annotatorWithoutRelationships) {
      console.log(`Found annotator without relationships: ${annotatorWithoutRelationships.name}`);
      console.log('✅ This annotator should be hard deleted when deletion is attempted\n');
    } else {
      console.log('❌ No annotator without relationships found\n');
    }

    // Test case 4: Check for already soft-deleted users
    console.log('📋 Test Case 4: Already soft-deleted users');
    
    const softDeletedUsers = await prisma.user.findMany({
      where: {
        OR: [
          { isDeleted: true },
          { accountStatus: 'DELETED' }
        ],
        role: {
          in: ['ANNOTATOR', 'PROJECT_COORDINATOR']
        }
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isDeleted: true,
        accountStatus: true
      }
    });

    if (softDeletedUsers.length > 0) {
      console.log(`Found ${softDeletedUsers.length} soft-deleted users:`);
      softDeletedUsers.forEach(user => {
        console.log(`- ${user.name} (${user.role}) - isDeleted: ${user.isDeleted}, status: ${user.accountStatus}`);
      });
      console.log('✅ These users should not appear in normal queries\n');
    } else {
      console.log('❌ No soft-deleted users found\n');
    }

    console.log('🎯 Summary:');
    console.log('- Users assigned to clients CANNOT be deleted - will throw error with client names');
    console.log('- Users with other relationships (projects, tasks, etc.) will be soft deleted');
    console.log('- Users without any relationships will be hard deleted (completely removed)');
    console.log('- Soft-deleted users are excluded from normal queries by default');
    console.log('- Use includeDeleted=true query parameter to include soft-deleted users');
    console.log('- Email is anonymized during soft delete to prevent conflicts');
    console.log('- To delete a user assigned to clients, first unassign them from all clients');

  } catch (error) {
    console.error('❌ Error during testing:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testSoftDelete();