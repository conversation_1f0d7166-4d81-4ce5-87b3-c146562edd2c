import { Router } from "express";
import { FAQController } from "../../controllers/faq/faq.controller";
import { asyncHandler } from "../../middlewares/asyncHandler";
import authMiddleware from "../../middlewares/checkAuth";

const router = Router();
const controller = new FAQController();

router.post("/create-faq", authMiddleware, asyncHandler(controller.create.bind(controller)));
router.get("/get-all-faq", asyncHandler(controller.getAll.bind(controller)));
router.get("/get-by-id/:id", asyncHandler(controller.getById.bind(controller)));
router.put("/update-faq/:id", authMiddleware, asyncHandler(controller.update.bind(controller)));
router.delete("/delete-faq/:id", authMiddleware, asyncHandler(controller.delete.bind(controller)));

export default router;
