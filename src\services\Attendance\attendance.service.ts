import { parse, startOfDay, endOfDay } from "date-fns";
import prisma from "../../prisma";
import { ArrivalStatus, AttendanceStatus } from "@prisma/client";
import { writeFileSync } from "fs";
import { Parser } from "json2csv";
import { AppError } from "../../utils/ApiError";

class AttendanceService {
  // async clockIn(userId: string) {
  //   const today = new Date();
  //   const start = new Date(
  //     today.getFullYear(),
  //     today.getMonth(),
  //     today.getDate()
  //   );
  //   const end = new Date(start);
  //   end.setHours(23, 59, 59, 999);

  //   const existing = await prisma.attendanceSummary.findFirst({
  //     where: {
  //       userId,
  //       date: { gte: start, lte: end },
  //     },
  //   });

  //   if (existing?.timeIn) throw new AppError("Already clocked in.", 400);

  //   const now = new Date();
  //   const expectedTime = new Date(now);
  //   expectedTime.setHours(9, 0, 0, 0);

  //   const diffMin = Math.floor((+now - +expectedTime) / 60000);

  //   let arrivalStatus: ArrivalStatus = ArrivalStatus.ON_TIME;
  //   if (diffMin > 0 && diffMin <= 59) {
  //     arrivalStatus = ArrivalStatus.MINUTES_LATE;
  //   } else if (diffMin >= 60) {
  //     arrivalStatus = ArrivalStatus.HOURS_LATE;
  //   }

  //   let attendanceStatus: AttendanceStatus = AttendanceStatus.ACTIVE;

  //   if (existing) {
  //     return prisma.attendanceSummary.update({
  //       where: {
  //         id: existing.id,
  //       },
  //       data: {
  //         timeIn: now,
  //         status: attendanceStatus,
  //         arrivalStatus,
  //       },
  //     });
  //   }

  //   // Otherwise, create new record
  //   return prisma.attendanceSummary.create({
  //     data: {
  //       userId,
  //       date: today,
  //       timeIn: now,
  //       status: attendanceStatus,
  //       arrivalStatus,
  //     },
  //   });
  // }
  async clockIn(userId: string) {
    const now = new Date();
    const startOfDay = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate()
    );
    const endOfDay = new Date(startOfDay);
    endOfDay.setHours(23, 59, 59, 999);

    // Try to find today's attendance summary with last clockSession
    let attendanceSummary = await prisma.attendanceSummary.findFirst({
      where: {
        userId,
        date: {
          gte: startOfDay,
          lte: endOfDay,
        },
      },
      include: {
        clockSessions: {
          orderBy: { timeIn: "desc" },
          take: 1,
        },
      },
    });

    // ⛔ If last session exists and is not closed, prevent new clock-in
    const lastSession = attendanceSummary?.clockSessions[0];
    if (lastSession && !lastSession.timeOut) {
      throw new AppError("Already clocked in. Please clock out first.", 400);
    }

    // 🕘 Determine arrival status if it's the first session
    let arrivalStatus: ArrivalStatus | undefined;
    if (!attendanceSummary) {
      const expectedTime = new Date(now);
      expectedTime.setHours(9, 0, 0, 0);
      const diffMin = Math.floor((+now - +expectedTime) / 60000);

      if (diffMin > 0 && diffMin <= 59) {
        arrivalStatus = ArrivalStatus.MINUTES_LATE;
      } else if (diffMin >= 60) {
        arrivalStatus = ArrivalStatus.HOURS_LATE;
      } else {
        arrivalStatus = ArrivalStatus.ON_TIME;
      }

      // ➕ Create new attendance summary if it doesn't exist
      attendanceSummary = await prisma.attendanceSummary.create({
        data: {
          userId,
          date: startOfDay,
          status: AttendanceStatus.ACTIVE,
          arrivalStatus,
        },
        include: {
          clockSessions: {
            orderBy: { timeIn: "desc" },
            take: 1,
          },
        },
      });
    }

    await prisma.attendanceSummary.update({
      where: {
        id: attendanceSummary.id,
      },
      data: {
        status: AttendanceStatus.ACTIVE,
      },
    });

    // ✅ Record new clock-in session
    await prisma.clockSession.create({
      data: {
        attendanceSummaryId: attendanceSummary.id,
        timeIn: now,
      },
    });

    return { message: "Clock-in recorded." };
  }

  // async clockOut(userId: string) {
  //   const today = new Date();
  //   // Use the same date calculation approach as in clockIn
  //   const start = new Date(
  //     today.getFullYear(),
  //     today.getMonth(),
  //     today.getDate()
  //   );
  //   const end = new Date(start);
  //   end.setHours(23, 59, 59, 999);

  //   const record = await prisma.attendanceSummary.findFirst({
  //     where: {
  //       userId,
  //       date: { gte: start, lte: end },
  //     },
  //   });

  //   if (!record || !record.timeIn)
  //     throw new AppError("Clock-in required before clock-out.", 400);

  //   const timeOut = new Date();
  //   const workedMinutes =
  //     Math.floor((+timeOut - +record.timeIn) / 60000) - record.consumedBreak;

  //   let updatedStatus: AttendanceStatus = AttendanceStatus.CLOCKED_OUT;

  //   if (record.status === AttendanceStatus.ON_BREAK) {
  //     updatedStatus = AttendanceStatus.ON_BREAK;
  //   }

  //   return prisma.attendanceSummary.update({
  //     where: { id: record.id },
  //     data: {
  //       timeOut,
  //       workingMinutes: workedMinutes,
  //       status: updatedStatus,
  //     },
  //   });
  // }

  async clockOut(userId: string) {
    const now = new Date();
    const startOfDay = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate()
    );
    const endOfDay = new Date(startOfDay);
    endOfDay.setHours(23, 59, 59, 999);

    // Get today's attendance summary and last open clock session
    const attendanceSummary = await prisma.attendanceSummary.findFirst({
      where: {
        userId,
        date: { gte: startOfDay, lte: endOfDay },
      },
      include: {
        clockSessions: {
          where: { timeOut: null },
          orderBy: { timeIn: "desc" },
          take: 1,
        },
        breakSessions: {
          where: { endTime: null },
          orderBy: { startTime: "desc" },
          take: 1,
        },
      },
    });

    if (!attendanceSummary) {
      throw new AppError("Clock-in required before clock-out.", 400);
    }

    const openSession = attendanceSummary.clockSessions[0];

    if (!openSession) {
      throw new AppError("No active clock-in session found.", 400);
    }

    let breakEndMessage = "";

    // Check if user is currently on break and automatically end it
    if (attendanceSummary.status === AttendanceStatus.ON_BREAK) {
      const activeBreakSession = attendanceSummary.breakSessions[0];
      
      if (activeBreakSession) {
        // Calculate break duration
        const breakDuration = Math.floor((+now - +activeBreakSession.startTime) / 60000);
        
        // Ensure break duration is at least 1 minute (avoid 0 minute breaks)
        const actualBreakDuration = Math.max(breakDuration, 1);
        
        // Check if break duration exceeds available break time
        if (actualBreakDuration > attendanceSummary.availableBreak) {
          console.warn(`Break duration (${actualBreakDuration} mins) exceeds available break time (${attendanceSummary.availableBreak} mins) for user ${userId}`);
          
          // Cap the break time to available break time
          const cappedBreakDuration = Math.max(attendanceSummary.availableBreak, 0);
          
          // End the break session
          await prisma.breakSession.update({
            where: { id: activeBreakSession.id },
            data: { endTime: now },
          });

          // Update attendance summary with capped break time
          await prisma.attendanceSummary.update({
            where: { id: attendanceSummary.id },
            data: {
              consumedBreak: { increment: cappedBreakDuration },
              availableBreak: { decrement: cappedBreakDuration },
            },
          });

          breakEndMessage = ` Break automatically ended (capped at ${cappedBreakDuration} minutes due to exceeding available break time).`;
        } else {
          // End the break session normally
          await prisma.breakSession.update({
            where: { id: activeBreakSession.id },
            data: { endTime: now },
          });

          // Update attendance summary with actual break time
          await prisma.attendanceSummary.update({
            where: { id: attendanceSummary.id },
            data: {
              consumedBreak: { increment: actualBreakDuration },
              availableBreak: { decrement: actualBreakDuration },
            },
          });

          breakEndMessage = ` Break automatically ended (${actualBreakDuration} minutes consumed).`;
        }

        console.log(`Auto-ended break for user ${userId}: ${actualBreakDuration} minutes (original: ${breakDuration} minutes)`);
      } else {
        // Handle edge case: status is ON_BREAK but no active break session found
        console.warn(`User ${userId} has ON_BREAK status but no active break session found. Resetting status.`);
        breakEndMessage = ` Status corrected (was ON_BREAK but no active break session found).`;
      }
    }

    // Calculate worked minutes for this session
    const workedMinutes = Math.floor((+now - +openSession.timeIn) / 60000);

    // Update the clockSession
    await prisma.clockSession.update({
      where: {
        id: openSession.id,
      },
      data: {
        timeOut: now,
      },
    });

    // Update attendance summary's workingMinutes (add this session's time)
    const updatedWorkingMinutes =
      attendanceSummary.workingMinutes + workedMinutes;

    await prisma.attendanceSummary.update({
      where: {
        id: attendanceSummary.id,
      },
      data: {
        workingMinutes: updatedWorkingMinutes,
        status: AttendanceStatus.CLOCKED_OUT,
      },
    });

    return { 
      message: `Clock-out recorded.${breakEndMessage}`,
      breakAutoEnded: attendanceSummary.status === AttendanceStatus.ON_BREAK,
      breakDuration: attendanceSummary.status === AttendanceStatus.ON_BREAK && attendanceSummary.breakSessions[0] 
        ? Math.floor((+now - +attendanceSummary.breakSessions[0].startTime) / 60000)
        : 0
    };
  }

  async startBreak(userId: string) {
    const today = new Date();
    const start = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );
    const end = new Date(start);
    end.setHours(23, 59, 59, 999);

    const attendanceRecord = await prisma.attendanceSummary.findFirst({
      where: {
        userId,
        date: {
          gte: start,
          lte: end,
        },
      },
      include: {
        clockSessions: {
          orderBy: { timeIn: "desc" },
          take: 1,
        },
      },
    });

    if (!attendanceRecord)
      throw new AppError(
        "No attendance record found. Please clock in first.",
        400
      );

    console.log(attendanceRecord, "attendende");

    const lastClockSession = attendanceRecord.clockSessions[0];

    if (!lastClockSession || !lastClockSession.timeIn)
      throw new AppError("You must clock in before taking a break.", 400);

    if (lastClockSession.timeOut)
      throw new AppError(
        "You are not currently clocked in. Please clock in first.",
        400
      );

    if (attendanceRecord.status === AttendanceStatus.ON_BREAK)
      throw new AppError("You are already on a break.", 400);

    if (attendanceRecord.status === AttendanceStatus.CLOCKED_OUT)
      throw new AppError("You have already clocked out for today.", 400);

    if (attendanceRecord.status !== AttendanceStatus.ACTIVE)
      throw new AppError("You must be in active status to take a break.", 400);

    await prisma.attendanceSummary.update({
      where: { id: attendanceRecord.id },
      data: {
        status: AttendanceStatus.ON_BREAK,
      },
    });

    return prisma.breakSession.create({
      data: {
        attendanceId: attendanceRecord.id,
        userId: userId,
        startTime: new Date(),
      },
    });
  }

  async endBreak(userId: string) {
    const today = new Date();
    const record = await prisma.attendanceSummary.findFirst({
      where: {
        userId,
        date: {
          gte: startOfDay(today),
          lte: endOfDay(today),
        },
      },
    });

    if (!record) throw new AppError("No attendance found.", 400);

    await prisma.attendanceSummary.update({
      where: { id: record.id },
      data: {
        status: AttendanceStatus.ACTIVE,
      },
    });

    const lastBreak = await prisma.breakSession.findFirst({
      where: {
        attendanceId: record.id,
        endTime: null,
      },
      orderBy: { startTime: "desc" },
    });

    if (!lastBreak) throw new AppError("No active break found.", 400);

    const endTime = new Date();
    const duration = Math.floor((+endTime - +lastBreak.startTime) / 60000);

    if (duration > record.availableBreak)
      throw new AppError("Break exceeds available break time.", 400);

    await prisma.breakSession.update({
      where: { id: lastBreak.id },
      data: { endTime },
    });

    return prisma.attendanceSummary.update({
      where: { id: record.id },
      data: {
        consumedBreak: { increment: duration },
        availableBreak: { decrement: duration },
      },
    });
  }

  // // services/Attendance/attendance.service.ts
  // async getTodayAttendance(userId: string) {
  //   const today = new Date();
  //   // Use consistent date calculation
  //   const start = new Date(
  //     today.getFullYear(),
  //     today.getMonth(),
  //     today.getDate()
  //   );
  //   const end = new Date(start);
  //   end.setHours(23, 59, 59, 999);

  //   const reponse = await prisma.attendanceSummary.findFirst({
  //     where: {
  //       userId,
  //       date: { gte: start, lte: end },
  //     },
  //     include: {
  //       clockSessions: true,
  //     },
  //   });
  //   if (!reponse) {
  //     return await prisma.attendanceSummary.create({
  //       data: {
  //         userId,
  //         date: start,
  //       },
  //       include: {
  //         clockSessions: {
  //           orderBy: { timeIn: "desc" },
  //           take: 1,
  //         },
  //       },
  //     });
  //   }
  //   return reponse;
  // }

  async getTodayAttendance(userId: string) {
    const today = new Date();
    const start = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );
    const end = new Date(start);
    end.setHours(23, 59, 59, 999);

    // Get today's attendance summary and all clock sessions
    let response = await prisma.attendanceSummary.findFirst({
      where: {
        userId,
        date: { gte: start, lte: end },
      },
      include: {
        clockSessions: {
          orderBy: { timeIn: "asc" }, // all sessions, ordered
        },
      },
    });

    if (!response) {
      // If not found, create a new one with empty clockSessions
      response = await prisma.attendanceSummary.create({
        data: {
          userId,
          date: start,
        },
        include: {
          clockSessions: {
            orderBy: { timeIn: "asc" },
          },
        },
      });
    }

    const sessions = response.clockSessions;

    // Get the latest session (last one in the ordered array)
    const latestSession = sessions.length
      ? sessions[sessions.length - 1]
      : null;

    return {
      id: response.id,
      userId: response.userId,
      date: response.date,
      status: response.status,
      arrivalStatus: response.arrivalStatus,
      breakMinutes: response.breakMinutes,
      workingMinutes: response.workingMinutes,
      totalLeave: response.totalLeave,
      availableLeave: response.availableLeave,
      consumedLeave: response.consumedLeave,
      totalBreak: response.totalBreak,
      availableBreak: response.availableBreak,
      consumedBreak: response.consumedBreak,
      createdAt: response.createdAt,
      updatedAt: response.updatedAt,
      timeIn: latestSession?.timeIn || null,
      timeOut: latestSession?.timeOut || null,
      clockSessions: sessions,
    };
  }

  async getUserHistory(
    userId: string,
    page: number,
    limit: number,
    from?: string,
    to?: string
  ) {
    let start = new Date(0);
    let end = new Date();

    if (from) {
      const fromDate = new Date(from);
      start = startOfDay(fromDate);
    }

    if (to) {
      const toDate = new Date(to);
      end = endOfDay(toDate);
    }

    const skip = (page - 1) * limit;

    const history = await prisma.attendanceSummary.findMany({
      where: {
        userId,
        date: { gte: start, lte: end },
      },
      skip,
      take: limit,
      orderBy: { date: "desc" },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            lastname: true,
            email: true,
          },
        },
      },
    });

    return history;
  }

  // async downloadAttendanceReport(from: string, to: string) {
  //   const fromDate = parse(from, "dd-MM-yyyy", new Date());
  //   const toDate = parse(to, "dd-MM-yyyy", new Date());

  //   const attendanceData = await prisma.attendanceSummary.findMany({
  //     where: {
  //       date: {
  //         gte: fromDate,
  //         lte: toDate,
  //       },
  //     },
  //     select: {
  //       userId: true,
  //       date: true,
  //       timeIn: true,
  //       timeOut: true,
  //       workingMinutes: true,
  //       status: true,
  //       consumedBreak: true,
  //     },
  //   });

  //   const parser = new Parser();
  //   const csvData = parser.parse(attendanceData);

  //   const filePath = `./reports/attendance-report-${from}-to-${to}.csv`;
  //   writeFileSync(filePath, csvData);

  //   return filePath;
  // }

  async downloadAttendanceReport(from: string, to: string) {
    const fromDate = parse(from, "dd-MM-yyyy", new Date());
    const toDate = parse(to, "dd-MM-yyyy", new Date());

    const attendanceRecords = await prisma.attendanceSummary.findMany({
      where: {
        date: {
          gte: fromDate,
          lte: toDate,
        },
      },
      select: {
        date: true,
        workingMinutes: true,
        consumedBreak: true,
        status: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        clockSessions: {
          select: {
            timeIn: true,
            timeOut: true,
          },
          orderBy: { timeIn: "asc" },
        },
      },
    });

    // Flatten for CSV output
    const flatData = attendanceRecords.flatMap((record) => {
      if (record.clockSessions.length === 0) {
        return [
          {
            userId: record.user.id,
            name: record.user.name,
            email: record.user.email,
            date: record.date.toISOString().split("T")[0],
            sessionStart: "",
            sessionEnd: "",
            sessionDuration: "",
            totalWorkingMinutes: record.workingMinutes,
            totalConsumedBreak: record.consumedBreak,
            status: record.status,
          },
        ];
      }

      return record.clockSessions.map((session) => {
        const duration =
          session.timeIn && session.timeOut
            ? Math.floor((+session.timeOut - +session.timeIn) / 60000)
            : "";

        return {
          userId: record.user.id,
          name: record.user.name,
          email: record.user.email,
          date: record.date.toISOString().split("T")[0],
          sessionStart: session.timeIn?.toISOString() ?? "",
          sessionEnd: session.timeOut?.toISOString() ?? "",
          sessionDuration: duration,
          totalWorkingMinutes: record.workingMinutes,
          totalConsumedBreak: record.consumedBreak,
          status: record.status,
        };
      });
    });

    const parser = new Parser({
      fields: [
        "userId",
        "name",
        "email",
        "date",
        "sessionStart",
        "sessionEnd",
        "sessionDuration",
        "totalWorkingMinutes",
        "totalConsumedBreak",
        "status",
      ],
    });

    const csv = parser.parse(flatData);
    const filePath = `./reports/attendance-report-${from}-to-${to}.csv`;
    writeFileSync(filePath, csv);

    return filePath;
  }

  async getClockLog(userId: string) {
    const today = new Date();
    // Use consistent date calculation
    const start = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );
    const end = new Date(start);
    end.setHours(23, 59, 59, 999);

    return prisma.attendanceSummary.findFirst({
      where: {
        userId,
        date: { gte: start, lte: end },
      },
    });
  }

  // async getAnnotatorAttendanceHistory(userId: string, query: any) {
  //   const { from, to, page = 1, limit = 10 } = query;
  //   const skip = (page - 1) * limit;

  //   let startDate = new Date(0); // Default to earliest date
  //   let endDate = new Date(); // Default to current date

  //   if (from) {
  //     startDate = startOfDay(new Date(from));
  //   }

  //   if (to) {
  //     endDate = endOfDay(new Date(to));
  //   }

  //   const attendanceRecords = await prisma.attendanceSummary.findMany({
  //     where: {
  //       userId,
  //       date: {
  //         gte: startDate,
  //         lte: endDate,
  //       },
  //     },
  //     orderBy: {
  //       date: "desc",
  //     },
  //     skip,
  //     take: parseInt(limit as string),
  //     include: {
  //       breakSessions: true,
  //     },
  //   });

  //   const total = await prisma.attendanceSummary.count({
  //     where: {
  //       userId,
  //       date: {
  //         gte: startDate,
  //         lte: endDate,
  //       },
  //     },
  //   });

  //   // Format the data for frontend display
  //   const formattedRecords = attendanceRecords.map((record) => {
  //     const breakHours = record.consumedBreak || 0;
  //     const breakHoursFormatted = `${Math.floor(breakHours / 60)}Hr ${
  //       breakHours % 60
  //     }Mins`;

  //     const workingMinutes = record.workingMinutes || 0;
  //     const workingHoursFormatted = `${Math.floor(workingMinutes / 60)}Hrs ${
  //       workingMinutes % 60
  //     }Mins`;

  //     let arrivalStatus = "Not clocked in";
  //     if (record.arrivalStatus === "ON_TIME") {
  //       arrivalStatus = "On time";
  //     } else if (record.arrivalStatus === "MINUTES_LATE") {
  //       // Calculate minutes late if timeIn exists
  //       if (record.timeIn) {
  //         const expectedTime = new Date(record.date);
  //         expectedTime.setHours(9, 0, 0, 0); // Assuming 9 AM is the expected time
  //         const minutesLate = Math.floor(
  //           (record.timeIn.getTime() - expectedTime.getTime()) / 60000
  //         );
  //         arrivalStatus = `${minutesLate} Mins Late`;
  //       } else {
  //         arrivalStatus = "Minutes Late";
  //       }
  //     } else if (record.arrivalStatus === "HOURS_LATE") {
  //       arrivalStatus = "Hours Late";
  //     }

  //     return {
  //       date: record.date,
  //       timeIn: record.timeIn ? record.timeIn : null,
  //       timeOut: record.timeOut ? record.timeOut : null,
  //       arrival: arrivalStatus,
  //       breakHours: breakHoursFormatted,
  //       workingHours: workingHoursFormatted,
  //       status: record.status,
  //       rawData: {
  //         breakMinutes: record.consumedBreak,
  //         workingMinutes: record.workingMinutes,
  //       },
  //     };
  //   });

  //   return {
  //     data: formattedRecords,
  //     pagination: {
  //       total,
  //       page: parseInt(page as string),
  //       limit: parseInt(limit as string),
  //       totalPages: Math.ceil(total / parseInt(limit as string)),
  //     },
  //   };
  // }

  async getAnnotatorAttendanceHistory(userId: string, query: any) {
    const { from, to, page = 1, limit = 10 } = query;
    const skip = (page - 1) * limit;

    let startDate = new Date(0); // default to earliest date
    let endDate = new Date(); // default to now

    if (from) {
      startDate = startOfDay(new Date(from));
    }
    if (to) {
      endDate = endOfDay(new Date(to));
    }

    const attendanceRecords = await prisma.attendanceSummary.findMany({
      where: {
        userId,
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: {
        date: "desc",
      },
      skip,
      take: parseInt(limit as string),
      include: {
        breakSessions: true,
        clockSessions: {
          orderBy: { timeIn: "asc" }, // ensure clockIn[0] is first-in
        },
      },
    });

    const total = await prisma.attendanceSummary.count({
      where: {
        userId,
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
    });

    const formattedRecords = attendanceRecords.map((record) => {
      const clockIns = record.clockSessions;
      const firstClockIn = clockIns.length ? clockIns[0]?.timeIn : null;
      const lastClockOut =
        clockIns.filter((s) => s.timeOut !== null).slice(-1)[0]?.timeOut ||
        null;

      // Arrival Status
      let arrivalStatus = "Not clocked in";
      if (record.arrivalStatus === "ON_TIME") {
        arrivalStatus = "On time";
      } else if (record.arrivalStatus === "MINUTES_LATE") {
        if (firstClockIn) {
          const expectedTime = new Date(record.date);
          expectedTime.setHours(9, 0, 0, 0);
          const minutesLate = Math.floor(
            (firstClockIn.getTime() - expectedTime.getTime()) / 60000
          );
          arrivalStatus = `${minutesLate} Mins Late`;
        } else {
          arrivalStatus = "Minutes Late";
        }
      } else if (record.arrivalStatus === "HOURS_LATE") {
        arrivalStatus = "Hours Late";
      }

      const breakMinutes = record.consumedBreak || 0;
      const breakHoursFormatted = `${Math.floor(breakMinutes / 60)}Hr ${
        breakMinutes % 60
      }Mins`;

      const workingMinutes = record.workingMinutes || 0;
      const workingHoursFormatted = `${Math.floor(workingMinutes / 60)}Hrs ${
        workingMinutes % 60
      }Mins`;

      return {
        date: record.date,
        timeIn: firstClockIn,
        timeOut: lastClockOut,
        arrival: arrivalStatus,
        breakHours: breakHoursFormatted,
        workingHours: workingHoursFormatted,
        status: record.status,
        rawData: {
          breakMinutes,
          workingMinutes,
        },
      };
    });

    return {
      data: formattedRecords,
      pagination: {
        total,
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        totalPages: Math.ceil(total / parseInt(limit as string)),
      },
    };
  }
}

export default new AttendanceService();
