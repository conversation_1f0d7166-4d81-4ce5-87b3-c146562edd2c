import { Request, Response } from "express";
import { leaveService } from "../../services/leave/leave.service";
import { successResponseWithData, errorResponse } from "../../helper/apiResponse";
import { JwtPayload } from "jsonwebtoken";

interface AuthenticatedRequest extends Request {
  user?: JwtPayload & { userId: string; userRole: string };
}

export const requestLeave = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { startDate, endDate, reason } = req.body;
    const userId = req.user?.userId;
    const userRole = req.user?.userRole;

    if (!userId) {
      return errorResponse(res, "Unauthorized");
    }

    // Only annotators can request leave
    if (userRole !== "ANNOTATOR") {
      return errorResponse(res, "Only annotators can request leave");
    }

    // Validate required fields
    if (!startDate || !endDate || !reason) {
      return errorResponse(res, "All fields are required: startDate, endDate, reason");
    }

    const result = await leaveService.requestLeave({
      annotatorId: userId,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      reason,
    });

    return successResponseWithData(res, "Leave request submitted successfully", result);
  } catch (err: any) {
    return errorResponse(res, err.message || "Failed to submit leave request");
  }
};

export const approveLeave = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { requestId } = req.params;
    const approverId = req.user?.userId;
    const userRole = req.user?.userRole;

    if (!approverId) {
      return errorResponse(res, "Unauthorized");
    }

    // Check if user has permission to approve
    if (!["ADMIN", "PROJECT_COORDINATOR", "CLIENT"].includes(userRole || "")) {
      return errorResponse(res, "You don't have permission to approve leave requests");
    }

    const result = await leaveService.approveLeave(requestId, approverId);
    return successResponseWithData(res, "Leave request approved successfully", result);
  } catch (err: any) {
    return errorResponse(res, err.message || "Failed to approve leave request");
  }
};

export const rejectLeave = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { requestId } = req.params;
    const { rejectionReason } = req.body;
    const approverId = req.user?.userId;
    const userRole = req.user?.userRole;

    if (!approverId) {
      return errorResponse(res, "Unauthorized");
    }

    // Check if user has permission to reject
    if (!["ADMIN", "PROJECT_COORDINATOR", "CLIENT"].includes(userRole || "")) {
      return errorResponse(res, "You don't have permission to reject leave requests");
    }

    if (!rejectionReason) {
      return errorResponse(res, "Rejection reason is required");
    }

    const result = await leaveService.rejectLeave(requestId, approverId, rejectionReason);
    return successResponseWithData(res, "Leave request rejected successfully", result);
  } catch (err: any) {
    return errorResponse(res, err.message || "Failed to reject leave request");
  }
};

export const getPendingLeaveRequests = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userRole = req.user?.userRole;

    // Check if user has permission to view pending requests
    if (!["ADMIN", "PROJECT_COORDINATOR", "CLIENT"].includes(userRole || "")) {
      return errorResponse(res, "You don't have permission to view pending leave requests");
    }

    const result = await leaveService.getPendingLeaveRequests(req.query);
    return successResponseWithData(res, "Pending leave requests fetched successfully", result);
  } catch (err: any) {
    return errorResponse(res, err.message || "Failed to fetch pending leave requests");
  }
};

export const getAnnotatorLeaveHistory = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { annotatorId } = req.params;
    const userId = req.user?.userId;
    const userRole = req.user?.userRole;

    if (!userId) {
      return errorResponse(res, "Unauthorized");
    }

    // If annotator is viewing their own history or if admin/coordinator/client is viewing
    if (
      annotatorId === userId ||
      ["ADMIN", "PROJECT_COORDINATOR", "CLIENT"].includes(userRole || "")
    ) {
      const result = await leaveService.getAnnotatorLeaveHistory(annotatorId, req.query);
      return successResponseWithData(res, "Leave history fetched successfully", result);
    } else {
      return errorResponse(res, "You don't have permission to view this leave history");
    }
  } catch (err: any) {
    return errorResponse(res, err.message || "Failed to fetch leave history");
  }
};

export const getAllLeaveRequests = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userRole = req.user?.userRole;

    // Permission check (same roles as /pending)
    if (!["ADMIN", "PROJECT_COORDINATOR", "CLIENT"].includes(userRole || "")) {
      return errorResponse(res, "You don't have permission to view all leave requests");
    }

    const result = await leaveService.getAllLeaveRequests(req.query);
    return successResponseWithData(res, "All leave requests fetched successfully", result);
  } catch (err: any) {
    return errorResponse(res, err.message || "Failed to fetch all leave requests");
  }
};

export const getRelevantLeaveRequests = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userId, userRole } = req.user || {};

    if (!userId || !userRole) {
      return errorResponse(res, "Unauthorized");
    }

    let leaveRequests;

    if (userRole === "ANNOTATOR") {
      leaveRequests = await leaveService.getLeaveRequestsByAnnotator(userId);
    } else if (["CLIENT", "PROJECT_COORDINATOR", "COWORKER"].includes(userRole)) {
      leaveRequests = await leaveService.getLeaveRequestsByRole(userId, userRole);
    } else {
      return errorResponse(res, "You don't have permission to view these leave requests");
    }

    return successResponseWithData(res, "Leave requests fetched successfully", leaveRequests);

  } catch (err: any) {
    console.error(err, "errrrrrrrrrrrrrr");
    return errorResponse(res, err.message || "Failed to fetch relevant leave requests");
  }
};
