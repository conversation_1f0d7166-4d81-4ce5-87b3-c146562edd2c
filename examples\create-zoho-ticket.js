/**
 * Example script to create a Zoho Desk ticket using the API
 * 
 * Usage: node create-zoho-ticket.js
 */

const axios = require('axios');

async function createZohoTicket() {
  try {
    // Replace with your API base URL and authentication token
    const baseUrl = 'http://localhost:3000/api';
    const authToken = 'YOUR_AUTH_TOKEN'; // Replace with your actual auth token
    
    const ticketData = {
      // Required fields
      subject: "Real Time analysis Requirement",
      description: "Hai This is Description",
      priority: "High",
      status: "Open",
      
      // Optional fields
      entitySkills: [ "18921000000379001", "18921000000364001", "18921000000379055", "18921000000379031" ],
      subCategory: "Sub General",
      cf: {
        cf_permanentaddress: null,
        cf_dateofpurchase: null,
        cf_phone: null,
        cf_numberofitems: null,
        cf_url: null,
        cf_secondaryemail: null,
        cf_severitypercentage: "0.0",
        cf_modelname: "F3 2017"
      },
      productId: "",
      contactId: "1892000000042032",
      dueDate: "2016-06-21T16:16:16.000Z",
      departmentId: "1892000000006907",
      channel: "Email",
      language: "English",
      classification: "",
      assigneeId: "1892000000056007",
      phone: "**************",
      category: "general",
      email: "<EMAIL>"
    };

    const response = await axios.post(`${baseUrl}/zoho-desk/tickets`, ticketData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      }
    });

    console.log('Ticket created successfully:');
    console.log(JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('Error creating ticket:');
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error:', error.message);
    }
  }
}

createZohoTicket();