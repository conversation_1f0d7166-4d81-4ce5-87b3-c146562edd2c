import { Router } from "express";
import { AnnotatorController } from "../../controllers/annotator/annotator.controller";
import { asyncHandler } from "../../middlewares/asyncHandler";
import authMiddleware from "../../middlewares/checkAuth"; // ✅ fixed import
import { hasRole } from "../../middlewares/checkRole";

const router = Router();
const controller = new AnnotatorController();

router.post(
  "/create-annotator",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(controller.createAnnotator.bind(controller))
);

router.get(
  "/get-all-annotators",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(controller.getAnnotator.bind(controller))
);

router
  .route("/get-all-coordinator")
  .get(
    authMiddleware,
    hasRole("ADMIN"),
    asyncHandler(controller.getCoordinator.bind(controller))
  );

router.get(
  "/get-annotator/:id",
  authMiddleware,
  hasRole("ADMIN"),
  async<PERSON><PERSON><PERSON>(controller.getAnnotatorById.bind(controller))
);

router.patch(
  "/update-annotator-status/:id",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(controller.updateAnnotator.bind(controller))
);

router.delete(
  "/delete-annotator/:id",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(controller.deleteAnnotator.bind(controller))
);

router.get(
  "/get-all-clients",
  authMiddleware,
  hasRole("PROJECT_COORDINATOR"),
  asyncHandler(controller.getClients.bind(controller))
);

router.get(
  "/annotator-projects/:annotatorId",
  authMiddleware,
  hasRole("PROJECT_COORDINATOR"), 
  asyncHandler(controller.getAnnotatorProjects.bind(controller))
);


router.get('/coordinator-projects', authMiddleware,
  asyncHandler(controller.getCoordinatorProjects.bind(controller)));

  router.get('/clients-with-subscriptions',  asyncHandler(controller.getClientsWithSubscriptions.bind(controller)));
// router.get(
//   "/get-all-clients",
//   authMiddleware,
//   hasRole("ADMIN"),
//   asyncHandler(controller.getCoordinator.bind(controller))
// );

// Clients Annotators
router.get(
  "/get-all-client-annotators",
  authMiddleware,
  // hasRole("CLIENT"),
  asyncHandler(controller.getClientAnnotators.bind(controller))
);

// ADMIN DASHBOARD

// get all assigned Annotators
router.get(
  "/get-all-admin-annotators",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(controller.getAllAnnotator.bind(controller))
);

router.get(
  "/get-all-admin-coordinators",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(controller.getAllCoordinator.bind(controller))
);

router.get(
  "/get-all-coordinator-clients",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(controller.getCoordinatorClients.bind(controller))
);

router.get(
  "/coordinator-client-projects/:clientId",
  authMiddleware,
  hasRole("PROJECT_COORDINATOR"),
  asyncHandler(controller.getCoordinatorClientProjects.bind(controller))
);


router.post(
  "/update-description",
  authMiddleware, 
  asyncHandler(controller.updateDescription.bind(controller))
);


export default router;
