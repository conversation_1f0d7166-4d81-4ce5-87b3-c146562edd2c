// import prisma from "../../prisma";
// import { SubscriptionStatus, BankTransferStatus } from "@prisma/client";
// import { EmailService } from "../email/email.service";
// import cron from "node-cron";

// export class BankTransferRenewalService {
//   private emailService: EmailService;

//   constructor() {
//     this.emailService = new EmailService();
//     this.initializeCronJobs();
//   }

//   /**
//    * Initialize cron jobs for automated reminders and expiration handling
//    */
//   private initializeCronJobs() {
//     // Run daily at 9:00 AM to check for reminders and expirations
//     cron.schedule('0 9 * * *', async () => {
//       console.log('🔄 Running daily bank transfer subscription check...');
//       await this.processDailySubscriptionCheck();
//     });

//     console.log('✅ Bank transfer renewal cron jobs initialized');
//   }

//   /**
//    * Main daily process to handle reminders and expirations
//    */
//   async processDailySubscriptionCheck() {
//     try {
//       await this.sendExpirationReminders();
//       await this.processExpiredSubscriptions();
//       await this.processRenewalPayments();
//     } catch (error) {
//       console.error('Error in daily subscription check:', error);
//     }
//   }

//   /**
//    * Send expiration reminder emails (3, 2, 1 days before expiration)
//    */
//   async sendExpirationReminders() {
//     try {
//       const today = new Date();
      
//       // Check for subscriptions expiring in 3, 2, and 1 days
//       for (const daysUntilExpiry of [3, 2, 1]) {
//         const targetDate = new Date(today);
//         targetDate.setDate(today.getDate() + daysUntilExpiry);
//         targetDate.setHours(23, 59, 59, 999); // End of day

//         const expiringSubscriptions = await this.getBankTransferSubscriptionsExpiringOn(targetDate);

//         for (const subscription of expiringSubscriptions) {
//           await this.sendExpirationReminderEmail(subscription, daysUntilExpiry);
          
//           // Log the reminder
//           await this.logReminderSent(subscription.id, daysUntilExpiry);
//         }

//         console.log(`📧 Sent ${expiringSubscriptions.length} reminders for subscriptions expiring in ${daysUntilExpiry} days`);
//       }
//     } catch (error) {
//       console.error('Error sending expiration reminders:', error);
//     }
//   }

//   /**
//    * Get bank transfer subscriptions expiring on a specific date
//    */
//   private async getBankTransferSubscriptionsExpiringOn(targetDate: Date) {
//     const startOfDay = new Date(targetDate);
//     startOfDay.setHours(0, 0, 0, 0);
    
//     const endOfDay = new Date(targetDate);
//     endOfDay.setHours(23, 59, 59, 999);

//     return await prisma.subscription.findMany({
//       where: {
//         status: SubscriptionStatus.ACTIVE,
//         endDate: {
//           gte: startOfDay,
//           lte: endOfDay,
//         },
//         // Only include subscriptions that have bank transfer payments
//         Payment: {
//           some: {
//             provider: "BANK_TRANSFER",
//             status: "SUCCESS",
//           },
//         },
//       },
//       include: {
//         user: {
//           select: {
//             id: true,
//             name: true,
//             lastname: true,
//             email: true,
//           },
//         },
//         package: {
//           select: {
//             id: true,
//             name: true,
//             price: true,
//             billingType: true,
//           },
//         },
//         Payment: {
//           where: {
//             provider: "BANK_TRANSFER",
//             status: "SUCCESS",
//           },
//           orderBy: {
//             createdAt: "desc",
//           },
//           take: 1,
//         },
//       },
//     });
//   }

//   /**
//    * Process expired subscriptions
//    */
//   async processExpiredSubscriptions() {
//     try {
//       const today = new Date();
//       today.setHours(0, 0, 0, 0);

//       const expiredSubscriptions = await prisma.subscription.findMany({
//         where: {
//           status: SubscriptionStatus.ACTIVE,
//           endDate: {
//             lt: today,
//           },
//           // Only bank transfer subscriptions
//           Payment: {
//             some: {
//               provider: "BANK_TRANSFER",
//               status: "SUCCESS",
//             },
//           },
//         },
//         include: {
//           user: {
//             select: {
//               id: true,
//               name: true,
//               lastname: true,
//               email: true,
//             },
//           },
//           package: {
//             select: {
//               id: true,
//               name: true,
//               price: true,
//             },
//           },
//         },
//       });

//       for (const subscription of expiredSubscriptions) {
//         // Check if there's a pending renewal payment
//         const pendingRenewal = await this.checkForPendingRenewalPayment(subscription.id);
        
//         if (!pendingRenewal) {
//           // No pending renewal, expire the subscription
//           await this.expireSubscription(subscription);
//         }
//       }

//       console.log(`⏰ Processed ${expiredSubscriptions.length} expired subscriptions`);
//     } catch (error) {
//       console.error('Error processing expired subscriptions:', error);
//     }
//   }

//   /**
//    * Process renewal payments and extend subscriptions
//    */
//   async processRenewalPayments() {
//     try {
//       // Find verified bank transfer payments that are for renewals
//       const verifiedRenewalPayments = await prisma.bankTransferPayment.findMany({
//         where: {
//           status: BankTransferStatus.VERIFIED,
//           // Look for payments made in the last 7 days for renewal
//           verifiedAt: {
//             gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
//           },
//           subscription: {
//             status: {
//               in: [SubscriptionStatus.ACTIVE, SubscriptionStatus.EXPIRED],
//             },
//           },
//         },
//         include: {
//           user: true,
//           package: true,
//           subscription: true,
//           payment: true,
//         },
//       });

//       for (const renewalPayment of verifiedRenewalPayments) {
//         if (renewalPayment.subscription) {
//           await this.processSubscriptionRenewal(renewalPayment);
//         }
//       }

//       console.log(`🔄 Processed ${verifiedRenewalPayments.length} renewal payments`);
//     } catch (error) {
//       console.error('Error processing renewal payments:', error);
//     }
//   }

//   /**
//    * Check if there's a pending renewal payment for a subscription
//    */
//   private async checkForPendingRenewalPayment(subscriptionId: string) {
//     return await prisma.bankTransferPayment.findFirst({
//       where: {
//         subscriptionId,
//         status: {
//           in: [BankTransferStatus.PENDING, BankTransferStatus.VERIFIED],
//         },
//         createdAt: {
//           gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
//         },
//       },
//     });
//   }

//   /**
//    * Expire a subscription
//    */
//   private async expireSubscription(subscription: any) {
//     try {
//       await prisma.subscription.update({
//         where: { id: subscription.id },
//         data: {
//           status: SubscriptionStatus.EXPIRED,
//           updatedAt: new Date(),
//         },
//       });

//       // Send expiration notification
//       await this.sendSubscriptionExpiredEmail(subscription);

//       console.log(`❌ Expired subscription ${subscription.id} for user ${subscription.user.email}`);
//     } catch (error) {
//       console.error(`Error expiring subscription ${subscription.id}:`, error);
//     }
//   }

//   /**
//    * Process subscription renewal
//    */
//   private async processSubscriptionRenewal(renewalPayment: any) {
//     try {
//       const subscription = renewalPayment.subscription;
//       const currentEndDate = new Date(subscription.endDate);
//       const newEndDate = new Date(currentEndDate);
      
//       // Extend by 1 month
//       newEndDate.setMonth(newEndDate.getMonth() + 1);

//       await prisma.subscription.update({
//         where: { id: subscription.id },
//         data: {
//           status: SubscriptionStatus.ACTIVE,
//           endDate: newEndDate,
//           updatedAt: new Date(),
//         },
//       });

//       // Send renewal confirmation email
//       await this.sendSubscriptionRenewedEmail(renewalPayment, newEndDate);

//       console.log(`✅ Renewed subscription ${subscription.id} for user ${renewalPayment.user.email} until ${newEndDate.toDateString()}`);
//     } catch (error) {
//       console.error(`Error renewing subscription ${renewalPayment.subscription.id}:`, error);
//     }
//   }

//   /**
//    * Send expiration reminder email
//    */
//   private async sendExpirationReminderEmail(subscription: any, daysUntilExpiry: number) {
//     try {
//       const user = subscription.user;
//       const packageInfo = subscription.package;

//       await this.emailService.sendEmail({
//         to: user.email,
//         subject: `⚠️ Your ${packageInfo.name} subscription expires in ${daysUntilExpiry} day${daysUntilExpiry > 1 ? 's' : ''}`,
//         template: "bank-transfer-expiration-reminder",
//         data: {
//           userName: user.name,
//           packageName: packageInfo.name,
//           expirationDate: subscription.endDate.toLocaleDateString(),
//           daysUntilExpiry,
//           packagePrice: packageInfo.price,
//           renewalInstructions: "To renew your subscription, please make a bank transfer payment and submit the payment details through our portal.",
//           bankTransferLink: `${process.env.FRONTEND_URL}/bank-transfer`,
//           dashboardLink: `${process.env.FRONTEND_URL}/dashboard`,
//           supportEmail: "<EMAIL>",
//         },
//       });
//     } catch (error) {
//       console.error(`Error sending reminder email to ${subscription.user.email}:`, error);
//     }
//   }

//   /**
//    * Send subscription expired email
//    */
//   private async sendSubscriptionExpiredEmail(subscription: any) {
//     try {
//       await this.emailService.sendEmail({
//         to: subscription.user.email,
//         subject: `❌ Your ${subscription.package.name} subscription has expired`,
//         template: "bank-transfer-subscription-expired",
//         data: {
//           userName: subscription.user.name,
//           packageName: subscription.package.name,
//           expiredDate: subscription.endDate.toLocaleDateString(),
//           renewalLink: `${process.env.FRONTEND_URL}/bank-transfer`,
//           supportEmail: "<EMAIL>",
//         },
//       });
//     } catch (error) {
//       console.error(`Error sending expiration email to ${subscription.user.email}:`, error);
//     }
//   }

//   /**
//    * Send subscription renewed email
//    */
//   private async sendSubscriptionRenewedEmail(renewalPayment: any, newEndDate: Date) {
//     try {
//       await this.emailService.sendEmail({
//         to: renewalPayment.user.email,
//         subject: `✅ Your ${renewalPayment.package.name} subscription has been renewed`,
//         template: "bank-transfer-subscription-renewed",
//         data: {
//           userName: renewalPayment.user.name,
//           packageName: renewalPayment.package.name,
//           renewedDate: new Date().toLocaleDateString(),
//           newExpirationDate: newEndDate.toLocaleDateString(),
//           transactionId: renewalPayment.transactionId,
//           amount: renewalPayment.amount,
//           currency: renewalPayment.currency,
//           dashboardLink: `${process.env.FRONTEND_URL}/dashboard`,
//         },
//       });
//     } catch (error) {
//       console.error(`Error sending renewal email to ${renewalPayment.user.email}:`, error);
//     }
//   }

//   /**
//    * Log reminder sent to avoid duplicate reminders
//    */
//   private async logReminderSent(subscriptionId: string, daysUntilExpiry: number) {
//     try {
//       // You can create a separate table for this or use existing logging
//       console.log(`📝 Logged reminder for subscription ${subscriptionId}, ${daysUntilExpiry} days until expiry`);
//     } catch (error) {
//       console.error('Error logging reminder:', error);
//     }
//   }

//   /**
//    * Manual trigger for testing (admin only)
//    */
//   async triggerManualCheck() {
//     console.log('🔧 Manual subscription check triggered');
//     await this.processDailySubscriptionCheck();
//     return {
//       success: true,
//       message: "Manual subscription check completed",
//       timestamp: new Date().toISOString(),
//     };
//   }

//   /**
//    * Get subscription renewal statistics
//    */
//   async getRenewalStatistics() {
//     try {
//       const today = new Date();
//       const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

//       const [
//         expiringIn3Days,
//         expiringIn2Days,
//         expiringIn1Day,
//         expiredToday,
//         renewedThisMonth,
//       ] = await Promise.all([
//         this.getBankTransferSubscriptionsExpiringOn(new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000)),
//         this.getBankTransferSubscriptionsExpiringOn(new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000)),
//         this.getBankTransferSubscriptionsExpiringOn(new Date(today.getTime() + 1 * 24 * 60 * 60 * 1000)),
//         this.getBankTransferSubscriptionsExpiringOn(today),
//         prisma.bankTransferPayment.count({
//           where: {
//             status: BankTransferStatus.VERIFIED,
//             verifiedAt: {
//               gte: thirtyDaysAgo,
//             },
//           },
//         }),
//       ]);

//       return {
//         expiringIn3Days: expiringIn3Days.length,
//         expiringIn2Days: expiringIn2Days.length,
//         expiringIn1Day: expiringIn1Day.length,
//         expiredToday: expiredToday.length,
//         renewedThisMonth,
//         lastChecked: new Date().toISOString(),
//       };
//     } catch (error) {
//       console.error('Error getting renewal statistics:', error);
//       throw new Error('Failed to get renewal statistics');
//     }
//   }
// }