/*
  Warnings:

  - The values [VIEWER,EDITOR] on the enum `Role` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `annotatorId` on the `Project` table. All the data in the column will be lost.
  - You are about to drop the column `attachment` on the `Project` table. All the data in the column will be lost.
  - You are about to drop the column `annotatorId` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `attachment` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `defaultWorkspace` on the `User` table. All the data in the column will be lost.
  - You are about to drop the column `image` on the `User` table. All the data in the column will be lost.
  - You are about to drop the column `isMachine` on the `User` table. All the data in the column will be lost.
  - You are about to drop the column `lockedAt` on the `User` table. All the data in the column will be lost.
  - You are about to drop the column `source` on the `User` table. All the data in the column will be lost.
  - You are about to drop the column `subscribed` on the `User` table. All the data in the column will be lost.
  - You are about to drop the `Admin` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Annotator` table. If the table is not empty, all the data it contains will be lost.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "Role_new" AS ENUM ('ADMIN', 'CLIENT', 'COWORKER', 'PROJECT_COORDINATOR', 'ANNOTATOR');
ALTER TABLE "User" ALTER COLUMN "role" DROP DEFAULT;
ALTER TABLE "User" ALTER COLUMN "role" TYPE "Role_new" USING ("role"::text::"Role_new");
ALTER TYPE "Role" RENAME TO "Role_old";
ALTER TYPE "Role_new" RENAME TO "Role";
DROP TYPE "Role_old";
ALTER TABLE "User" ALTER COLUMN "role" SET DEFAULT 'CLIENT';
COMMIT;

-- DropForeignKey
ALTER TABLE "Annotator" DROP CONSTRAINT "Annotator_projectId_fkey";

-- DropForeignKey
ALTER TABLE "Task" DROP CONSTRAINT "Task_annotatorId_fkey";

-- DropIndex
DROP INDEX "User_defaultWorkspace_idx";

-- DropIndex
DROP INDEX "User_source_idx";

-- AlterTable
ALTER TABLE "Project" DROP COLUMN "annotatorId",
DROP COLUMN "attachment";

-- AlterTable
ALTER TABLE "Task" DROP COLUMN "annotatorId",
DROP COLUMN "attachment",
ADD COLUMN     "assignedToId" TEXT;

-- AlterTable
ALTER TABLE "User" DROP COLUMN "defaultWorkspace",
DROP COLUMN "image",
DROP COLUMN "isMachine",
DROP COLUMN "lockedAt",
DROP COLUMN "source",
DROP COLUMN "subscribed",
ADD COLUMN     "annotatorStatus" "AnnotatorStatus",
ADD COLUMN     "clientOwnerId" TEXT,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "zohoCustomerId" TEXT,
ALTER COLUMN "role" SET DEFAULT 'CLIENT';

-- DropTable
DROP TABLE "Admin";

-- DropTable
DROP TABLE "Annotator";

-- CreateTable
CREATE TABLE "ProjectCoordinatorAssignment" (
    "id" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,
    "projectCoordinatorId" TEXT NOT NULL,
    "assignedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProjectCoordinatorAssignment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TimeLog" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "loginAt" TIMESTAMP(3) NOT NULL,
    "logoutAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "TimeLog_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_clientOwnerId_fkey" FOREIGN KEY ("clientOwnerId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Project" ADD CONSTRAINT "Project_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProjectCoordinatorAssignment" ADD CONSTRAINT "ProjectCoordinatorAssignment_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProjectCoordinatorAssignment" ADD CONSTRAINT "ProjectCoordinatorAssignment_projectCoordinatorId_fkey" FOREIGN KEY ("projectCoordinatorId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Task" ADD CONSTRAINT "Task_assignedToId_fkey" FOREIGN KEY ("assignedToId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TimeLog" ADD CONSTRAINT "TimeLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
