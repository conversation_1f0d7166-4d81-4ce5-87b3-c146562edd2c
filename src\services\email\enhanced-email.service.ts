import nodemailer from 'nodemailer';
import { promises as fs } from 'fs';
import path from 'path';
import { EMAIL_TEMPLATES, TemplateRenderer } from '../../templates';
import { EmailAnalyticsService } from './email-analytics.service';

export interface EmailData {
  to: string | string[];
  subject: string;
  template: string;
  data: any;
  cc?: string | string[];
  bcc?: string | string[];
  attachments?: any[];
}

export class EnhancedEmailService {
  private transporter!: nodemailer.Transporter;
  private templatesPath: string;
  private analyticsService: EmailAnalyticsService;
  private enableAnalytics: boolean;

  constructor(enableAnalytics: boolean = true) {
    this.templatesPath = path.join(__dirname, '../../templates/email');
    this.analyticsService = new EmailAnalyticsService();
    this.enableAnalytics = enableAnalytics;
    this.initializeTransporter();
  }

  private initializeTransporter(): void {
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
  }

  /**
   * Send email using template (supports both HTML files and TypeScript templates)
   */
  async sendEmail(emailData: EmailData): Promise<string | null> {
    let emailId: string | null = null;
    
    try {
      // Log email attempt if analytics is enabled
      if (this.enableAnalytics) {
        emailId = await this.analyticsService.logEmailSent({
          to: Array.isArray(emailData.to) ? emailData.to.join(', ') : emailData.to,
          subject: emailData.subject,
          template: emailData.template,
          metadata: emailData.data,
        });
      }

      let htmlContent = await this.renderTemplate(emailData.template, emailData.data);
      
      // Add tracking pixel if analytics is enabled
      if (this.enableAnalytics && emailId) {
        const trackingPixel = `<img src="${this.analyticsService.generateTrackingPixelUrl(emailId)}" width="1" height="1" style="display:none;" />`;
        htmlContent = htmlContent.replace('</body>', `${trackingPixel}</body>`);
      }
      
      const mailOptions = {
        from: process.env.SMTP_FROM || process.env.SMTP_USER,
        to: Array.isArray(emailData.to) ? emailData.to.join(', ') : emailData.to,
        cc: emailData.cc ? (Array.isArray(emailData.cc) ? emailData.cc.join(', ') : emailData.cc) : undefined,
        bcc: emailData.bcc ? (Array.isArray(emailData.bcc) ? emailData.bcc.join(', ') : emailData.bcc) : undefined,
        subject: emailData.subject,
        html: htmlContent,
        attachments: emailData.attachments
      };

      await this.transporter.sendMail(mailOptions);
      
      // Mark as delivered if analytics is enabled
      if (this.enableAnalytics && emailId) {
        await this.analyticsService.markEmailDelivered(emailId);
      }
      
      console.log(`📧 Email sent to ${emailData.to}: ${emailData.subject}`);
      return emailId;
    } catch (error) {
      // Mark as failed if analytics is enabled
      if (this.enableAnalytics && emailId) {
        await this.analyticsService.markEmailFailed(
          emailId,
          error instanceof Error ? error.message : String(error)
        );
      }
      
      console.error('Error sending email:', error);
      throw error;
    }
  }

  /**
   * Render email template (tries TypeScript templates first, then HTML files)
   */
  private async renderTemplate(templateName: string, data: any): Promise<string> {
    try {
      // First, try to use TypeScript templates
      if (templateName in EMAIL_TEMPLATES) {
        return TemplateRenderer.render(templateName as keyof typeof EMAIL_TEMPLATES, data);
      }

      // Fallback to HTML file templates
      const templatePath = path.join(this.templatesPath, `${templateName}.html`);
      let template = await fs.readFile(templatePath, 'utf-8');

      // Handle conditional blocks {{#variable}}...{{/variable}}
      template = template.replace(/\{\{#(\w+)\}\}([\s\S]*?)\{\{\/\1\}\}/g, (match, key, content) => {
        return data[key] ? content : '';
      });

      // Handle nested objects
      template = template.replace(/\{\{(\w+)\.(\w+)\}\}/g, (match, obj, key) => {
        return data[obj]?.[key] || '';
      });

      // Simple template rendering (replace {{variable}} with data)
      template = template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
        return data[key] || '';
      });

      return template;
    } catch (error) {
      console.error(`Error rendering template ${templateName}:`, error);
      // Return a basic template if file doesn't exist
      return this.getBasicTemplate(data);
    }
  }

  /**
   * Get basic email template
   */
  private getBasicTemplate(data: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${data.title || 'Notification'}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #007bff; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; }
          .button { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${data.title || 'Notification'}</h1>
          </div>
          <div class="content">
            <p>Hello ${data.userName || data.firstName || 'User'},</p>
            <p>${data.alert?.message || data.message || 'You have a new notification.'}</p>
            ${data.dashboardUrl ? `<p><a href="${data.dashboardUrl}" class="button">View Dashboard</a></p>` : ''}
          </div>
          <div class="footer">
            <p>Best regards,<br>${data.companyName || 'Your Team'}</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Send bulk emails
   */
  async sendBulkEmails(emails: EmailData[]): Promise<void> {
    const promises = emails.map(email => this.sendEmail(email));
    await Promise.allSettled(promises);
    console.log(`📧 Sent ${emails.length} bulk emails`);
  }

  /**
   * Test email configuration
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      console.log('✅ Email service connection verified');
      return true;
    } catch (error) {
      console.error('❌ Email service connection failed:', error);
      return false;
    }
  }

  /**
   * Send email using legacy mailSender format (for backward compatibility)
   */
  async sendLegacyEmail(user: { name: string; email: string; subject: string; body: string }): Promise<boolean> {
    try {
      const mailOptions = {
        from: process.env.EMAIL_FROM || process.env.SMTP_USER,
        to: user.email,
        subject: user.subject,
        html: user.body,
      };

      await this.transporter.sendMail(mailOptions);
      console.log(`📧 Legacy email sent to ${user.email}: ${user.subject}`);
      return true;
    } catch (error) {
      console.error("Error sending legacy email:", error);
      return false;
    }
  }

  /**
   * Get email analytics
   */
  async getAnalytics(startDate: Date, endDate: Date, template?: string) {
    if (!this.enableAnalytics) {
      throw new Error('Analytics is not enabled for this email service instance');
    }
    return await this.analyticsService.getEmailAnalytics(startDate, endDate, template);
  }

  /**
   * Get template performance analytics
   */
  async getTemplateAnalytics(startDate: Date, endDate: Date) {
    if (!this.enableAnalytics) {
      throw new Error('Analytics is not enabled for this email service instance');
    }
    return await this.analyticsService.getTemplateAnalytics(startDate, endDate);
  }

  /**
   * Get recent email activity
   */
  async getRecentActivity(limit: number = 50) {
    if (!this.enableAnalytics) {
      throw new Error('Analytics is not enabled for this email service instance');
    }
    return await this.analyticsService.getRecentEmailActivity(limit);
  }

  /**
   * Track email open (to be called from tracking endpoint)
   */
  async trackOpen(emailId: string) {
    if (!this.enableAnalytics) {
      return;
    }
    await this.analyticsService.trackEmailOpen(emailId);
  }

  /**
   * Track email click (to be called from tracking endpoint)
   */
  async trackClick(emailId: string) {
    if (!this.enableAnalytics) {
      return;
    }
    await this.analyticsService.trackEmailClick(emailId);
  }
}

export default EnhancedEmailService;