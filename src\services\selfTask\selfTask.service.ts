import prisma from "../../prisma";

export class SelfTaskService {
  async create(data: {
    name: string;
    description: string;
    color: string;
    priority: "LOW" | "MEDIUM" | "HIGH";
    createdById: string;
    startDate: Date;
    dueDate: Date;
  }) {
    return prisma.selfTask.create({
      data: {
        name: data.name,
        description: data.description,
        color: data.color,
        priority: data.priority,
        createdById: data.createdById,
        startDate: data.startDate,
        dueDate: data.dueDate,
        status: "PENDING",
      },
    });
  }

  async getAll(filter?: { createdById: string }) {
    return prisma.selfTask.findMany({
      where: { createdById: filter?.createdById },
    });
  }

  async getPending(filter: { createdById: string }) {
    console.log(filter, "filter");
    return prisma.selfTask.findMany({
      where: { status: "PENDING", createdById: filter.createdById },
    });
  }

  async getInProgress(filter: { createdById: string }) {
    return prisma.selfTask.findMany({
      where: { status: "IN_PROGRESS", createdById: filter.createdById },
      include: {
        createdBy: true,
      },
    });
  }

  async getCompleted(filter?: { createdById: string }) {
    return prisma.selfTask.findMany({
      where: { status: "COMPLETED", createdById: filter?.createdById },
      include: {
        createdBy: true,
      },
    });
  }

  async getById(id: string) {
    return prisma.selfTask.findUnique({ where: { id } });
  }

  async update(
    id: string,
    data: Partial<{
      name: string;
      description: string;
      color: string;
      priority: "LOW" | "MEDIUM" | "HIGH";
      status: "PENDING" | "IN_PROGRESS" | "COMPLETED";
      startDate: Date;
      dueDate: Date;
    }>
  ) {
    return prisma.selfTask.update({
      where: { id },
      data,
    });
  }

  async delete(id: string) {
    return prisma.selfTask.delete({ where: { id } });
  }
}
