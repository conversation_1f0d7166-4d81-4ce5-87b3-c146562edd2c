.zoho-ticket-form-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.zoho-ticket-form-container h2 {
  margin-bottom: 1.5rem;
  color: #333;
  text-align: center;
}

.zoho-ticket-form .form-group {
  margin-bottom: 1.5rem;
  width: 100%;
}

.zoho-ticket-form .form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 0;
}

.zoho-ticket-form .form-row .form-group {
  flex: 1;
}

.zoho-ticket-form label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.zoho-ticket-form input,
.zoho-ticket-form select,
.zoho-ticket-form textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.zoho-ticket-form input:focus,
.zoho-ticket-form select:focus,
.zoho-ticket-form textarea:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.zoho-ticket-form .form-actions {
  margin-top: 2rem;
  text-align: center;
}

.zoho-ticket-form .submit-button {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.zoho-ticket-form .submit-button:hover {
  background-color: #3a7bc8;
}

.zoho-ticket-form .submit-button:disabled {
  background-color: #a0c0e8;
  cursor: not-allowed;
}

.alert {
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: 4px;
}

.alert-error {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ef9a9a;
}

.alert-success {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #a5d6a7;
}

@media (max-width: 768px) {
  .zoho-ticket-form .form-row {
    flex-direction: column;
    gap: 0;
  }
}