import { Request, Response } from 'express';
import { EMAIL_TEMPLATES, TemplateRenderer } from '../../templates';
import { EmailPreviewService } from '../../services/email-preview/email-preview.service';
import { ApiResponse } from '../../helper/apiResponse';
import catchAsync from '../../utils/catchAsync';

export class EmailPreviewController {
  private emailPreviewService: EmailPreviewService;

  constructor() {
    this.emailPreviewService = new EmailPreviewService();
  }

  /**
   * Get list of all available email templates
   */
  getTemplateList = catchAsync(async (req: Request, res: Response) => {
    const templates = this.emailPreviewService.getAvailableTemplates();
    return ApiResponse.success(res, templates, 'Email templates retrieved successfully');
  });

  /**
   * Preview a specific email template with sample data
   */
  previewTemplate = catchAsync(async (req: Request, res: Response) => {
    const { templateName } = req.params;
    const customData = req.body;

    if (!templateName || !(templateName in EMAIL_TEMPLATES)) {
      return ApiResponse.error(res, 'Invalid template name', 400);
    }

    const renderedTemplate = this.emailPreviewService.renderTemplatePreview(
      templateName as keyof typeof EMAIL_TEMPLATES,
      customData
    );

    // Return HTML directly for browser preview
    res.setHeader('Content-Type', 'text/html');
    res.send(renderedTemplate);
  });

  /**
   * Get sample data for a specific template
   */
  getTemplateSampleData = catchAsync(async (req: Request, res: Response) => {
    const { templateName } = req.params;

    if (!templateName || !(templateName in EMAIL_TEMPLATES)) {
      return ApiResponse.error(res, 'Invalid template name', 400);
    }

    const sampleData = this.emailPreviewService.getSampleDataForTemplate(
      templateName as keyof typeof EMAIL_TEMPLATES
    );

    return ApiResponse.success(res, sampleData, 'Sample data retrieved successfully');
  });

  /**
   * Test email template rendering with custom data
   */
  testTemplate = catchAsync(async (req: Request, res: Response) => {
    const { templateName } = req.params;
    const testData = req.body;

    if (!templateName || !(templateName in EMAIL_TEMPLATES)) {
      return ApiResponse.error(res, 'Invalid template name', 400);
    }

    try {
      const renderedTemplate = TemplateRenderer.render(
        templateName as keyof typeof EMAIL_TEMPLATES,
        testData
      );

      return ApiResponse.success(res, {
        templateName,
        testData,
        renderedHtml: renderedTemplate,
        success: true
      }, 'Template rendered successfully');
    } catch (error) {
      return ApiResponse.error(res, `Template rendering failed: ${error}`, 400);
    }
  });

  /**
   * Get template variables/placeholders
   */
  getTemplateVariables = catchAsync(async (req: Request, res: Response) => {
    const { templateName } = req.params;

    if (!templateName || !(templateName in EMAIL_TEMPLATES)) {
      return ApiResponse.error(res, 'Invalid template name', 400);
    }

    const variables = this.emailPreviewService.extractTemplateVariables(
      templateName as keyof typeof EMAIL_TEMPLATES
    );

    return ApiResponse.success(res, variables, 'Template variables extracted successfully');
  });

  /**
   * Render preview dashboard with all templates
   */
  renderPreviewDashboard = catchAsync(async (req: Request, res: Response) => {
    const dashboardHtml = this.emailPreviewService.generatePreviewDashboard();
    
    res.setHeader('Content-Type', 'text/html');
    res.send(dashboardHtml);
  });

  /**
   * Send test email
   */
  sendTestEmail = catchAsync(async (req: Request, res: Response) => {
    const { templateName } = req.params;
    const { email, customData } = req.body;

    if (!templateName || !(templateName in EMAIL_TEMPLATES)) {
      return ApiResponse.error(res, 'Invalid template name', 400);
    }

    if (!email) {
      return ApiResponse.error(res, 'Email address is required', 400);
    }

    try {
      await this.emailPreviewService.sendTestEmail(
        templateName as keyof typeof EMAIL_TEMPLATES,
        email,
        customData
      );

      return ApiResponse.success(res, {
        templateName,
        email,
        sent: true
      }, 'Test email sent successfully');
    } catch (error) {
      return ApiResponse.error(res, `Failed to send test email: ${error}`, 500);
    }
  });
}