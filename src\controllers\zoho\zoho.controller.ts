import { Request, Response, NextFunction } from "express";
import * as ApiResponse from "../../helper/apiResponse";
import axios from "axios";
import qs from "qs";
import zohoClient from "../../lib/axiosPaypal";

export class ZohoController {
  async initiate(req: Request, res: Response, next: NextFunction) {
    const clientId = process.env.ZOHO_CLIENT_ID!;
    const redirectUri = process.env.ZOHO_REDIRECT_URI!;
    const scope = [
      "ZohoSubscriptions.customers.CREATE",
      "ZohoSubscriptions.customers.READ",
      "ZohoSubscriptions.customers.UPDATE",
      "ZohoSubscriptions.customers.DELETE",

      "ZohoSubscriptions.products.CREATE",
      "ZohoSubscriptions.products.READ",
      "ZohoSubscriptions.products.UPDATE",
      "ZohoSubscriptions.products.DELETE",

      "ZohoSubscriptions.subscriptions.CREATE",
      "ZohoSubscriptions.subscriptions.READ",
      "ZohoSubscriptions.subscriptions.UPDATE",
      "ZohoSubscriptions.subscriptions.DELETE",

      "ZohoSubscriptions.invoices.CREATE",
      "ZohoSubscriptions.invoices.READ",
      "ZohoSubscriptions.invoices.UPDATE",
      "ZohoSubscriptions.invoices.DELETE",

      "ZohoSubscriptions.webhooks.READ",

      "ZohoSubscriptions.payments.CREATE",
      "ZohoSubscriptions.payments.READ",
      "ZohoSubscriptions.payments.UPDATE",
      "ZohoSubscriptions.payments.DELETE",
      "ZohoSubscriptions.plans.CREATE",
      "ZohoSubscriptions.plans.READ",
      "ZohoSubscriptions.plans.UPDATE",
      "ZohoSubscriptions.plans.DELETE",

      "ZohoSubscriptions.addons.CREATE",
      "ZohoSubscriptions.addons.READ",
      "ZohoSubscriptions.addons.UPDATE",
      "ZohoSubscriptions.addons.DELETE",

      "ZohoSubscriptions.coupons.CREATE",
      "ZohoSubscriptions.coupons.READ",
      "ZohoSubscriptions.coupons.UPDATE",
      "ZohoSubscriptions.coupons.DELETE",

      "ZohoSubscriptions.hostedpages.CREATE",
      "ZohoSubscriptions.hostedpages.READ",

      "ZohoSubscriptions.settings.READ",
    ].join(",");

    console.log("clientId", clientId);
    console.log("redirectUri", redirectUri);

    const oauthUrl = `https://accounts.zoho.in/oauth/v2/auth?scope=${scope}&client_id=${clientId}&response_type=code&access_type=offline&redirect_uri=${redirectUri}&state=secure`;

    res.redirect(oauthUrl);
  }

  async callback(req: Request, res: Response, next: NextFunction) {
    const { ZOHO_CLIENT_ID, ZOHO_CLIENT_SECRET, ZOHO_REDIRECT_URI } =
      process.env;
    const code = req.query.code as string;

    if (!code) {
      ApiResponse.errorResponse(res, "Authorization code missing");
      return;
    }

    const payload = qs.stringify({
      code,
      client_id: ZOHO_CLIENT_ID,
      client_secret: ZOHO_CLIENT_SECRET,
      redirect_uri: ZOHO_REDIRECT_URI,
      grant_type: "authorization_code",
    });

    const headers = {
      "Content-Type": "application/x-www-form-urlencoded",
    };

    const tokenUrl = `${process.env.ZOHO_BASE_AUTH_URL}/token`;

    const { data } = await axios.post(tokenUrl, payload, { headers });

    ApiResponse.successResponseWithData(res, "Token received", data);
  }

  async refreshToken(req: Request, res: Response, next: NextFunction) {
    const { refresh_token } = req.body;
    const { ZOHO_CLIENT_ID, ZOHO_CLIENT_SECRET, ZOHO_REDIRECT_URI } =
      process.env;

    if (!refresh_token) {
      return ApiResponse.errorResponse(res, "Refresh token is required");
    }

    const payload = qs.stringify({
      refresh_token,
      client_id: ZOHO_CLIENT_ID,
      client_secret: ZOHO_CLIENT_SECRET,
      redirect_uri: ZOHO_REDIRECT_URI,
      grant_type: "refresh_token",
    });

    const { data } = await axios.post(
      `${process.env.ZOHO_BASE_AUTH_URL}/token`,
      payload,
      { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
    );

    ApiResponse.successResponseWithData(res, "Access token refreshed", data);
  }

  async revokeToken(req: Request, res: Response, next: NextFunction) {
    const { refresh_token } = req.body;
    const { ZOHO_CLIENT_ID, ZOHO_CLIENT_SECRET } = process.env;

    if (!refresh_token) {
      return ApiResponse.errorResponse(res, "Refresh token is required");
    }

    const payload = qs.stringify({
      client_id: ZOHO_CLIENT_ID,
      client_secret: ZOHO_CLIENT_SECRET,
      token: refresh_token,
    });

    const { data } = await axios.post(
      `${process.env.ZOHO_BASE_AUTH_URL}/revoke`,
      payload,
      { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
    );

    ApiResponse.successResponseWithData(res, "Token revoked", data);
  }

  // ========== Zoho Subscriptions APIs ==========

  // Helper for setting auth headers
  private getHeaders(token: string) {
    return { Authorization: `Zoho-oauthtoken ${token}` };
  }

  // Plans
  async listPlans(req: Request, res: Response) {
    const { access_token } = req.headers;
    const { data } = await axios.get(
      `https://subscriptions.zoho.com/api/v1/plans`,
      {
        headers: this.getHeaders(access_token as string),
      }
    );
    ApiResponse.successResponseWithData(res, "Plans fetched", data);
  }

  // Addons
  async listAddons(req: Request, res: Response) {
    const { access_token } = req.headers;
    const { data } = await axios.get(
      `https://subscriptions.zoho.com/api/v1/addons`,
      {
        headers: this.getHeaders(access_token as string),
      }
    );
    ApiResponse.successResponseWithData(res, "Addons fetched", data);
  }

  // Customers
  async createCustomer(req: Request, res: Response) {
    const { access_token } = req.headers;
    const { data } = await axios.post(
      `https://subscriptions.zoho.com/api/v1/customers`,
      req.body,
      {
        headers: this.getHeaders(access_token as string),
      }
    );
    ApiResponse.successResponseWithData(res, "Customer created", data);
  }

  async listCustomers(req: Request, res: Response) {
    const { access_token } = req.headers;
    const { data } = await zohoClient.get("customers");
    ApiResponse.successResponseWithData(res, "Customers fetched", data);
  }

  // Subscriptions
  async createSubscription(req: Request, res: Response) {
    const { access_token } = req.headers;
    const { data } = await axios.post(
      `https://subscriptions.zoho.com/api/v1/subscriptions`,
      req.body,
      {
        headers: this.getHeaders(access_token as string),
      }
    );
    ApiResponse.successResponseWithData(res, "Subscription created", data);
  }

  async listSubscriptions(req: Request, res: Response) {
    const { access_token } = req.headers;
    const { data } = await axios.get(
      `https://subscriptions.zoho.com/api/v1/subscriptions`,
      {
        headers: this.getHeaders(access_token as string),
      }
    );
    ApiResponse.successResponseWithData(res, "Subscriptions fetched", data);
  }
}
