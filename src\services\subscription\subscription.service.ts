import prisma from "../../prisma";
import { SubscriptionStatus } from "@prisma/client";

export class SubscriptionService {
  
  /**
   * Get subscription status for a user (CLIENT role)
   */
  async getUserSubscriptionStatus(userId: string) {
    try {
      const subscriptions = await prisma.subscription.findMany({
        where: {
          userId: userId,
        },
        include: {
          package: {
            select: {
              id: true,
              name: true,
              features: {
                include: {
                  feature: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      const activeSubscriptions = this.filterActiveSubscriptions(subscriptions);
      const hasActiveSubscription = activeSubscriptions.length > 0;

      return {
        subscriptions,
        activeSubscriptions,
        hasActiveSubscription,
        subscriptionCount: subscriptions.length,
        activeSubscriptionCount: activeSubscriptions.length
      };
    } catch (error: any) {
      console.error("Error getting user subscription status:", error);
      throw error;
    }
  }

  /**
   * Get client subscription status for non-client users (COWORKER, ANNOTATOR, etc.)
   */
  async getClientSubscriptionForUser(userId: string) {
    try {
      // First, find the user and their client owner
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          role: true,
          clientOwnerId: true,
          clientOwner: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });

      if (!user) {
        throw new Error("User not found");
      }

      let clientId = userId;

      // If user is not a client, find their client owner
      if (user.role !== 'CLIENT') {
        if (!user.clientOwnerId) {
          throw new Error("User is not associated with any client");
        }
        clientId = user.clientOwnerId;
      }

      // Get client's subscription status
      return await this.getUserSubscriptionStatus(clientId);
    } catch (error: any) {
      console.error("Error getting client subscription for user:", error);
      throw error;
    }
  }

  /**
   * Check if user has access to a specific feature
   */
  async checkFeatureAccess(userId: string, featureName: string): Promise<boolean> {
    try {
      let subscriptionStatus;
      
      // Get user info to determine if they're a client or need to check client's subscription
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { role: true, clientOwnerId: true }
      });

      if (!user) {
        return false;
      }

      if (user.role === 'ADMIN') {
        return true; // Admins have access to all features
      }

      if (user.role === 'CLIENT') {
        subscriptionStatus = await this.getUserSubscriptionStatus(userId);
      } else {
        subscriptionStatus = await this.getClientSubscriptionForUser(userId);
      }

      if (!subscriptionStatus.hasActiveSubscription) {
        return false;
      }

      // Get combined features from all active subscriptions
      const combinedFeatures = this.getCombinedFeatures(subscriptionStatus.activeSubscriptions);
      return combinedFeatures.includes(featureName);
    } catch (error: any) {
      console.error("Error checking feature access:", error);
      return false;
    }
  }

  /**
   * Get comprehensive subscription summary for a user
   */
  async getSubscriptionSummary(userId: string) {
    try {
      let subscriptionStatus;
      
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { role: true, clientOwnerId: true, name: true, email: true }
      });

      if (!user) {
        throw new Error("User not found");
      }

      if (user.role === 'ADMIN') {
        return {
          hasActiveSubscription: true,
          subscriptionCount: 0,
          activeSubscriptionCount: 0,
          availableFeatures: ['ALL_FEATURES'],
          userRole: 'ADMIN',
          message: 'Admin users have access to all features'
        };
      }

      if (user.role === 'CLIENT') {
        subscriptionStatus = await this.getUserSubscriptionStatus(userId);
      } else {
        subscriptionStatus = await this.getClientSubscriptionForUser(userId);
      }

      const combinedFeatures = this.getCombinedFeatures(subscriptionStatus.activeSubscriptions);
      const bestSubscription = this.getBestActiveSubscription(subscriptionStatus.activeSubscriptions);

      return {
        ...subscriptionStatus,
        availableFeatures: combinedFeatures,
        bestSubscription,
        userRole: user.role,
        message: subscriptionStatus.hasActiveSubscription 
          ? `Active subscription with ${combinedFeatures.length} features available`
          : 'No active subscription found'
      };
    } catch (error: any) {
      console.error("Error getting subscription summary:", error);
      throw error;
    }
  }

  /**
   * Filter subscriptions to only include active ones
   */
  private filterActiveSubscriptions(subscriptions: any[]) {
    const now = new Date();
    
    return subscriptions.filter(subscription => {
      // Check status
      if (subscription.status !== SubscriptionStatus.ACTIVE) {
        return false;
      }

      // Check if subscription has expired
      if (subscription.endDate && subscription.endDate < now) {
        return false;
      }

      return true;
    });
  }

  /**
   * Get the most comprehensive active subscription (highest feature count)
   */
  private getBestActiveSubscription(activeSubscriptions: any[]) {
    if (activeSubscriptions.length === 0) return null;
    
    // Sort by feature count (descending) and return the best one
    return activeSubscriptions.sort((a, b) => {
      const aFeatureCount = a.package?.features?.length || 0;
      const bFeatureCount = b.package?.features?.length || 0;
      return bFeatureCount - aFeatureCount;
    })[0];
  }

  /**
   * Get all unique features from multiple active subscriptions
   */
  private getCombinedFeatures(activeSubscriptions: any[]) {
    const allFeatures = new Set<string>();
    
    activeSubscriptions.forEach(subscription => {
      subscription.package?.features?.forEach((pf: any) => {
        if (pf.available) {
          allFeatures.add(pf.feature.rule);
        }
      });
    });

    return Array.from(allFeatures);
  }

  /**
   * Get detailed subscription information for a user
   */
  async getDetailedSubscriptionInfo(userId: string) {
    try {
      const subscriptionStatus = await this.getUserSubscriptionStatus(userId);
      
      const subscriptionInfo = {
        ...subscriptionStatus,
        expiringSubscriptions: this.getExpiringSubscriptions(subscriptionStatus.subscriptions),
        expiredSubscriptions: this.getExpiredSubscriptions(subscriptionStatus.subscriptions),
        availableFeatures: this.getAvailableFeatures(subscriptionStatus.activeSubscriptions)
      };

      return subscriptionInfo;
    } catch (error: any) {
      console.error("Error getting detailed subscription info:", error);
      throw error;
    }
  }

  /**
   * Get subscriptions expiring within the next 7 days
   */
  private getExpiringSubscriptions(subscriptions: any[]) {
    const now = new Date();
    const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    return subscriptions.filter(subscription => {
      return subscription.endDate && 
             subscription.endDate > now && 
             subscription.endDate <= sevenDaysFromNow &&
             subscription.status === SubscriptionStatus.ACTIVE;
    });
  }

  /**
   * Get expired subscriptions
   */
  private getExpiredSubscriptions(subscriptions: any[]) {
    const now = new Date();
    
    return subscriptions.filter(subscription => {
      return (subscription.endDate && subscription.endDate < now) ||
             subscription.status === SubscriptionStatus.EXPIRED ||
             subscription.status === SubscriptionStatus.CANCELLED;
    });
  }

  /**
   * Get all available features from active subscriptions
   */
  private getAvailableFeatures(activeSubscriptions: any[]) {
    const features = new Set();
    
    activeSubscriptions.forEach(subscription => {
      subscription.package.features.forEach((pf: any) => {
        if (pf.available) {
          features.add(pf.feature.rule);
        }
      });
    });

    return Array.from(features);
  }

  /**
   * Check if user can perform a specific action based on subscription limits
   */
  async checkActionLimit(userId: string, action: string, currentCount: number): Promise<{ allowed: boolean; limit?: number; message?: string }> {
    try {
      const subscriptionStatus = await this.getUserSubscriptionStatus(userId);
      
      if (!subscriptionStatus.hasActiveSubscription) {
        return {
          allowed: false,
          message: "No active subscription found"
        };
      }

      // This is a placeholder for action-specific limits
      // You can implement specific limits based on package features
      const actionLimits: { [key: string]: number } = {
        'create_project': 10,
        'create_task': 100,
        'add_coworker': 5,
        'send_message': 1000
      };

      const limit = actionLimits[action];
      
      if (limit && currentCount >= limit) {
        return {
          allowed: false,
          limit,
          message: `You have reached the limit of ${limit} for ${action}. Please upgrade your subscription.`
        };
      }

      return {
        allowed: true,
        limit
      };
    } catch (error: any) {
      console.error("Error checking action limit:", error);
      return {
        allowed: false,
        message: "Failed to check subscription limits"
      };
    }
  }

  /**
   * Update subscription status (for internal use)
   */
  async updateSubscriptionStatus(subscriptionId: string, status: SubscriptionStatus, endDate?: Date) {
    try {
      const updateData: any = {
        status,
        updatedAt: new Date()
      };

      if (endDate) {
        updateData.endDate = endDate;
      }

      const updatedSubscription = await prisma.subscription.update({
        where: { id: subscriptionId },
        data: updateData
      });

      console.log(`Subscription ${subscriptionId} status updated to ${status}`);
      return updatedSubscription;
    } catch (error: any) {
      console.error("Error updating subscription status:", error);
      throw error;
    }
  }

  /**
   * Get all features from database
   */
  async getAllFeatures() {
    try {
      const features = await prisma.feature.findMany({
        orderBy: { rule: 'asc' }
      });
      return features;
    } catch (error: any) {
      console.error("Error getting all features:", error);
      throw error;
    }
  }

  /**
   * Batch update expired subscriptions
   */
  async updateExpiredSubscriptions() {
    try {
      const now = new Date();
      
      const expiredSubscriptions = await prisma.subscription.updateMany({
        where: {
          endDate: {
            lt: now
          },
          status: {
            in: [SubscriptionStatus.ACTIVE, SubscriptionStatus.PENDING]
          }
        },
        data: {
          status: SubscriptionStatus.EXPIRED,
          updatedAt: now
        }
      });

      console.log(`Updated ${expiredSubscriptions.count} expired subscriptions`);
      return expiredSubscriptions;
    } catch (error: any) {
      console.error("Error updating expired subscriptions:", error);
      throw error;
    }
  }
}