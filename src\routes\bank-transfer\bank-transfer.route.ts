import { Router } from "express";
import { BankTransferController } from "../../controllers/bank-transfer/bank-transfer.controller";
// import { BankTransferAnalyticsController } from "../../controllers/bank-transfer/bank-transfer-analytics.controller";
import { asyncHand<PERSON> } from "../../middlewares/asyncHandler";
import authMiddleware from "../../middlewares/checkAuth";
import { hasRole } from "../../middlewares/checkRole";
import multer from "multer";
import { upload } from "../../utils/chats.multer";

const router = Router();
const controller = new BankTransferController();
// const analyticsController = new BankTransferAnalyticsController();

// Configure multer for file upload

// Public route to get bank transfer information
router.get(
  "/info",
  asyncHandler(controller.getBankTransferInfo.bind(controller))
);

// Submit bank transfer payment details
router.post(
  "/submit",
  authMiddleware,
  upload.single("screenshot"),
  asyncHandler(controller.submitBankTransferPayment.bind(controller))
);

// Get user's bank transfer payments
router.get(
  "/my-payments",
  authMiddleware,
  asyncHandler(controller.getUserBankTransferPayments.bind(controller))
);

// Get bank transfer payment details
router.get(
  "/payment/:paymentId",
  authMiddleware,
  asyncHandler(controller.getBankTransferPaymentDetails.bind(controller))
);

// Admin routes
// Get pending bank transfer payments (admin only)
router.get(
  "/admin/pending",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(controller.getPendingBankTransferPayments.bind(controller))
);

// Verify bank transfer payment (admin only)
router.post(
  "/admin/verify/:bankTransferPaymentId",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(controller.verifyBankTransferPayment.bind(controller))
);

// Get bank transfer analytics (admin only)
// router.get(
//   "/admin/analytics",
//   authMiddleware,
//   hasRole("ADMIN"),
//   asyncHandler(analyticsController.getBankTransferAnalytics.bind(analyticsController))
// );

// Get all verified bank transfer users (admin only)
router.get(
  "/admin/verified-users",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(controller.getVerifiedBankTransferUsers.bind(controller))
);

export default router;
