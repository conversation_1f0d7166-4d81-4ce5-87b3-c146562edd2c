import React, { useState, useEffect } from 'react';
import axios from 'axios';
import ZohoTicketForm from './ZohoTicketForm';
import ZohoTicketComments from './ZohoTicketComments';
import './ZohoTicketSystem.css';

const ZohoTicketSystem = ({ authToken, userId }) => {
  const [tickets, setTickets] = useState([]);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [view, setView] = useState('list'); // 'list', 'create', 'view'

  // Fetch user's tickets
  useEffect(() => {
    const fetchTickets = async () => {
      if (!authToken) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        // You might need to adjust this endpoint based on your API
        const response = await axios.get('/api/zoho-desk/tickets', {
          headers: {
            'Authorization': `Bearer ${authToken}`
          },
          params: {
            // Filter by the current user if needed
            contactId: userId
          }
        });
        
        setTickets(response.data.data.tickets || []);
      } catch (err) {
        console.error('Failed to fetch tickets:', err);
        setError('Failed to load tickets. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchTickets();
  }, [authToken, userId]);

  const handleTicketCreated = (newTicket) => {
    setTickets(prevTickets => [newTicket, ...prevTickets]);
    setView('list');
  };

  const handleViewTicket = (ticket) => {
    setSelectedTicket(ticket);
    setView('view');
  };

  const renderTicketsList = () => {
    if (isLoading) {
      return <div className="loading">Loading tickets...</div>;
    }
    
    if (error) {
      return <div className="error-message">{error}</div>;
    }
    
    if (tickets.length === 0) {
      return (
        <div className="no-tickets">
          <p>You don't have any support tickets yet.</p>
          <button onClick={() => setView('create')} className="create-ticket-btn">
            Create Your First Ticket
          </button>
        </div>
      );
    }
    
    return (
      <div className="tickets-list">
        {tickets.map(ticket => (
          <div 
            key={ticket.id} 
            className={`ticket-item ${ticket.status.toLowerCase()}`}
            onClick={() => handleViewTicket(ticket)}
          >
            <div className="ticket-header">
              <span className="ticket-id">#{ticket.ticketNumber}</span>
              <span className={`ticket-status ${ticket.status.toLowerCase()}`}>
                {ticket.status}
              </span>
            </div>
            <h3 className="ticket-subject">{ticket.subject}</h3>
            <div className="ticket-meta">
              <span className="ticket-date">
                {new Date(ticket.createdTime).toLocaleDateString()}
              </span>
              <span className="ticket-priority">{ticket.priority}</span>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderTicketDetail = () => {
    if (!selectedTicket) return null;
    
    return (
      <div className="ticket-detail">
        <div className="ticket-detail-header">
          <button 
            onClick={() => setView('list')} 
            className="back-button"
          >
            &larr; Back to Tickets
          </button>
          <span className={`ticket-status ${selectedTicket.status.toLowerCase()}`}>
            {selectedTicket.status}
          </span>
        </div>
        
        <h2 className="ticket-detail-subject">
          #{selectedTicket.ticketNumber}: {selectedTicket.subject}
        </h2>
        
        <div className="ticket-detail-meta">
          <div className="meta-item">
            <span className="meta-label">Created:</span>
            <span className="meta-value">
              {new Date(selectedTicket.createdTime).toLocaleString()}
            </span>
          </div>
          <div className="meta-item">
            <span className="meta-label">Priority:</span>
            <span className="meta-value">{selectedTicket.priority}</span>
          </div>
          <div className="meta-item">
            <span className="meta-label">Department:</span>
            <span className="meta-value">{selectedTicket.departmentName || 'General'}</span>
          </div>
        </div>
        
        <div className="ticket-detail-description">
          <h3>Description</h3>
          <div className="description-content">
            {selectedTicket.description}
          </div>
        </div>
        
        <ZohoTicketComments 
          ticketId={selectedTicket.id} 
          authToken={authToken}
          onError={(err) => setError(err.message)}
        />
      </div>
    );
  };

  return (
    <div className="zoho-ticket-system">
      <div className="ticket-system-header">
        <h1>Support Tickets</h1>
        
        <div className="ticket-actions">
          {view === 'list' && (
            <button 
              onClick={() => setView('create')} 
              className="create-ticket-btn"
            >
              Create New Ticket
            </button>
          )}
        </div>
      </div>
      
      {view === 'list' && renderTicketsList()}
      {view === 'create' && (
        <div className="create-ticket-view">
          <button 
            onClick={() => setView('list')} 
            className="back-button"
          >
            &larr; Back to Tickets
          </button>
          <ZohoTicketForm 
            authToken={authToken}
            userId={userId}
            onSuccess={handleTicketCreated}
            onError={(err) => setError(err.message)}
          />
        </div>
      )}
      {view === 'view' && renderTicketDetail()}
    </div>
  );
};

export default ZohoTicketSystem;