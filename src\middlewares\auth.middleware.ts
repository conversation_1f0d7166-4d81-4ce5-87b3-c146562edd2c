import jwt from "jsonwebtoken";
import { Request, Response, NextFunction } from "express";
import { AppError } from "../utils/ApiError";

interface JwtPayload {
  id: string;
  email: string;
  role: string;
}

export const authenticateUser = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    const tokenFromHeader = req.headers.authorization?.split(" ")[1];
    const tokenFromCookies = req.cookies?.authToken;
    const token = tokenFromCookies || tokenFromHeader;

    if (!token) {
      throw new AppError("Unauthorized", 401);
    }

    // Verify the token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!);
    req.user = decoded;
    next();
  } catch (err) {
    next(new AppError("Unauthorized", 401)); 
  }
};
