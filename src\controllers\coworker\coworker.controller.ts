import { JwtPayload } from "jsonwebtoken";
import { CoworkerService } from "../../services/coworker/coworker.service";
import { Request, Response } from "express";
import {
  errorResponse,
  successResponse,
  successResponseWithData,
} from "../../helper/apiResponse";

interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}

export class CoworkerController {
   private CoworkerService: CoworkerService;
  
    constructor() {
      this.CoworkerService = new CoworkerService();
    }
  async inviteCoworker(req: AuthenticatedRequest, res: Response) {
    try {
      const { email, permissionRole } = req.body;
      const clientId = req.user?.userId;
      //console.log("Decoded User:", req.user);

      const result = await this.CoworkerService.inviteCoworker(
        email,
        permissionRole,
        clientId
      );

      if (result.updated) {
        return await successResponse(
          res,
          "Co-worker already exists. Client owner updated."
        );
      } else if (result.created) {
        return await successResponseWithData(
          res,
          "Co-worker invited successfully.",
          result.coworker
        );
      } else {
        return await errorResponse(res, "Unexpected result.");
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error("CoworkerController Error:", error.message);
        return await errorResponse(res, error.message);
      }
      console.error("Unknown Error:", error);
      return await errorResponse(res, "Something went wrong.");
    }
  }

  async listCoworkers(req: AuthenticatedRequest, res: Response) {
    try {
      const clientId = req.user?.userId;
      const coworkers = await CoworkerService.listCoworkers(
        clientId,
        req.query
      );
      return successResponseWithData(
        res,
        "Coworkers fetched successfully.",
        coworkers,
        // coworkers.length
      );
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error("ListCoworkers Error:", error.message);
        return errorResponse(res, error.message);
      }
      return errorResponse(res, "Something went wrong.");
    }
  }

  async updateCoworkerPermission(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params;
      const { permissionRole } = req.body;
      const clientId = req.user?.userId;

      const updatedCoworker = await CoworkerService.updateCoworkerPermission(
        id,
        permissionRole,
        clientId
      );

      return successResponseWithData(
        res,
        "Permission updated successfully.",
        updatedCoworker
      );
    } catch (error: unknown) {
      console.error("UpdatePermission Error:", error);

      if (error instanceof Error) {
        return errorResponse(res, error.message);
      }

      return errorResponse(res, "Something went wrong.");
    }
  }

  async resendInvite(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params;
      const clientId = req.user?.userId;

      const result = await CoworkerService.resendInvite(id, clientId);

      return successResponseWithData(
        res,
        "Invite resent successfully.",
        result
      );
    } catch (error: unknown) {
      console.error("ResendInvite Error:", error);

      if (error instanceof Error) {
        return errorResponse(res, error.message);
      }

      return errorResponse(res, "Something went wrong.");
    }
  }

  async acceptInvite(req: Request, res: Response) {
    try {
      const { email, password, name, token } = req.body;

      const result = await CoworkerService.acceptInvite(email, password, name, token);

      return successResponseWithData(
        res,
        "Invite accepted successfully.",
        result
      );
    } catch (error: unknown) {
      console.error("AcceptInvite Error:", error);

      if (error instanceof Error) {
        return errorResponse(res, error.message);
      }

      return errorResponse(res, "Something went wrong.");
    }
  }
}
