Stack trace:
Frame         Function      Args
0007FFFF9B70  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF9B70, 0007FFFF8A70) msys-2.0.dll+0x1FEBA
0007FFFF9B70  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9E48) msys-2.0.dll+0x67F9
0007FFFF9B70  000210046832 (000210285FF9, 0007FFFF9A28, 0007FFFF9B70, 000000000000) msys-2.0.dll+0x6832
0007FFFF9B70  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9B70  0002100690B4 (0007FFFF9B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9E50  00021006A49D (0007FFFF9B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD04720000 ntdll.dll
7FFD03AF0000 KERNEL32.DLL
7FFD01CB0000 KERNELBASE.dll
7FFD02B50000 USER32.dll
7FFD02140000 win32u.dll
7FFD045A0000 GDI32.dll
7FFD02170000 gdi32full.dll
7FFD01A80000 msvcp_win.dll
7FFD01870000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD03C50000 advapi32.dll
7FFD045D0000 msvcrt.dll
7FFD02950000 sechost.dll
7FFD024C0000 RPCRT4.dll
7FFD00E70000 CRYPTBASE.DLL
7FFD020A0000 bcryptPrimitives.dll
7FFD02910000 IMM32.DLL
