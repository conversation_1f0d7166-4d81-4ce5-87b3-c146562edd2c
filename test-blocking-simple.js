// Simple test to verify the blocking functionality
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testBlockingLogic() {
  console.log('🧪 Testing User Blocking Logic...\n');

  try {
    // Test 1: Check if AccountStatus enum exists
    console.log('1. Testing AccountStatus enum...');
    const testUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' },
      select: { accountStatus: true }
    });
    console.log('   ✅ AccountStatus field exists:', testUser?.accountStatus || 'ACTIVE');

    // Test 2: Check if we can update account status
    console.log('\n2. Testing account status update...');
    const user = await prisma.user.findFirst({
      where: { 
        role: { in: ['ANNOTATOR', 'CLIENT'] },
        accountStatus: 'ACTIVE'
      }
    });

    if (user) {
      console.log(`   Found user: ${user.email}`);
      
      // Simulate suspension
      const suspended = await prisma.user.update({
        where: { id: user.id },
        data: {
          accountStatus: 'SUSPENDED',
          suspendedUntil: new Date(Date.now() + 24 * 60 * 60 * 1000) // 1 day
        }
      });
      console.log('   ✅ User suspended successfully');

      // Simulate reactivation
      const reactivated = await prisma.user.update({
        where: { id: user.id },
        data: {
          accountStatus: 'ACTIVE',
          suspendedUntil: null
        }
      });
      console.log('   ✅ User reactivated successfully');
    } else {
      console.log('   ⚠️  No suitable user found for testing');
    }

    // Test 3: Check suspension expiry logic
    console.log('\n3. Testing suspension expiry logic...');
    const now = new Date();
    const pastDate = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 1 day ago
    
    console.log('   Current time:', now.toISOString());
    console.log('   Past suspension date:', pastDate.toISOString());
    console.log('   Should auto-reactivate:', now > pastDate ? '✅ Yes' : '❌ No');

    console.log('\n🎉 All blocking logic tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testBlockingLogic();