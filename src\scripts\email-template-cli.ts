#!/usr/bin/env ts-node

import { Command } from 'commander';
import { EmailPreviewService } from '../services/email-preview/email-preview.service';
import { EMAIL_TEMPLATES, Template<PERSON>enderer } from '../templates';
import { promises as fs } from 'fs';
import path from 'path';

const program = new Command();
const emailPreviewService = new EmailPreviewService();

program
  .name('email-template-cli')
  .description('CLI tool for email template development and testing')
  .version('1.0.0');

// List all templates
program
  .command('list')
  .description('List all available email templates')
  .option('-c, --category <category>', 'Filter by category')
  .action(async (options) => {
    const templates = emailPreviewService.getAvailableTemplates();
    
    let filteredTemplates = templates;
    if (options.category) {
      filteredTemplates = templates.filter(t => 
        t.category.toLowerCase().includes(options.category.toLowerCase())
      );
    }

    console.log('\n📧 Available Email Templates:\n');
    
    const categories = new Set(filteredTemplates.map(t => t.category));
    
    categories.forEach(category => {
      console.log(`\n📁 ${category}:`);
      const categoryTemplates = filteredTemplates.filter(t => t.category === category);
      
      categoryTemplates.forEach(template => {
        console.log(`  • ${template.name} - ${template.description}`);
        console.log(`    Variables: ${template.variables.length} (${template.variables.slice(0, 3).join(', ')}${template.variables.length > 3 ? '...' : ''})`);
      });
    });
    
    console.log(`\nTotal: ${filteredTemplates.length} templates\n`);
  });

// Preview template
program
  .command('preview <templateName>')
  .description('Preview an email template')
  .option('-o, --output <file>', 'Save preview to file')
  .option('-d, --data <json>', 'Custom data as JSON string')
  .action(async (templateName, options) => {
    if (!(templateName in EMAIL_TEMPLATES)) {
      console.error(`❌ Template '${templateName}' not found`);
      process.exit(1);
    }

    let customData = {};
    if (options.data) {
      try {
        customData = JSON.parse(options.data);
      } catch (error) {
        console.error('❌ Invalid JSON data provided');
        process.exit(1);
      }
    }

    const rendered = emailPreviewService.renderTemplatePreview(
      templateName as keyof typeof EMAIL_TEMPLATES,
      customData
    );

    if (options.output) {
      await fs.writeFile(options.output, rendered);
      console.log(`✅ Preview saved to ${options.output}`);
    } else {
      console.log('\n📧 Template Preview:\n');
      console.log(rendered);
    }
  });

// Get template info
program
  .command('info <templateName>')
  .description('Get detailed information about a template')
  .action(async (templateName) => {
    if (!(templateName in EMAIL_TEMPLATES)) {
      console.error(`❌ Template '${templateName}' not found`);
      process.exit(1);
    }

    const templates = emailPreviewService.getAvailableTemplates();
    const template = templates.find(t => t.name === templateName);

    if (!template) {
      console.error(`❌ Template info not found`);
      process.exit(1);
    }

    console.log(`\n📧 Template Information: ${template.displayName}\n`);
    console.log(`Name: ${template.name}`);
    console.log(`Category: ${template.category}`);
    console.log(`Description: ${template.description}`);
    console.log(`Variables (${template.variables.length}):`);
    
    template.variables.forEach(variable => {
      console.log(`  • {{${variable}}}`);
    });

    console.log('\nSample Data:');
    console.log(JSON.stringify(template.sampleData, null, 2));
  });

// Generate sample data
program
  .command('sample-data <templateName>')
  .description('Generate sample data for a template')
  .option('-o, --output <file>', 'Save sample data to file')
  .action(async (templateName, options) => {
    if (!(templateName in EMAIL_TEMPLATES)) {
      console.error(`❌ Template '${templateName}' not found`);
      process.exit(1);
    }

    const sampleData = emailPreviewService.getSampleDataForTemplate(
      templateName as keyof typeof EMAIL_TEMPLATES
    );

    const jsonData = JSON.stringify(sampleData, null, 2);

    if (options.output) {
      await fs.writeFile(options.output, jsonData);
      console.log(`✅ Sample data saved to ${options.output}`);
    } else {
      console.log('\n📋 Sample Data:\n');
      console.log(jsonData);
    }
  });

// Test template rendering
program
  .command('test <templateName>')
  .description('Test template rendering with custom data')
  .option('-f, --file <file>', 'Load test data from JSON file')
  .option('-d, --data <json>', 'Test data as JSON string')
  .action(async (templateName, options) => {
    if (!(templateName in EMAIL_TEMPLATES)) {
      console.error(`❌ Template '${templateName}' not found`);
      process.exit(1);
    }

    let testData = {};

    if (options.file) {
      try {
        const fileContent = await fs.readFile(options.file, 'utf-8');
        testData = JSON.parse(fileContent);
      } catch (error) {
        console.error(`❌ Error reading test data file: ${error}`);
        process.exit(1);
      }
    } else if (options.data) {
      try {
        testData = JSON.parse(options.data);
      } catch (error) {
        console.error('❌ Invalid JSON data provided');
        process.exit(1);
      }
    } else {
      // Use sample data
      testData = emailPreviewService.getSampleDataForTemplate(
        templateName as keyof typeof EMAIL_TEMPLATES
      );
    }

    try {
      const rendered = TemplateRenderer.render(
        templateName as keyof typeof EMAIL_TEMPLATES,
        testData
      );

      console.log('✅ Template rendered successfully');
      console.log(`📊 Data used: ${Object.keys(testData).length} variables`);
      console.log(`📏 Output size: ${rendered.length} characters`);
      
      // Check for unresolved variables
      const unresolvedVars = rendered.match(/\{\{[^}]+\}\}/g);
      if (unresolvedVars) {
        console.log(`⚠️  Unresolved variables found: ${unresolvedVars.join(', ')}`);
      } else {
        console.log('✅ All variables resolved');
      }

    } catch (error) {
      console.error(`❌ Template rendering failed: ${error}`);
      process.exit(1);
    }
  });

// Create new template
program
  .command('create <templateName>')
  .description('Create a new email template')
  .option('-c, --category <category>', 'Template category', 'General')
  .option('-d, --desc <description>', 'Template description')
  .action(async (templateName, options) => {
    const templatePath = path.join(__dirname, '../templates', `${templateName}.ts`);
    const htmlPath = path.join(__dirname, '../templates/email', `${templateName}.html`);

    // Check if template already exists
    try {
      await fs.access(templatePath);
      console.error(`❌ Template '${templateName}' already exists`);
      process.exit(1);
    } catch {
      // Template doesn't exist, continue
    }

    const description = options.desc || `${templateName} email template`;
    
    const templateContent = `export const ${templateName}Template = \`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{subject}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #6610f2;
            margin-bottom: 10px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{companyName}}</div>
        </div>

        <h2>{{title}}</h2>
        
        <p>Hi {{firstName}},</p>
        
        <p>{{message}}</p>
        
        <div class="footer">
            <p>Best regards,<br>
            <strong>{{teamName}}</strong><br>
            <strong>{{companyName}}</strong></p>
        </div>
    </div>
</body>
</html>
\`;
`;

    const htmlContent = `<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{subject}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #6610f2;
            margin-bottom: 10px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">{{companyName}}</div>
        </div>

        <h2>{{title}}</h2>
        
        <p>Hi {{firstName}},</p>
        
        <p>{{message}}</p>
        
        <div class="footer">
            <p>Best regards,<br>
            <strong>{{teamName}}</strong><br>
            <strong>{{companyName}}</strong></p>
        </div>
    </div>
</body>
</html>`;

    try {
      await fs.writeFile(templatePath, templateContent);
      await fs.writeFile(htmlPath, htmlContent);
      
      console.log(`✅ Template created successfully:`);
      console.log(`   TypeScript: ${templatePath}`);
      console.log(`   HTML: ${htmlPath}`);
      console.log(`\n📝 Next steps:`);
      console.log(`   1. Add the template to src/templates/index.ts`);
      console.log(`   2. Update the EMAIL_TEMPLATES mapping`);
      console.log(`   3. Test the template: npm run email-cli preview ${templateName}`);
      
    } catch (error) {
      console.error(`❌ Error creating template: ${error}`);
      process.exit(1);
    }
  });

// Validate all templates
program
  .command('validate')
  .description('Validate all email templates')
  .action(async () => {
    console.log('🔍 Validating all email templates...\n');
    
    const templates = emailPreviewService.getAvailableTemplates();
    let errors = 0;
    let warnings = 0;

    for (const template of templates) {
      console.log(`Checking ${template.name}...`);
      
      try {
        // Test rendering with sample data
        const rendered = TemplateRenderer.render(
          template.name as keyof typeof EMAIL_TEMPLATES,
          template.sampleData
        );

        // Check for unresolved variables
        const unresolvedVars = rendered.match(/\{\{[^}]+\}\}/g);
        if (unresolvedVars) {
          console.log(`  ⚠️  Unresolved variables: ${unresolvedVars.join(', ')}`);
          warnings++;
        } else {
          console.log(`  ✅ All variables resolved`);
        }

        // Check for basic HTML structure
        if (!rendered.includes('<!DOCTYPE html>')) {
          console.log(`  ⚠️  Missing DOCTYPE declaration`);
          warnings++;
        }

        if (!rendered.includes('<html>') || !rendered.includes('</html>')) {
          console.log(`  ❌ Invalid HTML structure`);
          errors++;
        }

      } catch (error) {
        console.log(`  ❌ Rendering failed: ${error}`);
        errors++;
      }
    }

    console.log(`\n📊 Validation Summary:`);
    console.log(`   Templates checked: ${templates.length}`);
    console.log(`   Errors: ${errors}`);
    console.log(`   Warnings: ${warnings}`);
    
    if (errors === 0 && warnings === 0) {
      console.log(`   ✅ All templates are valid!`);
    } else if (errors === 0) {
      console.log(`   ⚠️  Templates are functional but have warnings`);
    } else {
      console.log(`   ❌ Some templates have errors that need fixing`);
      process.exit(1);
    }
  });

program.parse();