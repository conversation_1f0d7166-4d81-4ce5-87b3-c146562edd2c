import { Router } from 'express';
import { EmailPreviewController } from '../../controllers/email-preview/email-preview.controller';

const router = Router();
const emailPreviewController = new EmailPreviewController();

/**
 * @swagger
 * /api/email-preview:
 *   get:
 *     summary: Get email template preview dashboard
 *     tags: [Email Preview]
 *     responses:
 *       200:
 *         description: Email template preview dashboard
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 */
router.get('/', emailPreviewController.renderPreviewDashboard);

/**
 * @swagger
 * /api/email-preview/templates:
 *   get:
 *     summary: Get list of all available email templates
 *     tags: [Email Preview]
 *     responses:
 *       200:
 *         description: List of email templates
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                       displayName:
 *                         type: string
 *                       description:
 *                         type: string
 *                       category:
 *                         type: string
 *                       variables:
 *                         type: array
 *                         items:
 *                           type: string
 */
router.get('/templates', emailPreviewController.getTemplateList);

/**
 * @swagger
 * /api/email-preview/template/{templateName}:
 *   get:
 *     summary: Preview a specific email template
 *     tags: [Email Preview]
 *     parameters:
 *       - in: path
 *         name: templateName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the email template
 *     responses:
 *       200:
 *         description: Rendered email template
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 *       400:
 *         description: Invalid template name
 */
router.get('/template/:templateName', emailPreviewController.previewTemplate);

/**
 * @swagger
 * /api/email-preview/template/{templateName}:
 *   post:
 *     summary: Preview email template with custom data
 *     tags: [Email Preview]
 *     parameters:
 *       - in: path
 *         name: templateName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the email template
 *     requestBody:
 *       description: Custom data for template rendering
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             additionalProperties: true
 *     responses:
 *       200:
 *         description: Rendered email template with custom data
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 */
router.post('/template/:templateName', emailPreviewController.previewTemplate);

/**
 * @swagger
 * /api/email-preview/template/{templateName}/sample-data:
 *   get:
 *     summary: Get sample data for a specific template
 *     tags: [Email Preview]
 *     parameters:
 *       - in: path
 *         name: templateName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the email template
 *     responses:
 *       200:
 *         description: Sample data for the template
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   additionalProperties: true
 */
router.get('/template/:templateName/sample-data', emailPreviewController.getTemplateSampleData);

/**
 * @swagger
 * /api/email-preview/template/{templateName}/variables:
 *   get:
 *     summary: Get variables/placeholders for a specific template
 *     tags: [Email Preview]
 *     parameters:
 *       - in: path
 *         name: templateName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the email template
 *     responses:
 *       200:
 *         description: Template variables
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: string
 */
router.get('/template/:templateName/variables', emailPreviewController.getTemplateVariables);

/**
 * @swagger
 * /api/email-preview/template/{templateName}/test:
 *   post:
 *     summary: Test email template rendering with custom data
 *     tags: [Email Preview]
 *     parameters:
 *       - in: path
 *         name: templateName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the email template
 *     requestBody:
 *       description: Test data for template rendering
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             additionalProperties: true
 *     responses:
 *       200:
 *         description: Template rendering test result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     templateName:
 *                       type: string
 *                     testData:
 *                       type: object
 *                     renderedHtml:
 *                       type: string
 *                     success:
 *                       type: boolean
 */
router.post('/template/:templateName/test', emailPreviewController.testTemplate);

/**
 * @swagger
 * /api/email-preview/template/{templateName}/send-test:
 *   post:
 *     summary: Send test email
 *     tags: [Email Preview]
 *     parameters:
 *       - in: path
 *         name: templateName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the email template
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address to send test email to
 *               customData:
 *                 type: object
 *                 additionalProperties: true
 *                 description: Custom data for template rendering
 *     responses:
 *       200:
 *         description: Test email sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     templateName:
 *                       type: string
 *                     email:
 *                       type: string
 *                     sent:
 *                       type: boolean
 */
router.post('/template/:templateName/send-test', emailPreviewController.sendTestEmail);

export default router;