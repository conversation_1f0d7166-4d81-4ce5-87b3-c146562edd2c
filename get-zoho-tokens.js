// Quick script to get Zoho Desk tokens
// Run: node get-zoho-tokens.js

const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🔑 Zoho Desk Token Generator\n');

rl.question('Enter your ZOHO_DESK_CLIENT_ID: ', (clientId) => {
  rl.question('Enter your ZOHO_DESK_CLIENT_SECRET: ', (clientSecret) => {
    
    const scopes = [
      'Desk.tickets.ALL',
      'Desk.contacts.ALL', 
      'Desk.basic.READ',
      'Desk.settings.READ',
      'Desk.search.READ'
    ].join(',');
    
    const redirectUri = 'http://localhost:3000/api/zoho-desk/auth/callback';
    
    const authUrl = `https://accounts.zoho.com/oauth/v2/auth?scope=${scopes}&client_id=${clientId}&response_type=code&access_type=offline&redirect_uri=${encodeURIComponent(redirectUri)}`;
    
    console.log('\n📋 Step 1: Visit this URL in your browser:');
    console.log('👉', authUrl);
    console.log('\n📋 Step 2: After authorization, you\'ll be redirected to a URL like:');
    console.log('👉 http://localhost:3000/api/zoho-desk/auth/callback?code=AUTHORIZATION_CODE');
    console.log('\n📋 Step 3: Copy the "code" parameter from the URL and paste it below:\n');
    
    rl.question('Enter the authorization code: ', async (code) => {
      try {
        const fetch = (await import('node-fetch')).default;
        
        const tokenResponse = await fetch('https://accounts.zoho.com/oauth/v2/token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            grant_type: 'authorization_code',
            client_id: clientId,
            client_secret: clientSecret,
            redirect_uri: redirectUri,
            code: code
          })
        });
        
        const tokens = await tokenResponse.json();
        
        if (tokens.access_token) {
          console.log('\n🎉 Success! Here are your tokens:\n');
          console.log('ZOHO_DESK_CLIENT_ID=' + clientId);
          console.log('ZOHO_DESK_CLIENT_SECRET=' + clientSecret);
          console.log('ZOHO_DESK_REFRESH_TOKEN=' + tokens.refresh_token);
          console.log('ZOHO_DESK_API_DOMAIN=https://desk.zoho.com');
          console.log('ZOHO_DESK_AUTH_URL=https://accounts.zoho.com/oauth/v2');
          console.log('\n📝 Add these to your .env file!');
          
          // Get ORG_ID
          console.log('\n🔍 Getting your ORG_ID...');
          const orgResponse = await fetch('https://desk.zoho.com/api/v1/organizations', {
            headers: {
              'Authorization': `Zoho-oauthtoken ${tokens.access_token}`
            }
          });
          
          const orgData = await orgResponse.json();
          if (orgData.data && orgData.data.length > 0) {
            console.log('ZOHO_DESK_ORG_ID=' + orgData.data[0].id);
          }
          
        } else {
          console.log('❌ Error getting tokens:', tokens);
        }
      } catch (error) {
        console.log('❌ Error:', error.message);
      }
      
      rl.close();
    });
  });
});