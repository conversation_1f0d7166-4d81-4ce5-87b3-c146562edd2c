// import { createHmac } from 'crypto';
// import { AppError } from "../../utils/ApiError";

// export class ZohoSSOService {
//   private readonly ssoAuthUrl: string;
//   private readonly portalId: string;
//   private readonly ssoKey: string;

//   constructor() {
//     this.ssoAuthUrl = process.env.ZOHO_DESK_SSO_AUTH_URL || 'https://desk.zoho.in/support/login';
//     this.portalId = process.env.ZOHO_DESK_PORTAL_ID || '';
//     this.ssoKey = process.env.ZOHO_DESK_SSO_KEY || '';

//     if (!this.portalId || !this.ssoKey) {
//       console.error('Zoho Desk SSO configuration is missing. Please set ZOHO_DESK_PORTAL_ID and ZOHO_DESK_SSO_KEY in your environment variables.');
//     }
//   }

//   /**
//    * Generate an SSO URL for a user to access Zoho Desk
//    * 
//    * @param user The user object containing email, name, and other details
//    * @param redirectTo Optional path to redirect to after login
//    * @returns The SSO URL
//    */
//   generateSSOUrl(user: {
//     email: string;
//     name: string;
//     lastname?: string;
//     id: string;
//   }, redirectTo?: string): string {
//     if (!this.portalId || !this.ssoKey) {
//       throw new AppError('Zoho Desk SSO is not configured properly', 500);
//     }
 
//     if (!user.email) {
//       throw new AppError('User email is required for SSO', 400);
//     }

//     // Current timestamp in seconds
//     const timestamp = Math.floor(Date.now() / 1000).toString();
    
//     // Create the SSO data
//     const ssoData = {
//       EMAIL: user.email,
//       FIRSTNAME: user.name,
//       LASTNAME: user.lastname || '',
//       CUSTOMERID: user.id, // Use your internal user ID as the customer ID
//       TIMESTAMP: timestamp
//     };
    
//     // Create the SSO token
//     const dataString = Object.entries(ssoData)
//       .map(([key, value]) => `${key}=${value}`)
//       .join('&');
    
//     // Generate HMAC signature
//     const ssoToken = createHmac('sha256', this.ssoKey)
//       .update(dataString)
//       .digest('hex');
    
//     // Build the SSO URL
//     let ssoUrl = `${this.ssoAuthUrl}?portal=${this.portalId}&${dataString}&ssotoken=${ssoToken}`;
    
//     // Add redirect path if provided
//     if (redirectTo) {
//       ssoUrl += `&redirectTo=${encodeURIComponent(redirectTo)}`;
//     }
    
//     return ssoUrl;
//   }
// }