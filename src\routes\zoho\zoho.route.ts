import { Router, Request, Response } from "express";
import { ZohoController } from "../../controllers/zoho/zoho.controller";
import { asyncHandler } from "../../middlewares/asyncHandler";

const router = Router();

const controller = new ZohoController();

// Step 1: Redirect user to Zoho
router.route("/initiate").get(controller.initiate.bind(controller));

// Step 2: Handle OAuth callback
router.route("/callback").get(controller.callback.bind(controller));

// Step 3: Handle token refresh
router
  .route("/refresh-token")
  .post(asyncHandler(controller.refreshToken.bind(controller)));

router
  .route("/revoke-token")
  .post(asyncHandler(controller.revokeToken.bind(controller)));

// ========== Zoho Subscriptions APIs ==========
router
  .route("/subscriptions")
  .get(asyncHandler(controller.listPlans.bind(controller)))
  .post(asyncHandler(controller.createSubscription.bind(controller)));

router
  .route("/customers")
  .get(asyncHandler(controller.listCustomers.bind(controller)))
  .post(asyncHandler(controller.createCustomer.bind(controller)));

// router
//   .route("/customers/:id")
//   .get(asyncHandler(controller.getCustomerById.bind(controller)))
//   .put(asyncHandler(controller.updateCustomer.bind(controller)))
//   .delete(asyncHandler(controller.deleteCustomer.bind(controller)));

// router
//   .route("/customers/:id/activate")
//   .post(asyncHandler(controller.activateCustomer.bind(controller)));

// router
//   .route("/customers/:id/deactivate")
//   .post(asyncHandler(controller.deactivateCustomer.bind(controller)));

// router
//   .route("/customers/:id/restore")
//   .post(asyncHandler(controller.restoreCustomer.bind(controller)));
// router
//   .route("/customers/:id/stop")
//   .post(asyncHandler(controller.stopCustomer.bind(controller)));
// router
//   .route("/customers/:id/upgrade")
//   .post(asyncHandler(controller.upgradeCustomer.bind(controller)));
// router
//   .route("/customers/:id/downgrade")
//   .post(asyncHandler(controller.downgradeCustomer.bind(controller)));
// router
//   .route("/customers/:id/renew")
//   .post(asyncHandler(controller.renewCustomer.bind(controller)));

// router
//   .route("/addons")
//   .get(asyncHandler(controller.listAddons.bind(controller)))
//   .post(asyncHandler(controller.createAddon.bind(controller)));
// router
//   .route("/addons/:id")
//   .get(asyncHandler(controller.getAddonById.bind(controller)))
//   .put(asyncHandler(controller.updateAddon.bind(controller)))
//   .delete(asyncHandler(controller.deleteAddon.bind(controller)));

// router
//   .route("/addons/:id/activate")
//   .post(asyncHandler(controller.activateAddon.bind(controller)));
// router
//   .route("/addons/:id/deactivate")
//   .post(asyncHandler(controller.deactivateAddon.bind(controller)));
// router
//   .route("/addons/:id/restore")
//   .post(asyncHandler(controller.restoreAddon.bind(controller)));
// router
//   .route("/addons/:id/stop")
//   .post(asyncHandler(controller.stopAddon.bind(controller)));
// router
//   .route("/addons/:id/upgrade")
//   .post(asyncHandler(controller.upgradeAddon.bind(controller)));
// router
//   .route("/addons/:id/downgrade")
//   .post(asyncHandler(controller.downgradeAddon.bind(controller)));
// router
//   .route("/addons/:id/renew")
//   .post(asyncHandler(controller.renewAddon.bind(controller)));
// router
//   .route("/addons/:id/stop")
//   .post(asyncHandler(controller.stopAddon.bind(controller)));

// router
//   .route("/subscriptions/:id")
//   .get(asyncHandler(controller.getSubscriptionById.bind(controller)))
//   .put(asyncHandler(controller.updateSubscription.bind(controller)))
//   .delete(asyncHandler(controller.deleteSubscription.bind(controller)));
// router
//   .route("/subscriptions/:id/renew")
//   .post(asyncHandler(controller.renewSubscription.bind(controller)));
// router
//   .route("/subscriptions/:id/cancel")
//   .post(asyncHandler(controller.cancelSubscription.bind(controller)));
// router
//   .route("/subscriptions/:id/upgrade")
//   .post(asyncHandler(controller.upgradeSubscription.bind(controller)));
// router
//   .route("/subscriptions/:id/downgrade")
//   .post(asyncHandler(controller.downgradeSubscription.bind(controller)));
// router
//   .route("/subscriptions/:id/activate")
//   .post(asyncHandler(controller.activateSubscription.bind(controller)));
// router
//   .route("/subscriptions/:id/stop")
//   .post(asyncHandler(controller.stopSubscription.bind(controller)));
// router
//   .route("/subscriptions/:id/restore")
//   .post(asyncHandler(controller.restoreSubscription.bind(controller)));

export default router;
