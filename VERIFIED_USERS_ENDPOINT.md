# Verified Bank Transfer Users Endpoint

## Overview
This endpoint provides a comprehensive list of all users who have successfully completed bank transfer payments and had them verified by administrators.

## Endpoint Details

### GET /api/bank-transfer/admin/verified-users

**Access:** Admin only  
**Method:** GET  
**Authentication:** Required (JWT <PERSON>)  
**Authorization:** ADMIN role required  

### Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | number | 1 | Page number for pagination |
| `limit` | number | 10 | Number of records per page (max 50) |

### Request Example

```bash
GET /api/bank-transfer/admin/verified-users?page=1&limit=10
Authorization: Bearer <admin_jwt_token>
```

### Response Format

```json
{
  "success": true,
  "message": "Verified bank transfer users retrieved successfully",
  "data": {
    "verifiedUsers": [
      {
        "user": {
          "id": "user_123",
          "name": "<PERSON>",
          "lastname": "Doe",
          "email": "<EMAIL>",
          "role": "CLIENT",
          "createdAt": "2024-01-15T10:30:00Z"
        },
        "totalPayments": 2,
        "totalAmount": 299.98,
        "packages": [
          {
            "id": "pkg_456",
            "name": "Premium Plan",
            "price": 149.99,
            "billingType": "MONTHLY"
          }
        ],
        "subscriptions": [
          {
            "id": "sub_789",
            "status": "ACTIVE",
            "startDate": "2024-01-15T10:30:00Z",
            "endDate": "2024-02-15T10:30:00Z"
          }
        ],
        "firstPaymentDate": "2024-01-15T10:30:00Z",
        "lastPaymentDate": "2024-02-15T10:30:00Z",
        "verifiedBy": {
          "id": "admin_001",
          "name": "Admin User",
          "email": "<EMAIL>"
        }
      }
    ],
    "rawPayments": [
      {
        "id": "bt_payment_123",
        "paymentId": "pay_456",
        "amount": 149.99,
        "currency": "USD",
        "transactionId": "TXN123456",
        "bankHolderName": "John Doe",
        "accountNumber": "****1234",
        "ifscCode": "BANK0001234",
        "bankName": "Example Bank",
        "status": "VERIFIED",
        "verifiedAt": "2024-01-15T12:00:00Z",
        "user": { /* user details */ },
        "package": { /* package details */ },
        "subscription": { /* subscription details */ },
        "payment": { /* payment details */ },
        "verifiedBy": { /* admin details */ }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3,
      "totalUniqueUsers": 20
    }
  }
}
```

### Response Fields

#### verifiedUsers Array
- **user**: Complete user information
- **totalPayments**: Number of verified payments by this user
- **totalAmount**: Sum of all verified payment amounts
- **packages**: Array of packages purchased
- **subscriptions**: Array of active/past subscriptions
- **firstPaymentDate**: Date of first verified payment
- **lastPaymentDate**: Date of most recent verified payment
- **verifiedBy**: Admin who verified the payments

#### rawPayments Array
- Complete bank transfer payment records
- Includes all related data (user, package, subscription, payment)
- Useful for detailed analysis

#### pagination Object
- **page**: Current page number
- **limit**: Records per page
- **total**: Total verified payments
- **totalPages**: Total number of pages
- **totalUniqueUsers**: Number of unique users with verified payments

### Use Cases

#### 1. Admin Dashboard
```javascript
// Get verified users for admin dashboard
const response = await fetch('/api/bank-transfer/admin/verified-users?page=1&limit=20', {
  headers: {
    'Authorization': `Bearer ${adminToken}`
  }
});

const { verifiedUsers, pagination } = response.data;

// Display user list with payment summary
verifiedUsers.forEach(userSummary => {
  console.log(`${userSummary.user.name}: ${userSummary.totalPayments} payments, $${userSummary.totalAmount}`);
});
```

#### 2. Customer Analytics
```javascript
// Analyze customer payment patterns
const verifiedUsers = response.data.verifiedUsers;

const highValueCustomers = verifiedUsers.filter(user => user.totalAmount > 500);
const repeatCustomers = verifiedUsers.filter(user => user.totalPayments > 1);
const recentCustomers = verifiedUsers.filter(user => 
  new Date(user.lastPaymentDate) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
);
```

#### 3. Revenue Reporting
```javascript
// Calculate total revenue from bank transfers
const totalRevenue = verifiedUsers.reduce((sum, user) => sum + user.totalAmount, 0);
const averagePaymentValue = totalRevenue / pagination.total;
```

### Error Responses

#### 401 Unauthorized
```json
{
  "success": false,
  "message": "User authentication required"
}
```

#### 403 Forbidden
```json
{
  "success": false,
  "message": "Access denied. Admin access required."
}
```

#### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Failed to get verified bank transfer users"
}
```

### Features

#### Data Aggregation
- **User Deduplication**: Groups payments by user to avoid duplicates
- **Payment Summary**: Calculates totals and counts per user
- **Date Tracking**: Tracks first and last payment dates
- **Package History**: Shows all packages purchased by user

#### Performance Optimization
- **Pagination**: Efficient data retrieval for large datasets
- **Selective Fields**: Only fetches necessary user data
- **Indexed Queries**: Uses database indexes for fast retrieval
- **Optimized Joins**: Efficient database joins for related data

#### Security
- **Admin Only**: Restricted to admin users
- **Data Sanitization**: Sensitive data like full account numbers are masked
- **Audit Trail**: Includes verification details for transparency

### Frontend Integration

#### React Component Example
```jsx
import React, { useState, useEffect } from 'react';

const VerifiedUsersTable = () => {
  const [users, setUsers] = useState([]);
  const [pagination, setPagination] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchVerifiedUsers(1);
  }, []);

  const fetchVerifiedUsers = async (page) => {
    try {
      const response = await fetch(`/api/bank-transfer/admin/verified-users?page=${page}&limit=10`, {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });
      const data = await response.json();
      
      setUsers(data.data.verifiedUsers);
      setPagination(data.data.pagination);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching verified users:', error);
      setLoading(false);
    }
  };

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      <h2>Verified Bank Transfer Users ({pagination.totalUniqueUsers})</h2>
      <table>
        <thead>
          <tr>
            <th>User</th>
            <th>Email</th>
            <th>Total Payments</th>
            <th>Total Amount</th>
            <th>Last Payment</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
          {users.map(userSummary => (
            <tr key={userSummary.user.id}>
              <td>{userSummary.user.name} {userSummary.user.lastname}</td>
              <td>{userSummary.user.email}</td>
              <td>{userSummary.totalPayments}</td>
              <td>${userSummary.totalAmount}</td>
              <td>{new Date(userSummary.lastPaymentDate).toLocaleDateString()}</td>
              <td>
                <span className="status-verified">Verified</span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      
      {/* Pagination Component */}
      <div className="pagination">
        {Array.from({ length: pagination.totalPages }, (_, i) => (
          <button 
            key={i + 1}
            onClick={() => fetchVerifiedUsers(i + 1)}
            className={pagination.page === i + 1 ? 'active' : ''}
          >
            {i + 1}
          </button>
        ))}
      </div>
    </div>
  );
};

export default VerifiedUsersTable;
```

### Testing

#### Test the endpoint
```bash
# Test with curl
curl -X GET "http://localhost:3000/api/bank-transfer/admin/verified-users?page=1&limit=5" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Test with the provided test script
node test-bank-transfer.js
```

The test script includes a specific test for this endpoint that will:
1. Fetch verified users list
2. Display the number of users retrieved
3. Show pagination information
4. Verify the response structure

This endpoint provides comprehensive user management capabilities for administrators to track and analyze verified bank transfer customers.