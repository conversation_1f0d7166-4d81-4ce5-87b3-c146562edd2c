import axios, { AxiosInstance, InternalAxiosRequestConfig } from "axios";
import dotenv from "dotenv";

dotenv.config();

const ZOHO_DESK_CLIENT_ID = process.env.ZOHO_DESK_CLIENT_ID!;
const ZOHO_DESK_CLIENT_SECRET = process.env.ZOHO_DESK_CLIENT_SECRET!;
const ZOHO_DESK_REFRESH_TOKEN = process.env.ZOHO_DESK_REFRESH_TOKEN!;
const ZOHO_DESK_ORG_ID = process.env.ZOHO_DESK_ORG_ID!;
const ZOHO_DESK_API_DOMAIN = process.env.ZOHO_DESK_API_DOMAIN || "https://desk.zoho.in";
const ZOHO_DESK_AUTH_URL = process.env.ZOHO_DESK_AUTH_URL || "https://accounts.zoho.in/oauth/v2";

interface TokenCache {
  token: string | null;
  expiresAt: number | null;
}

let tokenCache: TokenCache = {
  token: null,
  expiresAt: null,
};

async function fetchZohoDeskAccessToken(): Promise<string> {
  try {
    const response = await axios.post(
      `${ZOHO_DESK_AUTH_URL}/token`,
      null,
      {
        params: {
          refresh_token: ZOHO_DESK_REFRESH_TOKEN,
          client_id: ZOHO_DESK_CLIENT_ID,
          client_secret: ZOHO_DESK_CLIENT_SECRET,
          grant_type: "refresh_token",
        },
      }
    );

    const { access_token, expires_in } = response.data;

    tokenCache.token = access_token;
    tokenCache.expiresAt = Date.now() + expires_in * 1000 - 60000; // Refresh 60s early
    
    console.log("Zoho Desk token refreshed successfully");
    return tokenCache.token!;
  } catch (error) {
    console.error("Failed to fetch Zoho Desk access token:", error);
    throw new Error("Failed to authenticate with Zoho Desk");
  }
}

const zohoDeskClient: AxiosInstance = axios.create({
  baseURL: `${ZOHO_DESK_API_DOMAIN}/api/v1`,
  headers: {
    "Content-Type": "application/json",
    "orgId": ZOHO_DESK_ORG_ID,
  },
  timeout: 30000,
});

// Log requests for debugging
zohoDeskClient.interceptors.request.use(request => {
  console.log('Zoho Desk API Request:', {
    url: request.url,
    method: request.method,
    data: request.data,
    headers: {
      ...request.headers,
      // Don't log the full token for security reasons
      Authorization: request.headers.Authorization ? 
        request.headers.Authorization.toString().substring(0, 20) + '...' : 
        undefined
    }
  });
  return request;
});

zohoDeskClient.interceptors.request.use(
  async (config: InternalAxiosRequestConfig): Promise<InternalAxiosRequestConfig> => {
    // Check if token is missing or expired
    if (!tokenCache.token || !tokenCache.expiresAt || Date.now() >= tokenCache.expiresAt) {
      await fetchZohoDeskAccessToken();
    }

    // Set the authorization header
    config.headers.set("Authorization", `Zoho-oauthtoken ${tokenCache.token}`);
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor to handle token expiration
zohoDeskClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    // If we get a 401 and haven't already retried
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        // Force refresh the token
        tokenCache.token = null;
        tokenCache.expiresAt = null;
        await fetchZohoDeskAccessToken();
        
        // Retry the original request with new token
        originalRequest.headers.set("Authorization", `Zoho-oauthtoken ${tokenCache.token}`);
        return zohoDeskClient(originalRequest);
      } catch (refreshError) {
        console.error("Token refresh failed:", refreshError);
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

export default zohoDeskClient;