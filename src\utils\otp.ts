import bcrypt from "bcrypt";

/**
 * Generates a 4-digit OTP, hashes it, and sets an expiration time.
 * @returns {Promise<{ otp: string, hashedOtp: string, otpExpiry: Date }>}
 */
export async function generateOtp() {
  const otp = Math.floor(1000 + Math.random() * 9000).toString(); // Ensures 4-digit OTP
  //   const hashedOtp = await bcrypt.hash(otp, 10);
  const otpExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

  return { otp, otpExpiry };
}
