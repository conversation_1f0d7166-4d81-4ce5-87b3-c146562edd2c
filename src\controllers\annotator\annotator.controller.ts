import { Request, Response } from "express";
import { Role } from "@prisma/client";
import { annotatorService } from "../../services/annotator/annotator.service";
import { JwtPayload } from "jsonwebtoken";
import { AppError } from "../../utils/ApiError";
import { errorResponse } from "../../helper/apiResponse";

interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}

export class AnnotatorController {
  async createAnnotator(
    req: AuthenticatedRequest,
    res: Response
  ): Promise<void> {
    console.log(req.body, "Request Body");
    const loggedInUser = req.user;

    if (!loggedInUser || loggedInUser.userRole !== Role.ADMIN) {
      res.status(403).json({ message: "Only admins can create annotators" });
      return; // <-- important: stop here!
    }

    const { name, email, password } = req.body;

    if (!name || !email || !password) {
      res
        .status(400)
        .json({ message: "name, email, and password are required" });
      return;
    }

    const annotator = await annotatorService.createAnnotator({
      name,
      email,
      password,
    });

    res.status(201).json({
      message: "Annotator created successfully",
      user: {
        id: annotator.id,
        name: annotator.name,
        email: annotator.email,
        role: annotator.role,
        createdAt: annotator.createdAt,
      },
    });
  }

  async getAnnotator(req: AuthenticatedRequest, res: Response): Promise<void> {
    const loggedInUser = req.user;

    if (!loggedInUser || loggedInUser.userRole !== Role.ADMIN) {
      res.status(403).json({ message: "Only admins can view annotators" });
      return; // <-- important: stop here!
    }

    const annotators = await annotatorService.getAnnotator(req.query);

    res.status(200).json({
      message: "Annotators fetched successfully",
      data: annotators,
    });
  }

  async getCoordinator(
    req: AuthenticatedRequest,
    res: Response
  ): Promise<void> {
    const loggedInUser = req.user;

    if (!loggedInUser || loggedInUser.userRole !== Role.ADMIN) {
      throw new AppError("Only admins can view annotators", 403); // <-- important: stop here!
    }
    const annotators = await annotatorService.getAllCoordinator(req.query);
    res.status(200).json({
      message: "Annotators fetched successfully",
      data: annotators,
    });
  }
  async getAnnotatorById(
    req: AuthenticatedRequest,
    res: Response
  ): Promise<void> {
    const loggedInUser = req.user;

    if (!loggedInUser || loggedInUser.userRole !== Role.ADMIN) {
      res.status(403).json({ message: "Only admins can view annotators" });
      return; // <-- important: stop here!
    }

    const { id } = req.params;

    if (!id) {
      res.status(400).json({ message: "Annotator ID is required" });
      return;
    }

    const annotator = await annotatorService.getAnnotatorById(id);

    if (!annotator) {
      res.status(404).json({ message: "Annotator not found" });
      return;
    }

    res.status(200).json({
      message: "Annotator fetched successfully",
      annotator,
    });
  }
  async updateAnnotator(
    req: AuthenticatedRequest,
    res: Response
  ): Promise<void> {
    const loggedInUser = req.user;

    if (!loggedInUser || loggedInUser.userRole !== Role.ADMIN) {
      res.status(403).json({ message: "Only admins can update annotators" });
      return; // <-- important: stop here!
    }

    const { id } = req.params;

    if (!id) {
      res.status(400).json({ message: "Annotator ID is required" });
      return;
    }

    const { name, email, password } = req.body;

    if (!name && !email && !password) {
      res.status(400).json({
        message: "At least one field (name, email, password) is required",
      });
      return;
    }

    const updatedAnnotator = await annotatorService.updateAnnotator(id, {
      name,
      email,
      password,
    });

    if (!updatedAnnotator) {
      res.status(404).json({ message: "Annotator not found" });
      return;
    }

    res.status(200).json({
      message: "Annotator updated successfully",
      user: {
        id: updatedAnnotator.id,
        name: updatedAnnotator.name,
        email: updatedAnnotator.email,
        role: updatedAnnotator.role,
        createdAt: updatedAnnotator.createdAt,
      },
    });
  }
  async deleteAnnotator(
    req: AuthenticatedRequest,
    res: Response
  ): Promise<void> {
    const loggedInUser = req.user;

    if (!loggedInUser || loggedInUser.userRole !== Role.ADMIN) {
      res.status(403).json({ message: "Only admins can delete annotators" });
      return; // <-- important: stop here!
    }

    const { id } = req.params;

    if (!id) {
      res.status(400).json({ message: "Annotator ID is required" });
      return;
    }

    try {
      const deletionResult = await annotatorService.deleteAnnotator(id);

      if (!deletionResult) {
        res.status(404).json({ message: "User not found" });
        return;
      }

      res.status(200).json({
        message: deletionResult.message,
        deletionType: deletionResult.deletionType,
        relationshipDetails: deletionResult.relationshipDetails,
        user: {
          id: deletionResult.id,
          name: deletionResult.name,
          email: deletionResult.email,
          role: deletionResult.role,
          isDeleted: deletionResult.isDeleted,
          accountStatus: deletionResult.accountStatus,
          createdAt: deletionResult.createdAt,
        },
      });
    } catch (error) {
      res.status(400).json({ 
        message: error instanceof Error ? error.message : "An error occurred while deleting the user" 
      });
    }
  }

  async getClients(req: AuthenticatedRequest, res: Response): Promise<void> {
    const loggedInUser = req.user?.userId;

    const clients = await annotatorService.getCoordinatorClients(
      req.query,
      loggedInUser
    );
    res.status(200).json({
      message: "Clients fetched successfully",
      data: clients,
    });
  }

  async getCoordinatorProjects(req: AuthenticatedRequest, res: Response) {
    try {
      const coordinatorId = req.user?.id;
  
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
  
      const result = await annotatorService.getProjectsForCoordinator(coordinatorId, page, limit);
  
      return res.status(200).json({ success: true, ...result });
    } catch (error) {
      console.error(error);
      return res.status(500).json({ success: false, message: 'Internal server error' });
    }
  };

  async getClientsWithSubscriptions  (req: Request, res: Response) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
  
      const result = await annotatorService.getClientsWithSubscriptions(page, limit);
  
      res.status(200).json(result);
    } catch (error) {
      console.error('Error getting clients with subscriptions:', error);
      res.status(500).json({ message: 'Internal Server Error' });
    }
  };

  async getClientAnnotators(
    req: AuthenticatedRequest,
    res: Response
  ): Promise<void> {
    const loggedInUser = req.user?.userId;

    const clients = await annotatorService.getClientsAnnotators(
      req.query,
      loggedInUser
    );
    res.status(200).json({
      message: "Clients fetched successfully",
      data: clients,
    });
  }

  async getAllAnnotator(req: AuthenticatedRequest, res: Response) {
    const annotators = await annotatorService.getAllAssignedAnnotators(
      req.query
    );
    res.status(200).json({
      message: "Annotators fetched successfully",
      data: annotators,
    });
  }
  async getAllCoordinator(req: AuthenticatedRequest, res: Response) {
    const annotators = await annotatorService.getAllAssignedCoordinator(req.query);
    res.status(200).json({
      message: "Coordinators fetched successfully",
      data: annotators,
    });
  }

  async getCoordinatorClients(req: AuthenticatedRequest, res: Response) {
    const annotators = await annotatorService.getAllCoordinatorClients(req.query);
    res.status(200).json({
      message: "Coordinators fetched successfully",
      data: annotators,
    });
  }


  async getAnnotatorProjects(req: Request, res: Response): Promise<void> {
  const { annotatorId } = req.params;

  const projects = await annotatorService.getAnnotatorProjects(annotatorId);

  res.status(200).json({
    message: "Annotator projects fetched successfully",
    data: projects,
  });
}

async getCoordinatorClientProjects(req: AuthenticatedRequest, res: Response) {
  const { clientId } = req.params;
  const loggedInUserId = req.user?.userId;

  if (!clientId) {
    return errorResponse(res, "Client ID is required.");
  }

  try {
    const projects = await annotatorService.getCoordinatorClientProjects(
      clientId,
      loggedInUserId
    );

    return res.status(200).json({
      message: "Coordinator client's projects fetched successfully",
      data: projects,
    });
  } catch (error) {
    console.error(error, "dtfghjklhgfdsfghjk");
     return res.status(403).json({
      success: false,
      message: error instanceof Error ? error.message : "Something went wrong",
    });
  }
}


async updateDescription(req: AuthenticatedRequest, res: Response): Promise<void> {
  const userId = req.user?.userId;
  const { description } = req.body;

  if (!userId) {
    res.status(401).json({ message: "Unauthorized" });
    return;
  }

  if (typeof description !== "string") {
    res.status(400).json({ message: "Invalid description" });
    return;
  }

  const result = await annotatorService.updateDescription(userId, description);

  res.status(200).json({
    message: "Description updated successfully",
    data: result,
  });
}

}


