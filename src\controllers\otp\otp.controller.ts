import { Request, Response } from "express";
import { UserCommunicationService } from "../../services/email/user-communication.service";
import prisma from "../../prisma";
import { ApiResponse } from "../../helper/apiResponse";
import catchAsync from "../../utils/catchAsync";
import { JwtPayload } from "jsonwebtoken";
interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}
export class OTPController {
  private userCommunicationService: UserCommunicationService;

  constructor() {
    this.userCommunicationService = new UserCommunicationService();
  }

  /**
   * Send OTP for email verification
   */
  sendVerificationOTP = catchAsync(
    async (req: Request, res: Response): Promise<void> => {
      const { email, type = "verification" } = req.body;

      if (!email) {
        // res.status(400).json(ApiResponse.error("Email is required", 400));
        return;
      }

      // Generate OTP
      const otpCode = this.userCommunicationService.generateOTP(6);
      const expiryTime = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

      // Check if user exists
      const user = await prisma.user.findUnique({
        where: { email },
      });

      if (!user && type === "verification") {
        // res.status(404).json(ApiResponse.error("User not found", 404));
        return;
      }

      // Store OTP in database
      if (user) {
        await prisma.user.update({
          where: { email },
          data: {
            otpCode,
            otpExpiry: expiryTime,
          },
        });
      }

      // Prepare email data
      const emailData = {
        customerName: user?.name || "User",
        otpCode,
        timeLimit: 10,
        companyName: process.env.COMPANY_NAME || "Your Company",
        supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
        phoneNumber: process.env.SUPPORT_PHONE || "******-567-8900",
        website: process.env.WEBSITE_URL || "https://company.com",
      };

      // Send OTP email
      await this.userCommunicationService.sendOTPVerification(email, emailData);

      // res.status(200).json(
      //   ApiResponse.success("OTP sent successfully", {
      //     message: "OTP has been sent to your email address",
      //     expiresIn: "10 minutes",
      //   })
      // );
    }
  );

  /**
   * Send OTP for signup verification
   */
  sendSignupOTP = catchAsync(async (req: Request, res: Response) => {
    const { email, firstName } = req.body;

    if (!email || !firstName) {
      // return res
      //   .status(400)
      //   .json(ApiResponse.error("Email and first name are required", 400));
    }

    // Generate OTP
    const otpCode = this.userCommunicationService.generateOTP(6);
    const expiryTime = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

    // Store OTP temporarily (you might want to use Redis or a temporary table)
    // For now, we'll store it in the user table if user exists, or create a temporary record
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      await prisma.user.update({
        where: { email },
        data: {
          otpCode,
          otpExpiry: expiryTime,
        },
      });
    } else {
      // Create temporary user record for signup process
      await prisma.user.create({
        data: {
          email,
          name: firstName,
          otpCode,
          otpExpiry: expiryTime,
          emailVerified: null, // Not verified yet
        },
      });
    }

    // Prepare email data
    const emailData = {
      firstName,
      otpCode,
      timeLimit: 15,
      companyName: process.env.COMPANY_NAME || "Your Company",
      supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
      phoneNumber: process.env.SUPPORT_PHONE || "******-567-8900",
      websiteUrl: process.env.WEBSITE_URL || "https://company.com",
    };

    // Send signup OTP email
    await this.userCommunicationService.sendSignupOTP(email, emailData);

    // res.status(200).json(
    //   ApiResponse.success("Signup OTP sent successfully", {
    //     message: "Please check your email for the verification code",
    //     expiresIn: "15 minutes",
    //   })
    // );
  });

  /**
   * Verify OTP
   */
  verifyOTP = catchAsync(async (req: Request, res: Response) => {
    const { email, otpCode } = req.body;

    if (!email || !otpCode) {
      // return res
      //   .status(400)
      //   .json(ApiResponse.error("Email and OTP code are required", 400));
    }

    // Find user with matching OTP
    const user = await prisma.user.findFirst({
      where: {
        email,
        otpCode,
        otpExpiry: {
          gt: new Date(), // OTP not expired
        },
      },
    });

    if (!user) {
      return res
        .status(400)
    }

    // Clear OTP and mark email as verified
    await prisma.user.update({
      where: { id: user.id },
      data: {
        otpCode: null,
        otpExpiry: null,
        emailVerified: new Date(),
      },
    });

    // res.status(200).json(
    //   ApiResponse.success("OTP verified successfully", {
    //     message: "Email verified successfully",
    //     userId: user.id,
    //     verified: true,
    //   })
    // );
  });

  /**
   * Resend OTP
   */
  resendOTP = catchAsync(async (req: Request, res: Response) => {
    const { email, type = "verification" } = req.body;

    if (!email) {
      // return res.status(400).json(ApiResponse.error("Email is required", 400));
    }

    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      return res.status(404)
    }

    // Check if last OTP was sent recently (prevent spam)
    if (
      user.otpExpiry &&
      user.otpExpiry > new Date(Date.now() - 2 * 60 * 1000)
    ) {
      // return res
      //   .status(429)
      //   .json(
      //     ApiResponse.error("Please wait before requesting a new OTP", 429)
      //   );
    }

    // Generate new OTP
    const otpCode = this.userCommunicationService.generateOTP(6);
    const expiryTime = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Update user with new OTP
    await prisma.user.update({
      where: { email },
      data: {
        otpCode,
        otpExpiry: expiryTime,
      },
    });

    // Prepare email data based on type
    if (type === "signup") {
      const emailData = {
        firstName: user.name,
        otpCode,
        timeLimit: 10,
        companyName: process.env.COMPANY_NAME || "Your Company",
        supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
        phoneNumber: process.env.SUPPORT_PHONE || "******-567-8900",
        websiteUrl: process.env.WEBSITE_URL || "https://company.com",
      };
      await this.userCommunicationService.sendSignupOTP(email, emailData);
    } else {
      const emailData = {
        customerName: user.name,
        otpCode,
        timeLimit: 10,
        companyName: process.env.COMPANY_NAME || "Your Company",
        supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
        phoneNumber: process.env.SUPPORT_PHONE || "******-567-8900",
        website: process.env.WEBSITE_URL || "https://company.com",
      };
      await this.userCommunicationService.sendOTPVerification(email, emailData);
    }

    // res.status(200).json(
    //   ApiResponse.success("OTP resent successfully", {
    //     message: "A new OTP has been sent to your email address",
    //     expiresIn: "10 minutes",
    //   })
    // );
  });

  /**
   * Send coworker invitation
   */
  sendCoworkerInvitation = catchAsync(
    async (req: AuthenticatedRequest, res: Response) => {
      const {
        coworkerEmail,
        coworkerName,
        role = "Coworker",
        invitationToken,
      } = req.body;

      // Get client info from authenticated user
      const clientId = req.user?.id;
      if (!clientId) {
        return res
          .status(401)
          // .json(ApiResponse.error("Authentication required", 401));
      }

      const client = await prisma.user.findUnique({
        where: { id: clientId },
      });

      if (!client) {
        return res.status(404)
      }

      // Generate invitation link
      const baseUrl = process.env.FRONTEND_URL || "https://app.company.com";
      const invitationLink = `${baseUrl}/accept-invitation?token=${invitationToken}&email=${encodeURIComponent(
        coworkerEmail
      )}`;

      // Prepare email data
      const emailData = {
        coworkerName,
        clientName: client.name,
        platformName: process.env.PLATFORM_NAME || "Our Platform",
        role,
        invitationLink,
        companyName: process.env.COMPANY_NAME || "Your Company",
        supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
        websiteUrl: process.env.WEBSITE_URL || "https://company.com",
      };

      // Send invitation email
      await this.userCommunicationService.sendCoworkerInvitation(
        coworkerEmail,
        emailData
      );

      // res.status(200).json(
      //   ApiResponse.success("Invitation sent successfully", {
      //     message: "Coworker invitation has been sent",
      //     invitationLink,
      //   })
      // );
    }
  );
}

export default OTPController;
