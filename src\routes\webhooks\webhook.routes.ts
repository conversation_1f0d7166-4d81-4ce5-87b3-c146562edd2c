import express, { Request, Response, Router } from "express";
import { DodoWebhookController } from "../../controllers/webhook/webhook.controller";
import { PayPalWebhookController } from "../../controllers/webhook/paypal.controller";

export class WebhookRoute {
  public router: Router;
  private controller: DodoWebhookController;
  private paypalController: PayPalWebhookController;

  constructor() {
    this.router = Router();
    this.controller = new DodoWebhookController();
    this.paypalController = new PayPalWebhookController();
    this.initializeRoutes();
  }

  private initializeRoutes(): void {
    // Webhook expects raw body for signature verification
    this.router.post(
      "/dodo",
      express.raw({ type: "application/json" }),
      this.controller.handleWebhook.bind(this.controller)
    );

    // PayPal webhook - expects JSON body
    this.router.post(
      "/paypal",
      express.json({ type: "application/json" }),
      this.paypalController.handleWebhook.bind(this.paypalController)
    );

    this.router.post(
      "/register",
      this.paypalController.registerWebhook.bind(this.paypalController)
    );

    this.router.delete(
      "/delete/webook",
      this.paypalController.deleteWebHook.bind(this.paypalController)
    )
  }
}
