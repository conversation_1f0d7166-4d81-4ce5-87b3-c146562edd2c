import { Request, Response, NextFunction } from "express";
import { errorResponse } from "../helper/apiResponse";
import { constant } from "./middleware.constant";
import { JwtPayload } from "jsonwebtoken";

const { Auth_error } = constant;

interface AuthenticatedRequest extends Request {
  user?: { userRole?: string };
}

export const hasRole = (role: string) => {
  return (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): void => {
    console.log(req.user, "req.user");
    if (req.user?.userRole !== role) {
      console.log(req.user, "role");
      errorResponse(res, Auth_error);
      return;
    }
    next();
  };
};

export const hasMultipleRole = (allowedRoles: string[]) => {
  return (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): void => {
    console.log("User Data in Middleware:", req.user);

   const userRole = req.user?.userRole;
    console.log("Extracted Role:", userRole);

    if (!userRole || !allowedRoles.includes(userRole)) {
      console.log("Unauthorized Role:", userRole);
      errorResponse(res, Auth_error);
      return;
    }

    next();
  };
};
