// // Email logging and analytics models
// enum EmailStatus {
//   SENT
//   DELIVERED
//   OPENED
//   CLICKED
//   FAILED
//   BOUNCED
// }

// model EmailLog {
//   id              String      @id @default(cuid())
//   to              String
//   cc              String?
//   bcc             String?
//   subject         String
//   template        String?
//   status          EmailStatus @default(SENT)
//   sentAt          DateTime    @default(now())
//   deliveredAt     DateTime?
//   openedAt        DateTime?
//   clickedAt       DateTime?
//   failureReason   String?
//   metadata        Json?
//   userId          String?
//   user            User?       @relation(fields: [userId], references: [id], onDelete: SetNull)
//   createdAt       DateTime    @default(now())
//   updatedAt       DateTime    @updatedAt

//   @@index([userId])
//   @@index([template])
//   @@index([status])
//   @@index([sentAt])
// }

// model EmailTemplateUsage {
//   id           String   @id @default(cuid())
//   templateName String   @unique
//   usageCount   Int      @default(1)
//   lastUsed     DateTime @default(now())
//   createdAt    DateTime @default(now())
//   updatedAt    DateTime @updatedAt
// }

// model EmailQueue {
//   id           String      @id @default(cuid())
//   to           String
//   cc           String?
//   bcc          String?
//   subject      String
//   templateName String?
//   templateData Json
//   priority     Int         @default(5)
//   status       String      @default("pending")
//   scheduledFor DateTime?
//   attempts     Int         @default(0)
//   maxAttempts  Int         @default(3)
//   errorMessage String?
//   createdAt    DateTime    @default(now())
//   updatedAt    DateTime    @updatedAt
//   processedAt  DateTime?

//   @@index([status])
//   @@index([scheduledFor])
//   @@index([priority])
// }