// import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
// import { EmailIntegrationService } from '../../services/email/email-integration.service';
// import { EnhancedEmailService } from '../../services/email/enhanced-email.service';
// import { UserCommunicationService } from '../../services/email/user-communication.service';

// // Mock the dependencies
// jest.mock('../../services/email/enhanced-email.service');
// jest.mock('../../services/email/user-communication.service');

// const MockedEnhancedEmailService = EnhancedEmailService as jest.MockedClass<typeof EnhancedEmailService>;
// const MockedUserCommunicationService = UserCommunicationService as jest.MockedClass<typeof UserCommunicationService>;

// describe('EmailIntegrationService', () => {
//   let emailIntegrationService: EmailIntegrationService;
//   let mockEnhancedEmailService: jest.Mocked<EnhancedEmailService>;
//   let mockUserCommunicationService: jest.Mocked<UserCommunicationService>;

//   beforeEach(() => {
//     jest.clearAllMocks();

//     // Mock EnhancedEmailService
//     mockEnhancedEmailService = {
//       sendEmail: jest.fn().mockResolvedValue('test-email-id'),
//       sendBulkEmails: jest.fn().mockResolvedValue(undefined),
//       testConnection: jest.fn().mockResolvedValue(true),
//       sendLegacyEmail: jest.fn().mockResolvedValue(true),
//     } as any;

//     MockedEnhancedEmailService.mockImplementation(() => mockEnhancedEmailService);

//     // Mock UserCommunicationService
//     mockUserCommunicationService = {
//       testConnection: jest.fn().mockResolvedValue(true),
//     } as any;

//     MockedUserCommunicationService.mockImplementation(() => mockUserCommunicationService);

//     emailIntegrationService = new EmailIntegrationService();
//   });

//   afterEach(() => {
//     jest.restoreAllMocks();
//   });

//   describe('Authentication & Onboarding Emails', () => {
//     describe('sendSignupOTP', () => {
//       it('should send signup OTP email with correct data', async () => {
//         const testData = {
//           firstName: 'John',
//           otpCode: '123456',
//           timeLimit: 10,
//         };

//         await emailIntegrationService.sendSignupOTP('<EMAIL>', testData);

//         expect(mockEnhancedEmailService.sendEmail).toHaveBeenCalledWith({
//           to: '<EMAIL>',
//           subject: expect.stringContaining('Verify Your Email'),
//           template: 'otp-signup',
//           data: expect.objectContaining({
//             firstName: 'John',
//             otpCode: '123456',
//             timeLimit: 10,
//             companyName: expect.any(String),
//             supportEmail: expect.any(String),
//             phoneNumber: expect.any(String),
//             websiteUrl: expect.any(String),
//           }),
//         });
//       });

//       it('should use default values for optional parameters', async () => {
//         const testData = {
//           firstName: 'John',
//           otpCode: '123456',
//         };

//         await emailIntegrationService.sendSignupOTP('<EMAIL>', testData);

//         const callArgs = mockEnhancedEmailService.sendEmail.mock.calls[0][0];
//         expect(callArgs.data.timeLimit).toBe(10); // default value
//       });
//     });

//     describe('sendVerificationOTP', () => {
//       it('should send verification OTP email', async () => {
//         const testData = {
//           customerName: 'John Doe',
//           otpCode: '654321',
//           purpose: 'Password Reset',
//         };

//         await emailIntegrationService.sendVerificationOTP('<EMAIL>', testData);

//         expect(mockEnhancedEmailService.sendEmail).toHaveBeenCalledWith({
//           to: '<EMAIL>',
//           subject: 'Password Reset - OTP Code',
//           template: 'otp-verification',
//           data: expect.objectContaining({
//             customerName: 'John Doe',
//             otpCode: '654321',
//             purpose: 'Password Reset',
//           }),
//         });
//       });
//     });

//     describe('sendPasswordCreationLink', () => {
//       it('should send password creation link email', async () => {
//         const testData = {
//           firstName: 'John',
//           passwordCreationLink: 'https://example.com/create-password?token=abc123',
//         };

//         await emailIntegrationService.sendPasswordCreationLink('<EMAIL>', testData);

//         expect(mockEnhancedEmailService.sendEmail).toHaveBeenCalledWith({
//           to: '<EMAIL>',
//           subject: expect.stringContaining('Create Your Password'),
//           template: 'password-creation-link',
//           data: expect.objectContaining({
//             firstName: 'John',
//             passwordCreationLink: 'https://example.com/create-password?token=abc123',
//             timeLimit: '1 hour', // default value
//           }),
//         });
//       });
//     });
//   });

//   describe('Account Management Emails', () => {
//     describe('sendAccountSuspended', () => {
//       it('should send account suspended email', async () => {
//         const testData = {
//           firstName: 'John',
//           suspensionReason: 'Policy violation',
//           suspensionDuration: '7 days',
//         };

//         await emailIntegrationService.sendAccountSuspended('<EMAIL>', testData);

//         expect(mockEnhancedEmailService.sendEmail).toHaveBeenCalledWith({
//           to: '<EMAIL>',
//           subject: expect.stringContaining('Account Suspended'),
//           template: 'account-suspended',
//           data: expect.objectContaining({
//             firstName: 'John',
//             suspensionReason: 'Policy violation',
//             suspensionDuration: '7 days',
//             suspensionDate: expect.any(String),
//           }),
//         });
//       });
//     });

//     describe('sendAccountReactivated', () => {
//       it('should send account reactivated email', async () => {
//         const testData = {
//           firstName: 'John',
//           loginLink: 'https://example.com/login',
//         };

//         await emailIntegrationService.sendAccountReactivated('<EMAIL>', testData);

//         expect(mockEnhancedEmailService.sendEmail).toHaveBeenCalledWith({
//           to: '<EMAIL>',
//           subject: 'Account Reactivated - Welcome Back!',
//           template: 'account-reactivated',
//           data: expect.objectContaining({
//             firstName: 'John',
//             loginLink: 'https://example.com/login',
//           }),
//         });
//       });
//     });

//     describe('sendPasswordChanged', () => {
//       it('should send password changed email', async () => {
//         const testData = {
//           firstName: 'John',
//           email: '<EMAIL>',
//           ipAddress: '***********',
//         };

//         await emailIntegrationService.sendPasswordChanged('<EMAIL>', testData);

//         expect(mockEnhancedEmailService.sendEmail).toHaveBeenCalledWith({
//           to: '<EMAIL>',
//           subject: expect.stringContaining('Password Changed Successfully'),
//           template: 'password-changed',
//           data: expect.objectContaining({
//             firstName: 'John',
//             email: '<EMAIL>',
//             ipAddress: '***********',
//             dateTime: expect.any(String),
//           }),
//         });
//       });
//     });
//   });

//   describe('Collaboration Emails', () => {
//     describe('sendCoworkerInvitation', () => {
//       it('should send coworker invitation email', async () => {
//         const testData = {
//           coworkerName: 'Jane Doe',
//           clientName: 'Acme Corp',
//           role: 'Editor',
//           invitationLink: 'https://example.com/invite?token=xyz789',
//         };

//         await emailIntegrationService.sendCoworkerInvitation('<EMAIL>', testData);

//         expect(mockEnhancedEmailService.sendEmail).toHaveBeenCalledWith({
//           to: '<EMAIL>',
//           subject: expect.stringContaining("You're invited to join Acme Corp"),
//           template: 'invite-coworker',
//           data: expect.objectContaining({
//             coworkerName: 'Jane Doe',
//             clientName: 'Acme Corp',
//             role: 'Editor',
//             invitationLink: 'https://example.com/invite?token=xyz789',
//           }),
//         });
//       });
//     });

//     describe('sendWelcomeEmail', () => {
//       it('should send welcome email', async () => {
//         const testData = {
//           firstName: 'John',
//           meetingLink: 'https://example.com/meeting',
//         };

//         await emailIntegrationService.sendWelcomeEmail('<EMAIL>', testData);

//         expect(mockEnhancedEmailService.sendEmail).toHaveBeenCalledWith({
//           to: '<EMAIL>',
//           subject: expect.stringContaining("Welcome"),
//           template: 'welcome-mail',
//           data: expect.objectContaining({
//             firstName: 'John',
//             meetingLink: 'https://example.com/meeting',
//           }),
//         });
//       });
//     });

//     describe('sendTeamAssignment', () => {
//       it('should send team assignment email', async () => {
//         const testData = {
//           firstName: 'John',
//           projectName: 'Data Annotation Project',
//           projectId: 'proj-123',
//           startDate: '2023-12-01',
//           annotatorName: 'Alice Smith',
//           annotatorInitials: 'AS',
//           annotatorExpertise: 'NLP',
//           annotatorEmail: '<EMAIL>',
//           coordinatorName: 'Bob Johnson',
//           coordinatorInitials: 'BJ',
//           coordinatorEmail: '<EMAIL>',
//         };

//         await emailIntegrationService.sendTeamAssignment('<EMAIL>', testData);

//         expect(mockEnhancedEmailService.sendEmail).toHaveBeenCalledWith({
//           to: '<EMAIL>',
//           subject: 'Your Team is Ready - Annotator and Coordinator Assigned',
//           template: 'team-assignment',
//           data: expect.objectContaining({
//             firstName: 'John',
//             projectName: 'Data Annotation Project',
//             annotatorName: 'Alice Smith',
//             coordinatorName: 'Bob Johnson',
//           }),
//         });
//       });
//     });
//   });

//   describe('Subscription & Billing Emails', () => {
//     describe('sendSubscriptionExpiring', () => {
//       it('should send subscription expiring email', async () => {
//         const testData = {
//           firstName: 'John',
//           daysUntilExpiry: 7,
//           packageName: 'Premium Plan',
//           endDate: '2023-12-31',
//         };

//         await emailIntegrationService.sendSubscriptionExpiring('<EMAIL>', testData);

//         expect(mockEnhancedEmailService.sendEmail).toHaveBeenCalledWith({
//           to: '<EMAIL>',
//           subject: 'Subscription Expiring Soon',
//           template: 'subscription-expiring',
//           data: expect.objectContaining({
//             firstName: 'John',
//             daysUntilExpiry: 7,
//             packageName: 'Premium Plan',
//             endDate: '2023-12-31',
//           }),
//         });
//       });
//     });

//     describe('sendSubscriptionExpired', () => {
//       it('should send subscription expired email', async () => {
//         const testData = {
//           firstName: 'John',
//           packageName: 'Premium Plan',
//           endDate: '2023-12-31',
//           expiredDays: 3,
//         };

//         await emailIntegrationService.sendSubscriptionExpired('<EMAIL>', testData);

//         expect(mockEnhancedEmailService.sendEmail).toHaveBeenCalledWith({
//           to: '<EMAIL>',
//           subject: 'Subscription Expired',
//           template: 'subscription-expired',
//           data: expect.objectContaining({
//             firstName: 'John',
//             packageName: 'Premium Plan',
//             expiredDays: 3,
//           }),
//         });
//       });
//     });

//     describe('sendUsageLimitWarning', () => {
//       it('should send usage limit warning email', async () => {
//         const testData = {
//           firstName: 'John',
//           resource: 'projects',
//           current: 45,
//           limit: 50,
//           percentage: 90,
//         };

//         await emailIntegrationService.sendUsageLimitWarning('<EMAIL>', testData);

//         expect(mockEnhancedEmailService.sendEmail).toHaveBeenCalledWith({
//           to: '<EMAIL>',
//           subject: 'Usage Limit Warning',
//           template: 'usage-limit-warning',
//           data: expect.objectContaining({
//             firstName: 'John',
//             resource: 'projects',
//             current: 45,
//             limit: 50,
//             percentage: 90,
//           }),
//         });
//       });
//     });
//   });

//   describe('Additional Templates', () => {
//     describe('sendPaymentSuccess', () => {
//       it('should send payment success email', async () => {
//         const testData = {
//           firstName: 'John',
//           transactionId: 'txn_123456',
//           packageName: 'Premium Plan',
//           paymentMethod: 'Credit Card',
//           paymentDate: '2023-12-01',
//           currency: 'USD',
//           amount: '99.99',
//         };

//         await emailIntegrationService.sendPaymentSuccess('<EMAIL>', testData);

//         expect(mockEnhancedEmailService.sendEmail).toHaveBeenCalledWith({
//           to: '<EMAIL>',
//           subject: 'Payment Successful - Thank You!',
//           template: 'payment-success',
//           data: expect.objectContaining({
//             firstName: 'John',
//             transactionId: 'txn_123456',
//             packageName: 'Premium Plan',
//             currency: 'USD',
//             amount: '99.99',
//           }),
//         });
//       });
//     });

//     describe('sendProjectCompletion', () => {
//       it('should send project completion email', async () => {
//         const testData = {
//           firstName: 'John',
//           projectName: 'Data Annotation Project',
//           startDate: '2023-11-01',
//           completionDate: '2023-12-01',
//           projectDuration: '30 days',
//           totalTasks: 1000,
//           totalHours: 120,
//           qualityScore: 95,
//           annotatorName: 'Alice Smith',
//           annotatorEmail: '<EMAIL>',
//           coordinatorName: 'Bob Johnson',
//           coordinatorEmail: '<EMAIL>',
//         };

//         await emailIntegrationService.sendProjectCompletion('<EMAIL>', testData);

//         expect(mockEnhancedEmailService.sendEmail).toHaveBeenCalledWith({
//           to: '<EMAIL>',
//           subject: expect.stringContaining('Project "Data Annotation Project" Completed'),
//           template: 'project-completion',
//           data: expect.objectContaining({
//             firstName: 'John',
//             projectName: 'Data Annotation Project',
//             totalTasks: 1000,
//             qualityScore: 95,
//           }),
//         });
//       });
//     });

//     describe('sendSystemMaintenance', () => {
//       it('should send system maintenance email', async () => {
//         const testData = {
//           firstName: 'John',
//           startTime: '2023-12-01 02:00 UTC',
//           endTime: '2023-12-01 06:00 UTC',
//           duration: '4 hours',
//           timezone: 'UTC',
//           impactDescription: 'Platform will be unavailable',
//         };

//         await emailIntegrationService.sendSystemMaintenance('<EMAIL>', testData);

//         expect(mockEnhancedEmailService.sendEmail).toHaveBeenCalledWith({
//           to: '<EMAIL>',
//           subject: 'Scheduled System Maintenance Notification',
//           template: 'system-maintenance',
//           data: expect.objectContaining({
//             firstName: 'John',
//             startTime: '2023-12-01 02:00 UTC',
//             duration: '4 hours',
//             impactDescription: 'Platform will be unavailable',
//           }),
//         });
//       });
//     });

//     describe('sendNewsletter', () => {
//       it('should send newsletter email', async () => {
//         const testData = {
//           firstName: 'John',
//           newsletterTitle: 'Monthly Update - December 2023',
//           newsletterDate: '2023-12-01',
//           introMessage: 'Welcome to our monthly newsletter!',
//         };

//         await emailIntegrationService.sendNewsletter('<EMAIL>', testData);

//         expect(mockEnhancedEmailService.sendEmail).toHaveBeenCalledWith({
//           to: '<EMAIL>',
//           subject: 'Monthly Update - December 2023',
//           template: 'newsletter',
//           data: expect.objectContaining({
//             firstName: 'John',
//             newsletterTitle: 'Monthly Update - December 2023',
//             introMessage: 'Welcome to our monthly newsletter!',
//           }),
//         });
//       });
//     });
//   });

//   describe('Utility Methods', () => {
//     describe('testConnection', () => {
//       it('should test email service connection', async () => {
//         const result = await emailIntegrationService.testConnection();

//         expect(result).toBe(true);
//         expect(mockEnhancedEmailService.testConnection).toHaveBeenCalled();
//       });
//     });

//     describe('sendBulkEmails', () => {
//       it('should send bulk emails', async () => {
//         const emails = [
//           {
//             to: '<EMAIL>',
//             subject: 'Email 1',
//             template: 'otp-verification',
//             data: { customerName: 'User 1' },
//           },
//           {
//             to: '<EMAIL>',
//             subject: 'Email 2',
//             template: 'otp-verification',
//             data: { customerName: 'User 2' },
//           },
//         ];

//         await emailIntegrationService.sendBulkEmails(emails);

//         expect(mockEnhancedEmailService.sendBulkEmails).toHaveBeenCalledWith(emails);
//       });
//     });

//     describe('sendLegacyEmail', () => {
//       it('should send legacy format email', async () => {
//         const legacyData = {
//           name: 'John Doe',
//           email: '<EMAIL>',
//           subject: 'Legacy Email',
//           body: '<p>Legacy email content</p>',
//         };

//         const result = await emailIntegrationService.sendLegacyEmail(legacyData);

//         expect(result).toBe(true);
//         expect(mockEnhancedEmailService.sendLegacyEmail).toHaveBeenCalledWith(legacyData);
//       });
//     });
//   });
// });