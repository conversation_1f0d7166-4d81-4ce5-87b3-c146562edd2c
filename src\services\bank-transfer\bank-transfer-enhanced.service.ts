// // import prisma from "../../prisma";
// // import { v4 as uuidv4 } from "uuid";
// // import { BankTransferStatus, SubscriptionStatus, PaymentStatus, Provider } from "@prisma/client";
// // import { EmailService } from "../email/email.service";

// // export interface BankTransferPaymentData {
// //   userId: string;
// //   packageId: string;
// //   amount: number;
// //   currency: string;
// //   transactionId: string;
// //   bankHolderName: string;
// //   accountNumber: string;
// //   ifscCode: string;
// //   bankName: string;
// //   screenshotUrl: string;
// // }

// // export interface BankTransferVerificationData {
// //   bankTransferPaymentId: string;
// //   status: BankTransferStatus;
// //   adminNotes?: string;
// //   verifiedById: string;
// // }

// // export class EnhancedBankTransferService {
// //   private emailService: EmailService;

// //   constructor() {
// //     this.emailService = new EmailService();
// //   }

// //   /**
// //    * Submit bank transfer payment details with Payment table integration
// //    */
// //   async submitBankTransferPayment(data: BankTransferPaymentData) {
// //     try {
// //       const paymentId = uuidv4();

// //       // Create pending subscription first
// //       const subscription = await prisma.subscription.create({
// //         data: {
// //           userId: data.userId,
// //           packageId: data.packageId,
// //           status: SubscriptionStatus.PENDING,
// //           startDate: new Date(),
// //           endDate: this.calculateEndDate(new Date()),
// //         },
// //       });

// //       // Create payment record in Payment table
// //       const payment = await prisma.payment.create({
// //         data: {
// //           paymentId,
// //           userId: data.userId,
// //           subscriptionId: subscription.id,
// //           amount: data.amount,
// //           currency: data.currency,
// //           paymentMethod: "bank_transfer",
// //           status: PaymentStatus.PENDING,
// //           provider: Provider.BANK_TRANSFER,
// //           rawResponse: {
// //             type: "bank_transfer_submission",
// //             transactionId: data.transactionId,
// //             bankHolderName: data.bankHolderName,
// //             accountNumber: data.accountNumber,
// //             ifscCode: data.ifscCode,
// //             bankName: data.bankName,
// //             screenshotUrl: data.screenshotUrl,
// //             submittedAt: new Date().toISOString(),
// //           },
// //         },
// //       });

// //       // Create detailed bank transfer record
// //       const bankTransferPayment = await prisma.bankTransferPayment.create({
// //         data: {
// //           paymentId: payment.paymentId, // Link to Payment table
// //           userId: data.userId,
// //           subscriptionId: subscription.id,
// //           packageId: data.packageId,
// //           amount: data.amount,
// //           currency: data.currency,
// //           transactionId: data.transactionId,
// //           bankHolderName: data.bankHolderName,
// //           accountNumber: data.accountNumber,
// //           ifscCode: data.ifscCode,
// //           bankName: data.bankName,
// //           screenshotUrl: data.screenshotUrl,
// //           status: BankTransferStatus.PENDING,
// //         },
// //         include: {
// //           user: true,
// //           package: true,
// //           payment: true,
// //         },
// //       });

// //       // Send confirmation email to user
// //       await this.sendPaymentSubmissionEmail(bankTransferPayment);

// //       // Send notification to admin
// //       await this.sendAdminNotificationEmail(bankTransferPayment);

// //       return {
// //         success: true,
// //         bankTransferPayment,
// //         payment,
// //         subscription,
// //         message: "Bank transfer payment details submitted successfully. Your payment is under review.",
// //       };
// //     } catch (error) {
// //       console.error("Error submitting bank transfer payment:", error);
// //       throw new Error("Failed to submit bank transfer payment");
// //     }
// //   }

// //   /**
// //    * Get pending bank transfer payments for admin review
// //    */
// //   async getPendingBankTransferPayments(page: number = 1, limit: number = 10) {
// //     try {
// //       const skip = (page - 1) * limit;

// //       const [payments, total] = await Promise.all([
// //         prisma.bankTransferPayment.findMany({
// //           where: { status: BankTransferStatus.PENDING },
// //           include: {
// //             user: {
// //               select: {
// //                 id: true,
// //                 name: true,
// //                 email: true,
// //               },
// //             },
// //             package: {
// //               select: {
// //                 id: true,
// //                 name: true,
// //                 price: true,
// //               },
// //             },
// //             subscription: {
// //               select: {
// //                 id: true,
// //                 status: true,
// //                 startDate: true,
// //                 endDate: true,
// //               },
// //             },
// //             payment: {
// //               select: {
// //                 id: true,
// //                 paymentId: true,
// //                 status: true,
// //                 createdAt: true,
// //               },
// //             },
// //           },
// //           orderBy: { createdAt: "desc" },
// //           skip,
// //           take: limit,
// //         }),
// //         prisma.bankTransferPayment.count({
// //           where: { status: BankTransferStatus.PENDING },
// //         }),
// //       ]);

// //       return {
// //         payments,
// //         pagination: {
// //           page,
// //           limit,
// //           total,
// //           totalPages: Math.ceil(total / limit),
// //         },
// //       };
// //     } catch (error) {
// //       console.error("Error getting pending bank transfer payments:", error);
// //       throw new Error("Failed to get pending bank transfer payments");
// //     }
// //   }

// //   /**
// //    * Verify bank transfer payment (admin only) - Updates both tables
// //    */
// //   async verifyBankTransferPayment(data: BankTransferVerificationData) {
// //     try {
// //       const bankTransferPayment = await prisma.bankTransferPayment.findUnique({
// //         where: { id: data.bankTransferPaymentId },
// //         include: {
// //           user: true,
// //           package: true,
// //           subscription: true,
// //           payment: true,
// //         },
// //       });

// //       if (!bankTransferPayment) {
// //         throw new Error("Bank transfer payment not found");
// //       }

// //       if (bankTransferPayment.status !== BankTransferStatus.PENDING) {
// //         throw new Error("Bank transfer payment has already been processed");
// //       }

// //       // Start transaction to update both tables
// //       const result = await prisma.$transaction(async (tx) => {
// //         // Update bank transfer payment status
// //         const updatedBankTransfer = await tx.bankTransferPayment.update({
// //           where: { id: data.bankTransferPaymentId },
// //           data: {
// //             status: data.status,
// //             adminNotes: data.adminNotes,
// //             verifiedAt: new Date(),
// //             verifiedById: data.verifiedById,
// //           },
// //           include: {
// //             user: true,
// //             package: true,
// //             subscription: true,
// //             payment: true,
// //           },
// //         });

// //         // Update payment table status
// //         const paymentStatus = data.status === BankTransferStatus.VERIFIED 
// //           ? PaymentStatus.SUCCESS 
// //           : PaymentStatus.FAILED;

// //         await tx.payment.update({
// //           where: { paymentId: updatedBankTransfer.paymentId },
// //           data: {
// //             status: paymentStatus,
// //             error_message: data.status === BankTransferStatus.REJECTED ? data.adminNotes : null,
// //             rawResponse: {
// //               ...bankTransferPayment.payment.rawResponse,
// //               verificationStatus: data.status,
// //               verifiedAt: new Date().toISOString(),
// //               verifiedById: data.verifiedById,
// //               adminNotes: data.adminNotes,
// //             },
// //           },
// //         });

// //         // Update subscription status
// //         if (data.status === BankTransferStatus.VERIFIED) {
// //           await tx.subscription.update({
// //             where: { id: updatedBankTransfer.subscription.id },
// //             data: {
// //               status: SubscriptionStatus.ACTIVE,
// //               startDate: new Date(),
// //             },
// //           });
// //         } else if (data.status === BankTransferStatus.REJECTED) {
// //           await tx.subscription.update({
// //             where: { id: updatedBankTransfer.subscription.id },
// //             data: { status: SubscriptionStatus.FAILED },
// //           });
// //         }

// //         return updatedBankTransfer;
// //       });

// //       // Send appropriate email notification
// //       if (data.status === BankTransferStatus.VERIFIED) {
// //         await this.sendPaymentVerifiedEmail(result);
// //       } else if (data.status === BankTransferStatus.REJECTED) {
// //         await this.sendPaymentRejectedEmail(result);
// //       }

// //       return {
// //         success: true,
// //         bankTransferPayment: result,
// //         message: `Bank transfer payment ${data.status.toLowerCase()} successfully`,
// //       };
// //     } catch (error) {
// //       console.error("Error verifying bank transfer payment:", error);
// //       throw new Error("Failed to verify bank transfer payment");
// //     }
// //   }

// //   /**
// //    * Get bank transfer payment details with Payment table data
// //    */
// //   async getBankTransferPaymentDetails(paymentId: string) {
// //     try {
// //       const payment = await prisma.bankTransferPayment.findUnique({
// //         where: { paymentId },
// //         include: {
// //           user: {
// //             select: {
// //               id: true,
// //               name: true,
// //               email: true,
// //             },
// //           },
// //           package: true,
// //           subscription: true,
// //           payment: true,
// //           verifiedBy: {
// //             select: {
// //               id: true,
// //               name: true,
// //               email: true,
// //             },
// //           },
// //         },
// //       });

// //       if (!payment) {
// //         throw new Error("Bank transfer payment not found");
// //       }

// //       return payment;
// //     } catch (error) {
// //       console.error("Error getting bank transfer payment details:", error);
// //       throw new Error("Failed to get bank transfer payment details");
// //     }
// //   }

// //   /**
// //    * Get user's bank transfer payments with Payment table data
// //    */
// //   async getUserBankTransferPayments(userId: string) {
// //     try {
// //       const payments = await prisma.bankTransferPayment.findMany({
// //         where: { userId },
// //         include: {
// //           package: {
// //             select: {
// //               id: true,
// //               name: true,
// //               price: true,
// //             },
// //           },
// //           subscription: {
// //             select: {
// //               id: true,
// //               status: true,
// //               startDate: true,
// //               endDate: true,
// //             },
// //           },
// //           payment: {
// //             select: {
// //               id: true,
// //               paymentId: true,
// //               status: true,
// //               createdAt: true,
// //               updatedAt: true,
// //             },
// //           },
// //         },
// //         orderBy: { createdAt: "desc" },
// //       });

// //       return payments;
// //     } catch (error) {
// //       console.error("Error getting user bank transfer payments:", error);
// //       throw new Error("Failed to get user bank transfer payments");
// //     }
// //   }

// //   /**
// //    * Get payment analytics for admin dashboard
// //    */
// //   async getBankTransferAnalytics() {
// //     try {
// //       const [totalPayments, pendingPayments, verifiedPayments, rejectedPayments, totalAmount] = await Promise.all([
// //         prisma.bankTransferPayment.count(),
// //         prisma.bankTransferPayment.count({ where: { status: BankTransferStatus.PENDING } }),
// //         prisma.bankTransferPayment.count({ where: { status: BankTransferStatus.VERIFIED } }),
// //         prisma.bankTransferPayment.count({ where: { status: BankTransferStatus.REJECTED } }),
// //         prisma.bankTransferPayment.aggregate({
// //           where: { status: BankTransferStatus.VERIFIED },
// //           _sum: { amount: true },
// //         }),
// //       ]);

// //       return {
// //         totalPayments,
// //         pendingPayments,
// //         verifiedPayments,
// //         rejectedPayments,
// //         totalVerifiedAmount: totalAmount._sum.amount || 0,
// //         verificationRate: totalPayments > 0 ? (verifiedPayments / totalPayments) * 100 : 0,
// //       };
// //     } catch (error) {
// //       console.error("Error getting bank transfer analytics:", error);
// //       throw new Error("Failed to get bank transfer analytics");
// //     }
// //   }

// //   private calculateEndDate(startDate: Date): Date {
// //     const endDate = new Date(startDate);
// //     endDate.setMonth(endDate.getMonth() + 1); // Default to 1 month
// //     return endDate;
// //   }

// //   private async sendPaymentSubmissionEmail(payment: any) {
// //     try {
// //       await this.emailService.sendEmail({
// //         to: payment.user.email,
// //         subject: "Bank Transfer Payment Submitted - Under Review",
// //         template: "bank-transfer",
// //         data: {
// //           userName: payment.user.name,
// //           packageName: payment.package.name,
// //           amount: payment.amount,
// //           currency: payment.currency,
// //           transactionId: payment.transactionId,
// //           paymentId: payment.paymentId,
// //           processingTime: "48-72 hours",
// //         },
// //       });
// //     } catch (error) {
// //       console.error("Error sending payment submission email:", error);
// //     }
// //   }

// //   private async sendAdminNotificationEmail(payment: any) {
// //     try {
// //       const admins = await prisma.user.findMany({
// //         where: { role: "ADMIN" },
// //         select: { email: true, name: true },
// //       });

// //       for (const admin of admins) {
// //         await this.emailService.sendEmail({
// //           to: admin.email,
// //           subject: "New Bank Transfer Payment Submitted for Review",
// //           template: "admin-bank-transfer-notification",
// //           data: {
// //             adminName: admin.name,
// //             userName: payment.user.name,
// //             userEmail: payment.user.email,
// //             packageName: payment.package.name,
// //             amount: payment.amount,
// //             currency: payment.currency,
// //             transactionId: payment.transactionId,
// //             paymentId: payment.paymentId,
// //             dashboardLink: `${process.env.FRONTEND_URL}/admin/bank-transfers`,
// //           },
// //         });
// //       }
// //     } catch (error) {
// //       console.error("Error sending admin notification email:", error);
// //     }
// //   }

// //   private async sendPaymentVerifiedEmail(payment: any) {
// //     try {
// //       await this.emailService.sendEmail({
// //         to: payment.user.email,
// //         subject: "Payment Verified - Subscription Activated",
// //         template: "payment-verified",
// //         data: {
// //           userName: payment.user.name,
// //           packageName: payment.package.name,
// //           amount: payment.amount,
// //           currency: payment.currency,
// //           transactionId: payment.transactionId,
// //           subscriptionStartDate: new Date().toLocaleDateString(),
// //           dashboardLink: `${process.env.FRONTEND_URL}/dashboard`,
// //         },
// //       });
// //     } catch (error) {
// //       console.error("Error sending payment verified email:", error);
// //     }
// //   }

// //   private async sendPaymentRejectedEmail(payment: any) {
// //     try {
// //       await this.emailService.sendEmail({
// //         to: payment.user.email,
// //         subject: "Payment Verification Failed",
// //         template: "payment-rejected",
// //         data: {
// //           userName: payment.user.name,
// //           packageName: payment.package.name,
// //           amount: payment.amount,
// //           currency: payment.currency,
// //           transactionId: payment.transactionId,
// //           adminNotes: payment.adminNotes || "Please contact support for more details.",
// //           supportEmail: "<EMAIL>",
// //         },
// //       });
// //     } catch (error) {
// //       console.error("Error sending payment rejected email:", error);
// //     }
// //   }
// // }
//   /**
//    * Get all verified bank transfer users (admin only)
//    */
//   async getVerifiedBankTransferUsers(page: number = 1, limit: number = 10) {
//     try {
//       const skip = (page - 1) * limit;

//       const [verifiedPayments, total] = await Promise.all([
//         prisma.bankTransferPayment.findMany({
//           where: { status: BankTransferStatus.VERIFIED },
//           include: {
//             user: {
//               select: {
//                 id: true,
//                 name: true,
//                 lastname: true,
//                 email: true,
//                 role: true,
//                 createdAt: true,
//               },
//             },
//             package: {
//               select: {
//                 id: true,
//                 name: true,
//                 price: true,
//                 billingType: true,
//               },
//             },
//             subscription: {
//               select: {
//                 id: true,
//                 status: true,
//                 startDate: true,
//                 endDate: true,
//               },
//             },
//             payment: {
//               select: {
//                 id: true,
//                 paymentId: true,
//                 amount: true,
//                 currency: true,
//                 status: true,
//                 createdAt: true,
//               },
//             },
//             verifiedBy: {
//               select: {
//                 id: true,
//                 name: true,
//                 email: true,
//               },
//             },
//           },
//           orderBy: { verifiedAt: "desc" },
//           skip,
//           take: limit,
//         }),
//         prisma.bankTransferPayment.count({
//           where: { status: BankTransferStatus.VERIFIED },
//         }),
//       ]);

//       // Group by user to avoid duplicates and get user summary
//       const userMap = new Map();
      
//       verifiedPayments.forEach(payment => {
//         const userId = payment.user.id;
//         if (!userMap.has(userId)) {
//           userMap.set(userId, {
//             user: payment.user,
//             totalPayments: 0,
//             totalAmount: 0,
//             packages: [],
//             subscriptions: [],
//             firstPaymentDate: payment.verifiedAt,
//             lastPaymentDate: payment.verifiedAt,
//             verifiedBy: payment.verifiedBy,
//           });
//         }
        
//         const userSummary = userMap.get(userId);
//         userSummary.totalPayments += 1;
//         userSummary.totalAmount += payment.amount;
//         userSummary.packages.push(payment.package);
//         userSummary.subscriptions.push(payment.subscription);
        
//         if (payment.verifiedAt < userSummary.firstPaymentDate) {
//           userSummary.firstPaymentDate = payment.verifiedAt;
//         }
//         if (payment.verifiedAt > userSummary.lastPaymentDate) {
//           userSummary.lastPaymentDate = payment.verifiedAt;
//         }
//       });

//       const verifiedUsers = Array.from(userMap.values());

//       return {
//         verifiedUsers,
//         rawPayments: verifiedPayments,
//         pagination: {
//           page,
//           limit,
//           total,
//           totalPages: Math.ceil(total / limit),
//           totalUniqueUsers: userMap.size,
//         },
//       };
//     } catch (error) {
//       console.error("Error getting verified bank transfer users:", error);
//       throw new Error("Failed to get verified bank transfer users");
//     }
//   }

