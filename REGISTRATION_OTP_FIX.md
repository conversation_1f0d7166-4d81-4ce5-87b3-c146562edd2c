# Registration OTP Issue Fix

## Problem Description

When a user signs up but doesn't complete OTP verification due to various reasons (distraction, lost email, expired OTP, etc.) and then tries to register again with the same email, the system would encounter database constraint errors and fail to complete the new registration.

### Root Causes

1. **Foreign Key Constraints**: The simple `user.delete()` operation failed because of related records in other tables
2. **External Service Records**: Dodo customer records weren't cleaned up, causing inconsistencies
3. **No Transaction Safety**: Partial deletions could leave the database in an inconsistent state
4. **Many-to-Many Relationships**: User relationships with tasks, projects, etc. weren't properly disconnected
5. **Incomplete Cleanup**: Various user-related records (sessions, tokens, notifications, etc.) weren't removed

## Solution Overview

The fix implements a comprehensive cleanup system that:

1. **Detects Unverified Users**: Identifies users who haven't completed email verification
2. **Comprehensive Cleanup**: Removes all related records in the correct order
3. **Transaction Safety**: Uses database transactions to ensure consistency
4. **External Service Cleanup**: Properly cleans up external service records
5. **Graceful Error Handling**: Continues operation even if some cleanup steps fail

## Implementation Details

### Files Modified

#### 1. `src/services/auth.service.ts`

**Added Methods:**

- `cleanupUnverifiedUser(userId: string)`: Comprehensive cleanup of unverified user
- `cleanupExpiredOtpUsers()`: Periodic cleanup of users with expired OTPs

**Modified Methods:**

- `registerClientUser()`: Now calls cleanup for existing unverified users

#### 2. `src/controllers/client.auth.controller.ts`

**Added Methods:**

- `cleanupExpiredOtpUsers()`: Admin endpoint for manual cleanup

#### 3. `src/routes/client.auth.route.ts`

**Added Routes:**

- `POST /admin/cleanup-expired-otp`: Admin-only endpoint for cleanup

### Cleanup Process Flow

```mermaid
graph TD
    A[User tries to register] --> B{Existing user found?}
    B -->|No| C[Create new user]
    B -->|Yes| D{User verified?}
    D -->|Yes| E[Throw error: User exists]
    D -->|No| F[Start cleanup process]
    F --> G[Get user details]
    G --> H[Clean external services]
    H --> I[Start database transaction]
    I --> J[Delete related records]
    J --> K[Delete user record]
    K --> L[Commit transaction]
    L --> M[Create new user]
```

### Cleanup Order

The cleanup process follows a specific order to avoid foreign key constraint violations:

1. **Authentication Records**
   - AuthSession
   - UserSessions
   - Token
   - RestrictedToken

2. **Profile and Settings**
   - Profile
   - UserProfile
   - UserSettings
   - BillingAddress

3. **Activity and Notifications**
   - UserActivity
   - UserActivityLog
   - UserNotification
   - UserFeedback

4. **Subscription Related**
   - Payment (for user's subscriptions)
   - Subscription

5. **Package and Assignments**
   - ClientPackageDetails
   - Assignment

6. **Attendance and Time Tracking**
   - BreakSession
   - AttendanceSummary
   - TimeLog

7. **Shift and Leave Management**
   - ShiftChangeRequest
   - LeaveRequest

8. **Messaging**
   - Mention
   - Reaction
   - MessageReadStatus
   - GroupMember
   - ConversationParticipant
   - Message

9. **Projects and Tasks**
   - Disconnect many-to-many relationships
   - ProjectCoordinatorAssignment
   - Task, SelfTask, AdminTask
   - Project

10. **Other Records**
    - PartnerUser
    - Notification
    - UserRole
    - UserPermission

11. **Relationships**
    - Update coworker relationships

12. **User Record**
    - Finally delete the user

13. **External Services**
    - Dodo customer cleanup

## Usage

### Automatic Cleanup

The cleanup happens automatically when a user tries to register with an email that has an existing unverified account:

```typescript
// In registerClientUser method
if (existingUser && !existingUser.emailVerified) {
  await this.cleanupUnverifiedUser(existingUser.id);
}
```

### Manual Cleanup (Admin)

Admins can manually trigger cleanup of expired OTP users:

```bash
POST /api/auth/admin/cleanup-expired-otp
Authorization: Bearer <admin-token>
```

This will clean up users whose OTP expired more than 24 hours ago.

### Periodic Cleanup

You can set up a cron job to periodically clean up expired OTP users:

```javascript
// Example cron job (runs daily at 2 AM)
const cron = require('node-cron');

cron.schedule('0 2 * * *', async () => {
  try {
    await authService.cleanupExpiredOtpUsers();
    console.log('Expired OTP users cleanup completed');
  } catch (error) {
    console.error('Cleanup failed:', error);
  }
});
```

## Error Handling

The cleanup process includes comprehensive error handling:

1. **Transaction Safety**: All database operations are wrapped in a transaction
2. **External Service Failures**: Continues even if external service cleanup fails
3. **Partial Failures**: Logs warnings for non-critical failures
4. **Verification Safety**: Only deletes unverified users

## Testing

### Test Script

Run the test script to verify the fix:

```bash
node test-registration-fix.js
```

### Manual Testing

1. Register a user but don't verify OTP
2. Try to register again with the same email
3. Verify that the new registration succeeds

## Benefits

1. **Robust Registration**: Users can retry registration without technical errors
2. **Clean Database**: No orphaned records or inconsistent state
3. **External Service Sync**: External services stay in sync with database
4. **Performance**: Periodic cleanup prevents accumulation of stale data
5. **User Experience**: Seamless re-registration process

## Monitoring

Monitor the cleanup process through:

1. **Application Logs**: Detailed logging of cleanup operations
2. **Database Metrics**: Monitor for constraint violations (should be zero)
3. **External Service Logs**: Monitor Dodo customer deletion success rate
4. **User Registration Success Rate**: Should improve significantly

## Future Enhancements

1. **Cleanup Metrics**: Add metrics collection for cleanup operations
2. **Configurable Timeouts**: Make the 24-hour cleanup threshold configurable
3. **Batch Processing**: Process cleanup in batches for better performance
4. **Notification**: Notify admins of cleanup operations
5. **Audit Trail**: Keep audit logs of cleanup operations

## Conclusion

This fix resolves the registration OTP issue by implementing a comprehensive, safe, and robust cleanup system. Users can now retry registration without encountering technical errors, and the system maintains data consistency across all related services.