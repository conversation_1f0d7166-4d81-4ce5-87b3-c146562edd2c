import express from "express";
import authMiddleware from "../../middlewares/checkAuth";
import { hasMultipleRole, hasRole } from "../../middlewares/checkRole";
import { asyncHandler } from "../../middlewares/asyncHandler";
import * as shiftChangeController from "../../controllers/shiftChange/shiftChange.controller";



const router = express.Router();

// Route to create a shift change request (available to ADMIN, PROJECT_COORDINATOR, CLIENT)
router.post(
  "/create-shift-change",
  authMiddleware,
  hasMultipleRole(["ADMIN", "PROJECT_COORDINATOR", "CLIENT"]),
  asyncHandler(shiftChangeController.createShiftChangeRequest)
);

// Route to approve a shift change request (only available to ADMIN, PROJECT_COORDINATOR)
router.post(
  "/approve-shift-change/:requestId",
  authMiddleware,
  hasMultipleRole(["ADMIN", "PROJECT_COORDINATOR"]),
  async<PERSON><PERSON><PERSON>(shiftChangeController.approveShiftChangeRequest)
);

router.get(
  "/pending-shift-requests",
  authMiddleware,
  hasMultipleRole(["ADMIN", "PROJECT_COORDINATOR"]),
  asyncHandler(shiftChangeController.getPendingShiftRequests)
);

router.get(
  "/all-shift-requests",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(shiftChangeController.getAllShiftChangeRequests)
);


router.get(
  "/shift-requests",
  authMiddleware,
  hasMultipleRole(["PROJECT_COORDINATOR","COWORKER", "ANNOTATOR","CLIENT"]),
  asyncHandler(shiftChangeController.getRelevantShiftChangeRequests)
);


export default router;
