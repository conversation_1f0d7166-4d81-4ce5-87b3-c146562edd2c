import { Request, Response } from "express";
import { FAQService } from "../../services/faq/faq.service";
import {
  successResponse,
  successResponseWithData,
  notFoundResponse,
  errorResponse,
  validationErrorWithData,
} from "../../helper/apiResponse";

export class FAQController {
  private faqService: FAQService;

  constructor() {
    this.faqService = new FAQService();
  }

  // Create FAQ
  async create(req: Request, res: Response) {
    try {
      const { question, answer } = req.body;

      if (!question || !answer) {
        return validationErrorWithData(res, "Validation Error", {
          question: !question ? "Question is required" : undefined,
          answer: !answer ? "Answer is required" : undefined,
        });
      }

      const faq = await this.faqService.create({ question, answer });
      return successResponseWithData(res, "FAQ created successfully", faq);
    } catch (error: any) {
      return errorResponse(res, error.message || "Something went wrong");
    }
  }

  // Get all FAQs
  async getAll(req: Request, res: Response) {
    try {
      const faqs = await this.faqService.getAll();
      return successResponseWithData(res, "FAQs retrieved successfully", faqs, faqs.length);
    } catch (error: any) {
      return errorResponse(res, error.message || "Failed to fetch FAQs");
    }
  }

  // Get FAQ by ID
  async getById(req: Request, res: Response) {
    try {
      const { id } = req.params;

      if (!id) return validationErrorWithData(res, "ID is required", { id });

      const faq = await this.faqService.getById(id);

      if (!faq) return notFoundResponse(res, "FAQ not found");

      return successResponseWithData(res, "FAQ retrieved", faq);
    } catch (error: any) {
      return errorResponse(res, error.message || "Something went wrong");
    }
  }

  // Update FAQ
  async update(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { question, answer, isActive } = req.body;

      if (!id) return validationErrorWithData(res, "ID is required", { id });

      const existing = await this.faqService.getById(id);
      if (!existing) return notFoundResponse(res, "FAQ not found");

      const updated = await this.faqService.update(id, { question, answer, isActive });
      return successResponseWithData(res, "FAQ updated successfully", updated);
    } catch (error: any) {
      return errorResponse(res, error.message || "Failed to update FAQ");
    }
  }

  // Delete FAQ
  async delete(req: Request, res: Response) {
    try {
      const { id } = req.params;

      if (!id) return validationErrorWithData(res, "ID is required", { id });

      const existing = await this.faqService.getById(id);
      if (!existing) return notFoundResponse(res, "FAQ not found");

      await this.faqService.delete(id);
      return successResponse(res, "FAQ deleted successfully");
    } catch (error: any) {
      return errorResponse(res, error.message || "Failed to delete FAQ");
    }
  }
}
