.zoho-ticket-system {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.ticket-system-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.ticket-system-header h1 {
  margin: 0;
  color: #333;
}

.create-ticket-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.create-ticket-btn:hover {
  background-color: #43a047;
}

.loading,
.error-message,
.no-tickets {
  padding: 3rem;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.error-message {
  color: #c62828;
  background-color: #ffebee;
}

.no-tickets {
  color: #555;
}

.no-tickets p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

.tickets-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.ticket-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.ticket-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.ticket-id {
  color: #777;
  font-size: 0.9rem;
}

.ticket-status {
  padding: 0.3rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.ticket-status.open {
  background-color: #e3f2fd;
  color: #1976d2;
}

.ticket-status.in.progress,
.ticket-status.in-progress {
  background-color: #fff8e1;
  color: #ff8f00;
}

.ticket-status.closed {
  background-color: #e8f5e9;
  color: #388e3c;
}

.ticket-status.on.hold,
.ticket-status.on-hold {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.ticket-status.escalated {
  background-color: #ffebee;
  color: #c62828;
}

.ticket-subject {
  margin: 0.5rem 0 1rem;
  font-size: 1.1rem;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ticket-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #777;
}

.create-ticket-view {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 2rem;
}

.back-button {
  background-color: transparent;
  border: none;
  color: #4a90e2;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem 0;
  margin-bottom: 1.5rem;
  display: inline-flex;
  align-items: center;
}

.back-button:hover {
  text-decoration: underline;
}

/* Ticket Detail Styles */
.ticket-detail {
  background-color: #fff;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.ticket-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.ticket-detail-subject {
  margin: 0 0 1.5rem;
  color: #333;
  font-size: 1.5rem;
}

.ticket-detail-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.meta-item {
  display: flex;
  flex-direction: column;
}

.meta-label {
  font-size: 0.9rem;
  color: #777;
  margin-bottom: 0.3rem;
}

.meta-value {
  font-weight: 500;
  color: #333;
}

.ticket-detail-description {
  margin-bottom: 2rem;
}

.ticket-detail-description h3 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.2rem;
}

.description-content {
  padding: 1.5rem;
  background-color: #f9f9f9;
  border-radius: 6px;
  white-space: pre-wrap;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .zoho-ticket-system {
    padding: 1rem;
  }
  
  .ticket-system-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .tickets-list {
    grid-template-columns: 1fr;
  }
  
  .ticket-detail-meta {
    grid-template-columns: 1fr;
  }
}