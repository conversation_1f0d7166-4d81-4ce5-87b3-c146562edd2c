# Email Templates Migration Guide

This guide explains how to migrate from the old email system to the new enhanced email template system.

## Overview

We have migrated from a mixed email system (HTML files + TypeScript templates + mailSender utility) to a unified enhanced email system with the following benefits:

- **Consistent Templates**: All email templates are now TypeScript files with proper typing
- **Enhanced Email Service**: Unified service that supports both new templates and legacy HTML files
- **Better Template Rendering**: Improved template variable replacement with conditional blocks
- **Centralized Configuration**: All email configuration in one place
- **Type Safety**: TypeScript templates provide better development experience

## New Structure

```
src/
├── templates/
│   ├── index.ts                    # Template exports and utilities
│   ├── otp-verification.ts         # OTP verification template
│   ├── otp-signup.ts              # Signup OTP template
│   ├── invite-coworker.ts         # Coworker invitation template
│   ├── password-creation-link.ts  # Password creation template
│   ├── account-suspended.ts       # Account suspension template
│   ├── account-reactivated.ts     # Account reactivation template
│   ├── welcome-mail.ts            # Welcome email template
│   ├── password-changed.ts        # Password change confirmation template
│   ├── team-assignment.ts         # Team assignment template
│   ├── subscription-expiring.ts   # Subscription expiring template
│   ├── subscription-expired.ts    # Subscription expired template
│   ├── usage-limit-warning.ts     # Usage limit warning template
│   ├── reset_password.ts          # Password reset template (existing)
│   ├── bank-transfer.ts           # Bank transfer template (existing)
│   └── paypal.ts                  # PayPal template (existing)
└── services/
    └── email/
        ├── enhanced-email.service.ts      # Enhanced email service
        ├── email-integration.service.ts   # Centralized email integration
        ├── email.service.ts              # Original email service (kept for compatibility)
        └── user-communication.service.ts # User communication service (existing)
```

## Migration Steps

### 1. Update Service Imports

**Before:**
```typescript
import { mailSender } from "../utils/mailSender";
import { resetPasswordTemplate } from "../templates/reset_password";
```

**After:**
```typescript
import { EnhancedEmailService } from "./email/enhanced-email.service";
// OR for easier usage:
import { EmailIntegrationService } from "./email/email-integration.service";
```

### 2. Update Service Constructor

**Before:**
```typescript
export class MyService {
  // No email service initialization
}
```

**After:**
```typescript
export class MyService {
  private emailService: EnhancedEmailService;
  // OR
  private emailIntegration: EmailIntegrationService;

  constructor() {
    this.emailService = new EnhancedEmailService();
    // OR
    this.emailIntegration = new EmailIntegrationService();
  }
}
```

### 3. Update Email Sending Code

**Before (using mailSender):**
```typescript
await mailSender({
  user: {
    name: user.name,
    email: user.email,
    subject: "Verify your email",
    body: `Your OTP is ${otp}. Please enter this OTP to verify your email`,
  },
});
```

**After (using EnhancedEmailService):**
```typescript
await this.emailService.sendEmail({
  to: user.email,
  subject: `Welcome to ${process.env.COMPANY_NAME} - Verify Your Email`,
  template: 'otp-signup',
  data: {
    firstName: user.name,
    otpCode: otp,
    timeLimit: 10,
    companyName: process.env.COMPANY_NAME || 'Our Platform',
    supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
    phoneNumber: process.env.SUPPORT_PHONE || '******-567-8900',
    websiteUrl: process.env.FRONTEND_URL || 'https://company.com',
  }
});
```

**After (using EmailIntegrationService - Recommended):**
```typescript
await this.emailIntegration.sendSignupOTP(user.email, {
  firstName: user.name,
  otpCode: otp,
  timeLimit: 10,
});
```

### 4. Update Template Usage

**Before (manual template replacement):**
```typescript
const html = resetPasswordTemplate
  .replace("{{SET_PASSWORD_LINK}}", link)
  .replace("{{CURRENT_YEAR}}", new Date().getFullYear().toString());

await mailSender({
  user: {
    name,
    email,
    subject: "Set your password",
    body: html,
  },
});
```

**After:**
```typescript
await this.emailIntegration.sendPasswordCreationLink(email, {
  firstName: name,
  passwordCreationLink: link,
  timeLimit: "1 hour",
});
```

## Available Email Templates

### Authentication & Onboarding
- `otp-verification` - OTP verification for password reset, account deletion, etc.
- `otp-signup` - OTP verification for signup
- `password-creation-link` - Password creation link for new users
- `reset-password` - Password reset (legacy template)

### Account Management
- `account-suspended` - Account suspension notification
- `account-reactivated` - Account reactivation notification
- `password-changed` - Password change confirmation

### Collaboration
- `invite-coworker` - Coworker invitation
- `welcome-mail` - Welcome email with meeting link
- `team-assignment` - Team assignment notification

### Subscription & Billing
- `subscription-expiring` - Subscription expiring warning
- `subscription-expired` - Subscription expired notification
- `usage-limit-warning` - Usage limit warning
- `paypal` - PayPal payment instructions (legacy template)
- `bank-transfer` - Bank transfer instructions (legacy template)

## Environment Variables

Make sure these environment variables are set:

```env
# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>

# Company Information
COMPANY_NAME=Your Company Name
SUPPORT_EMAIL=<EMAIL>
SUPPORT_PHONE=******-567-8900
FRONTEND_URL=https://yourcompany.com

# PayPal (if using PayPal templates)
PAYPALLINK=https://paypal.me/yourcompany
```

## Template Variables

Each template supports specific variables. Here are the common ones:

### Common Variables (available in all templates)
- `companyName` - Company name
- `supportEmail` - Support email address
- `websiteUrl` - Website URL
- `firstName` - User's first name

### Template-Specific Variables

#### OTP Templates
- `otpCode` - The OTP code
- `timeLimit` - Time limit in minutes
- `customerName` - Customer name

#### Password Templates
- `passwordCreationLink` - Link to create password
- `timeLimit` - Link expiration time

#### Subscription Templates
- `packageName` - Subscription package name
- `endDate` - Subscription end date
- `daysUntilExpiry` - Days until expiration
- `dashboardUrl` - Dashboard URL

## Migration Checklist

- [ ] Update service imports to use `EnhancedEmailService` or `EmailIntegrationService`
- [ ] Add email service to service constructors
- [ ] Replace `mailSender` calls with new email service methods
- [ ] Replace manual template string replacement with template data objects
- [ ] Update environment variables
- [ ] Test all email functionality
- [ ] Update any custom email templates to use the new system

## Backward Compatibility

The new system maintains backward compatibility:

1. **HTML Templates**: The `EnhancedEmailService` can still use HTML files from `src/templates/email/`
2. **Legacy mailSender**: The `sendLegacyEmail` method supports the old mailSender format
3. **Existing Templates**: All existing TypeScript templates (`reset_password.ts`, `paypal.ts`, `bank-transfer.ts`) are still supported

## Benefits of Migration

1. **Type Safety**: TypeScript templates provide better development experience
2. **Consistency**: All templates follow the same structure and styling
3. **Maintainability**: Centralized template management
4. **Flexibility**: Easy to add new templates and modify existing ones
5. **Better Error Handling**: Enhanced error handling and fallback templates
6. **Performance**: Improved template rendering performance
7. **Testing**: Easier to test email functionality

## Example: Complete Service Migration

**Before:**
```typescript
import { mailSender } from "../utils/mailSender";
import { resetPasswordTemplate } from "../templates/reset_password";

export class AuthService {
  async sendPasswordReset(email: string, token: string) {
    const link = `${process.env.FRONTEND_URL}/reset-password/${token}`;
    const html = resetPasswordTemplate
      .replace("{{SET_PASSWORD_LINK}}", link)
      .replace("{{CURRENT_YEAR}}", new Date().getFullYear().toString());

    await mailSender({
      user: {
        name: "User",
        email,
        subject: "Reset your password",
        body: html,
      },
    });
  }
}
```

**After:**
```typescript
import { EmailIntegrationService } from "./email/email-integration.service";

export class AuthService {
  private emailIntegration: EmailIntegrationService;

  constructor() {
    this.emailIntegration = new EmailIntegrationService();
  }

  async sendPasswordReset(email: string, token: string, userName: string) {
    const link = `${process.env.FRONTEND_URL}/reset-password/${token}`;
    
    await this.emailIntegration.sendPasswordCreationLink(email, {
      firstName: userName,
      passwordCreationLink: link,
      timeLimit: "1 hour",
    });
  }
}
```

This migration provides a much cleaner, more maintainable, and type-safe email system.