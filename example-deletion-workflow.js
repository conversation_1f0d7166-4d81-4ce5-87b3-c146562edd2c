/**
 * Example workflow for handling user deletion with client assignment protection
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function demonstrateDeletionWorkflow() {
  console.log('🔄 Demonstrating Enhanced Deletion Workflow\n');

  try {
    // Example 1: Attempt to delete a user assigned to clients (will fail)
    console.log('📋 Example 1: Attempting to delete user assigned to clients');
    
    const assignedUser = await prisma.user.findFirst({
      where: {
        role: { in: ['ANNOTATOR', 'PROJECT_COORDINATOR'] },
        OR: [
          { assignmentsAsCoordinator: { some: {} } },
          { assignmentsAsDeveloper: { some: {} } }
        ]
      },
      include: {
        assignmentsAsCoordinator: {
          include: { client: { select: { name: true } } }
        },
        assignmentsAsDeveloper: {
          include: { client: { select: { name: true } } }
        }
      }
    });

    if (assignedUser) {
      console.log(`Found user: ${assignedUser.name} (${assignedUser.role})`);
      
      const assignments = assignedUser.role === 'PROJECT_COORDINATOR' 
        ? assignedUser.assignmentsAsCoordinator 
        : assignedUser.assignmentsAsDeveloper;
      
      console.log(`Assigned to ${assignments.length} client(s):`);
      assignments.forEach(assignment => {
        console.log(`  - ${assignment.client.name}`);
      });
      
      console.log('❌ Deletion attempt would FAIL with error message:');
      console.log(`   "Cannot delete ${assignedUser.role.toLowerCase()} as they are currently assigned to ${assignments.length} client(s): ${assignments.map(a => a.client.name).join(', ')}. Please unassign them from all clients before deletion."`);
    }

    console.log('\n' + '='.repeat(80) + '\n');

    // Example 2: Show how to unassign and then delete
    console.log('📋 Example 2: Proper workflow for deleting assigned users');
    console.log('Step 1: Identify client assignments');
    console.log('Step 2: Unassign from all clients');
    console.log('Step 3: Attempt deletion (will now succeed)');
    
    if (assignedUser) {
      console.log('\n🔧 Unassignment commands:');
      if (assignedUser.role === 'PROJECT_COORDINATOR') {
        console.log(`DELETE FROM Assignment WHERE coordinatorId = '${assignedUser.id}';`);
      } else {
        console.log(`DELETE FROM Assignment WHERE developerId = '${assignedUser.id}';`);
      }
      console.log('✅ After unassignment, deletion will proceed based on other relationships');
    }

    console.log('\n' + '='.repeat(80) + '\n');

    // Example 3: Find users that can be deleted (no client assignments)
    console.log('📋 Example 3: Users that can be deleted');
    
    const deletableUsers = await prisma.user.findMany({
      where: {
        role: { in: ['ANNOTATOR', 'PROJECT_COORDINATOR'] },
        AND: [
          { assignmentsAsCoordinator: { none: {} } },
          { assignmentsAsDeveloper: { none: {} } }
        ]
      },
      include: {
        projectsAsCoordinator: true,
        annotatorProjects: true,
        annotatedTasks: true,
        createdTasks: true,
        timeLogs: true
      },
      take: 3
    });

    if (deletableUsers.length > 0) {
      console.log(`Found ${deletableUsers.length} users that can be deleted:`);
      
      deletableUsers.forEach(user => {
        console.log(`\n👤 ${user.name} (${user.role})`);
        
        let hasOtherRelationships = false;
        const relationships = [];
        
        if (user.projectsAsCoordinator.length > 0) {
          hasOtherRelationships = true;
          relationships.push(`${user.projectsAsCoordinator.length} project(s) as coordinator`);
        }
        
        if (user.annotatorProjects.length > 0) {
          hasOtherRelationships = true;
          relationships.push(`${user.annotatorProjects.length} annotator project(s)`);
        }
        
        if (user.annotatedTasks.length > 0) {
          hasOtherRelationships = true;
          relationships.push(`${user.annotatedTasks.length} annotated task(s)`);
        }
        
        if (user.createdTasks.length > 0) {
          hasOtherRelationships = true;
          relationships.push(`${user.createdTasks.length} created task(s)`);
        }
        
        if (user.timeLogs.length > 0) {
          hasOtherRelationships = true;
          relationships.push(`${user.timeLogs.length} time log(s)`);
        }
        
        if (hasOtherRelationships) {
          console.log(`   🔄 Would be SOFT DELETED (has relationships: ${relationships.join(', ')})`);
        } else {
          console.log(`   🗑️  Would be HARD DELETED (no relationships found)`);
        }
      });
    } else {
      console.log('❌ No users found that can be deleted');
    }

    console.log('\n' + '='.repeat(80) + '\n');

    // Example 4: Show deletion hierarchy
    console.log('📋 Example 4: Deletion Decision Hierarchy');
    console.log('1. ❌ BLOCKED: User assigned to clients → Throw error with client names');
    console.log('2. 🔄 SOFT DELETE: User has other relationships → Mark as deleted, preserve data');
    console.log('3. 🗑️  HARD DELETE: User has no relationships → Permanently remove from database');
    
    console.log('\n📊 Relationship Priority:');
    console.log('   Highest: Client assignments (blocks deletion)');
    console.log('   Medium:  Projects, tasks, time logs (triggers soft delete)');
    console.log('   Lowest:  No relationships (allows hard delete)');

  } catch (error) {
    console.error('❌ Error during demonstration:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the demonstration
demonstrateDeletionWorkflow();