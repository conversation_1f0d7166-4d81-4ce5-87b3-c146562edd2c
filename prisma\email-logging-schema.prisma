// // Optional Email Logging Models
// // Add these to your main schema.prisma if you want email logging functionality

// enum EmailStatus {
//   PENDING
//   SENT
//   FAILED
//   BOUNCED
//   DELIVERED
//   OPENED
//   CLICKED
// }

// enum EmailPriority {
//   LOW
//   NORMAL
//   HIGH
//   URGENT
// }

// // Email Log - tracks all sent emails
// model EmailLog {
//   id            String      @id @default(cuid())
//   to            String
//   cc            String?
//   bcc           String?
//   subject       String
//   templateName  String?
//   status        EmailStatus @default(SENT)
//   errorMessage  String?
//   sentAt        DateTime    @default(now())
//   deliveredAt   DateTime?
//   openedAt      DateTime?
//   clickedAt     DateTime?
  
//   // User who triggered the email (optional)
//   userId        String?
//   user          User?       @relation(fields: [userId], references: [id], onDelete: SetNull)
  
//   // Additional metadata
//   metadata      Json?
  
//   @@index([userId])
//   @@index([templateName])
//   @@index([sentAt])
//   @@index([status])
//   @@map("EmailLog")
// }

// // Email Template Usage Analytics
// model EmailTemplateUsage {
//   id           String   @id @default(cuid())
//   templateName String   @unique
//   usageCount   Int      @default(1)
//   lastUsed     DateTime @default(now())
//   createdAt    DateTime @default(now())
//   updatedAt    DateTime @updatedAt
  
//   @@map("EmailTemplateUsage")
// }

// // Email Queue for batch processing
// model EmailQueue {
//   id           String        @id @default(cuid())
//   to           String
//   cc           String?
//   bcc          String?
//   subject      String
//   templateName String?
//   templateData Json
//   priority     EmailPriority @default(NORMAL)
//   status       EmailStatus   @default(PENDING)
//   scheduledFor DateTime?
//   attempts     Int           @default(0)
//   maxAttempts  Int           @default(3)
//   errorMessage String?
//   createdAt    DateTime      @default(now())
//   updatedAt    DateTime      @updatedAt
//   processedAt  DateTime?
  
//   @@index([status])
//   @@index([scheduledFor])
//   @@index([priority])
//   @@map("EmailQueue")
// }

// // Email Templates Configuration (optional)
// model EmailTemplate {
//   id          String   @id @default(cuid())
//   name        String   @unique
//   subject     String
//   content     String
//   variables   String[] // Array of variable names
//   category    String?
//   description String?
//   isActive    Boolean  @default(true)
//   createdAt   DateTime @default(now())
//   updatedAt   DateTime @updatedAt
  
//   @@map("EmailTemplate")
// }