export const newsletterTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{ params.newsletterTitle }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #171617;
            padding: 20px;
            text-align: center;
        }
        .logo {
            max-width: 140px;
            height: auto;
            display: block;
            margin: 0 auto;
        }
        .header h1 {
            font-size: 24px;
            color: #ffffff;
            margin: 10px 0;
        }
        .newsletter-date {
            font-size: 14px;
            color: #f0f0f0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: #1a3c34;
            margin-bottom: 15px;
            border-bottom: 2px solid #1a3c34;
            padding-bottom: 5px;
        }
        .article {
            background-color: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 2px solid #162f29;
        }
        .article-title {
            font-size: 16px;
            font-weight: bold;
            color: #1a3c34;
            margin-bottom: 10px;
        }
        .article-excerpt {
            font-size: 14px;
            color: #4b5563;
            margin-bottom: 15px;
        }
        .read-more {
            color: #1a3c34 !important;
            text-decoration: none;
            font-weight: bold;
        }
        .feature-highlight {
            background-color: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
            text-align: center;
            border: 2px solid #162f29;
        }
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            color: #1a3c34;
            margin-bottom: 10px;
        }
        .cta-button {
            display: inline-block;
            background-color: #1a3c34;
            color: #ffffff !important;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin-top: 15px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-item {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 6px;
            text-align: center;
            border: 2px solid #162f29;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1a3c34;
        }
        .stat-label {
            font-size: 12px;
            color: #4b5563;
            text-transform: uppercase;
        }
        .social-links {
            text-align: center;
            margin: 30px 0;
        }
        .social-link {
            display: inline-block;
            margin: 0 10px;
            color: #1a3c34 !important;
            text-decoration: none;
            font-weight: bold;
        }
        .footer {
            padding: 20px;
            text-align: center;
            font-size: 13px;
            line-height: 20px;
            background-color: #171617;
            color: #ffffff;
        }
        .footer a {
            color: #e1e1e1 !important;
            text-decoration: none;
        }
        .footer span {
            color: #f0f0f0;
        }
        /* Email client compatibility */
        table {
            width: 100%;
            border-collapse: collapse;
        }
        @media only screen and (max-width: 600px) {
            .container {
                padding: 15px;
            }
            .content {
                padding: 20px;
            }
            .header {
                padding: 15px;
            }
            .section-title {
                font-size: 18px;
            }
            .article-title {
                font-size: 14px;
            }
            .cta-button {
                width: 100%;
                box-sizing: border-box;
            }
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
        /* Accessibility */
        a:focus, .cta-button:focus {
            outline: 2px solid #1a3c34;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <table role="presentation" class="container">
        <tr>
            <td>
                <div class="header">
                    <img src="https://macgence.s3.ap-south-1.amazonaws.com/whitemode.png" alt="GetAnnotator Logo" class="logo">
                    <h1>{{ params.newsletterTitle }}</h1>
                    <div class="newsletter-date">{{ params.newsletterDate }}</div>
                </div>

                <div class="content">
                    <p>Hi {{ params.firstName }},</p>
                    <p>{{ params.introMessage }}</p>

                    {% for section in params.sections %}
                    <div class="section">
                        <h2 class="section-title">{{ section.sectionTitle }}</h2>
                        {% for article in section.articles %}
                        <div class="article">
                            <div class="article-title">{{ article.title }}</div>
                            <div class="article-excerpt">{{ article.excerpt }}</div>
                            <a href="{{ article.url }}" class="read-more">Read More →</a>
                        </div>
                        {% endfor %}
                    </div>
                    {% endfor %}

                    {% if params.featureHighlight %}
                    <div class="feature-highlight">
                        <div class="feature-title">🚀 {{ params.featureHighlight.title }}</div>
                        <p>{{ params.featureHighlight.description }}</p>
                        <a href="{{ params.featureHighlight.url }}" class="cta-button">{{ params.featureHighlight.buttonText }}</a>
                    </div>
                    {% endif %}

                    {% if params.stats %}
                    <div class="section">
                        <h2 class="section-title">📊 This Month's Highlights</h2>
                        <div class="stats-grid">
                            {% for item in params.stats.items %}
                            <div class="stat-item">
                                <div class="stat-number">{{ item.number }}</div>
                                <div class="stat-label">{{ item.label }}</div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <div class="section">
                        <h2 class="section-title">🔗 Stay Connected</h2>
                        <div class="social-links">
                            {% for link in params.socialLinks %}
                            <a href="{{ link.url }}" class="social-link">{{ link.name }}</a>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <div class="footer">
                    <span><EMAIL></span> | 
                    <a href="http://app.getannotator.com/" style="color: #e1e1e1ff; text-decoration: none;">app.getannotator.com</a> | 
                    <a href="tel:+13602098904">******-209-8904</a><br>
                    <span>Need help? Reach us via our <a href="http://app.getannotator.com/contact">contact form</a>.</span>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>

`;