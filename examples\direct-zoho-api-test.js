/**
 * Direct test of the Zoho Desk API using the exact format from the curl command
 * 
 * Usage: node direct-zoho-api-test.js
 */

const axios = require('axios');
require('dotenv').config();

async function testZohoDeskAPI() {
  try {
    // Get the access token
    const accessToken = process.env.ZOHO_DESK_ACCESS_TOKEN || 'YOUR_ACCESS_TOKEN';
    const orgId = process.env.ZOHO_DESK_ORG_ID || '2389290';
    
    // Use the exact payload from the curl command
    const payload = {
      entitySkills: ["18921000000379001", "18921000000364001", "18921000000379055", "18921000000379031"],
      subCategory: "Sub General",
      cf: {
        cf_permanentaddress: null,
        cf_dateofpurchase: null,
        cf_phone: null,
        cf_numberofitems: null,
        cf_url: null,
        cf_secondaryemail: null,
        cf_severitypercentage: "0.0",
        cf_modelname: "F3 2017"
      },
      productId: "",
      contactId: "1892000000042032",
      subject: "Real Time analysis Requirement",
      dueDate: "2016-06-21T16:16:16.000Z",
      departmentId: "1892000000006907",
      channel: "Email",
      description: "Hai This is Description",
      language: "English",
      priority: "High",
      classification: "",
      assigneeId: "1892000000056007",
      phone: "**************",
      category: "general",
      email: "<EMAIL>",
      status: "Open"
    };

    console.log('Sending request to Zoho Desk API...');
    
    const response = await axios.post('https://desk.zoho.com/api/v1/tickets', payload, {
      headers: {
        'Content-Type': 'application/json',
        'orgId': orgId,
        'Authorization': `Zoho-oauthtoken ${accessToken}`
      }
    });

    console.log('Success! Ticket created:');
    console.log(JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('Error creating ticket:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Headers:', error.response.headers);
      console.error('Data:', error.response.data);
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Error:', error.message);
    }
  }
}

testZohoDeskAPI();