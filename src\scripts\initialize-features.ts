/**
 * <PERSON><PERSON><PERSON> to initialize default features for packages
 * Run this script to set up basic features for existing packages
 */

import prisma from "../prisma";
import { FEATURES, PACKAGE_FEATURE_SETS } from "../constants/features";

async function initializeFeatures() {
  console.log("🚀 Starting feature initialization...");

  try {
    // 1. Create all features if they don't exist
    console.log("📝 Creating features...");
    
    const featurePromises = Object.values(FEATURES).map(async (featureRule) => {
      const existingFeature = await prisma.feature.findFirst({
        where: { rule: featureRule }
      });

      if (!existingFeature) {
        const feature = await prisma.feature.create({
          data: { rule: featureRule }
        });
        console.log(`✅ Created feature: ${featureRule}`);
        return feature;
      } else {
        console.log(`⏭️  Feature already exists: ${featureRule}`);
        return existingFeature;
      }
    });

    const features = await Promise.all(featurePromises);
    console.log(`📊 Total features: ${features.length}`);

    // 2. Get all existing packages
    const packages = await prisma.package.findMany({
      where: { isActive: true },
      include: {
        features: {
          include: {
            feature: true
          }
        }
      }
    });

    console.log(`📦 Found ${packages.length} active packages`);

    // 3. Assign features to packages based on their names or create default assignments
    for (const pkg of packages) {
      console.log(`\n🔧 Processing package: ${pkg.name}`);
      
      // Determine feature set based on package name
      let featureSet: string[] = [];
      const pkgNameLower = pkg.name.toLowerCase();
      
      if (pkgNameLower.includes('basic') || pkgNameLower.includes('starter')) {
        featureSet = [...PACKAGE_FEATURE_SETS.BASIC];
      } else if (pkgNameLower.includes('standard') || pkgNameLower.includes('professional')) {
        featureSet = [...PACKAGE_FEATURE_SETS.STANDARD];
      } else if (pkgNameLower.includes('premium') || pkgNameLower.includes('advanced')) {
        featureSet = [...PACKAGE_FEATURE_SETS.PREMIUM];
      } else if (pkgNameLower.includes('enterprise') || pkgNameLower.includes('unlimited')) {
        featureSet = [...PACKAGE_FEATURE_SETS.ENTERPRISE];
      } else {
        // Default to STANDARD for unknown packages
        featureSet = [...PACKAGE_FEATURE_SETS.STANDARD];
        console.log(`⚠️  Unknown package type, defaulting to STANDARD features`);
      }

      // Get existing feature assignments
      const existingFeatureRules = pkg.features.map(pf => pf.feature.rule);
      
      // Add missing features
      for (const featureRule of featureSet) {
        if (!existingFeatureRules.includes(featureRule)) {
          const feature = features.find(f => f.rule === featureRule);
          if (feature) {
            await prisma.packageFeature.create({
              data: {
                packageId: pkg.id,
                featureId: feature.id,
                available: true
              }
            });
            console.log(`  ✅ Added feature: ${featureRule}`);
          }
        } else {
          console.log(`  ⏭️  Feature already assigned: ${featureRule}`);
        }
      }
    }

    // 4. Display summary
    console.log("\n📋 Feature Assignment Summary:");
    const updatedPackages = await prisma.package.findMany({
      where: { isActive: true },
      include: {
        features: {
          include: {
            feature: true
          }
        }
      }
    });

    for (const pkg of updatedPackages) {
      console.log(`\n📦 ${pkg.name}:`);
      console.log(`  💰 Price: ${pkg.price} ${pkg.currency}`);
      console.log(`  🔧 Features (${pkg.features.length}):`);
      pkg.features.forEach(pf => {
        console.log(`    ${pf.available ? '✅' : '❌'} ${pf.feature.rule}`);
      });
    }

    console.log("\n🎉 Feature initialization completed successfully!");

  } catch (error) {
    console.error("❌ Error during feature initialization:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script if called directly
if (require.main === module) {
  initializeFeatures()
    .then(() => {
      console.log("✅ Script completed successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Script failed:", error);
      process.exit(1);
    });
}

export { initializeFeatures };