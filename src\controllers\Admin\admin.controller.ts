// controllers/admin/admin.controller.ts
import { Request, Response } from "express";
import { AdminService } from "../../services/Admin/admin.service";
import {
  successResponseWithData,
  successResponse,
  errorResponse,
  notFoundResponse,
} from "../../helper/apiResponse";
import { AuthenticatedRequest } from "../../middlewares/checkAuth";

const adminService = new AdminService();

export class AdminController {
  async create(req: AuthenticatedRequest, res: Response) {
    try {
      const createdById = req.user?.userId;
      if (!createdById) return errorResponse(res, "Unauthorized");
  
      const admin = await adminService.create({
        ...req.body,
        createdById,
      });
  
      return successResponseWithData(res, "Admin created successfully", admin);
    } catch (err: any) {
      return errorResponse(res, err.message || "Something went wrong");
    }
  }
  async getAll(req: Request, res: Response) {
    try {
      const admins = await adminService.getAll();
  
      if (!admins || admins.length === 0) {
        return notFoundResponse(res, "No admins found");
      }
  
      return successResponseWithData(res, "Admins fetched successfully", admins, admins.length);
    } catch (error) {
      console.error("Error fetching admins:", error);
      return errorResponse(res, "An error occurred while fetching admins");
    }  
  }

  async getById(req: Request, res: Response) {
    const { id } = req.params;
    const admin = await adminService.getById(id);
    if (!admin) return errorResponse(res, "Admin not found");
    return successResponseWithData(res, "Admin found", admin);
  }

  async update(req: Request, res: Response) {
    try {
      const { id } = req.params;
  
      // Validate request body
      if (!req.body || Object.keys(req.body).length === 0) {
        return errorResponse(res, "Request body is empty");
      }
  
      // Try to update admin
      const updated = await adminService.update(id, req.body);
  
      if (!updated) {
        return notFoundResponse(res, "Admin not found");
      }
  
      return successResponseWithData(res, "Admin updated successfully", updated);
    } catch (error) {
      console.error("Error updating admin:", error);
      return errorResponse(res, "Something went wrong while updating admin");
    }
  }
  
  async delete(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const result = await adminService.delete(id);
      
      // Handle the new response format from the updated delete method
      if (result && typeof result === 'object' && 'deletionType' in result) {
        return successResponseWithData(res, result.message, {
          deletionType: result.deletionType,
          relationshipDetails: result.relationshipDetails,
          user: {
            id: result.id,
            name: result.name,
            email: result.email,
            role: result.role,
            isDeleted: result.isDeleted,
            accountStatus: result.accountStatus,
          }
        });
      }
      
      // Fallback for backward compatibility
      return successResponse(res, "User deleted successfully");
    } catch (err: any) {
      return errorResponse(res, err.message || "Something went wrong");
    }
  }

  async suspend(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { duration } = req.body;
  
      if (!duration) return errorResponse(res, "Suspension duration is required");
  
      const updated = await adminService.suspend(id, duration);
      return successResponseWithData(res, "Admin suspended", updated);
    } catch (error: any) {
      return errorResponse(res, error.message || "Something went wrong");
    }
  }

  
  async activate(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const updated = await adminService.activate(id);
      return successResponseWithData(res, "Admin account activated", updated);
    } catch (error: any) {
      return errorResponse(res, error.message || "Something went wrong");
    }
  }

  async deleteOtherAdmin(req: AuthenticatedRequest, res: Response) {
    const { id } = req.params;
    const requestedById = req.user?.userId;
  
    if (!requestedById) return errorResponse(res, "Unauthorized");
  
    try {
      const result = await adminService.deleteByOtherAdmin(id, requestedById);
      
      // Handle the new response format from the updated delete method
      if (result && typeof result === 'object' && 'deletionType' in result) {
        return successResponseWithData(res, result.message, {
          deletionType: result.deletionType,
          relationshipDetails: result.relationshipDetails,
          user: {
            id: result.id,
            name: result.name,
            email: result.email,
            role: result.role,
            isDeleted: result.isDeleted,
            accountStatus: result.accountStatus,
          }
        });
      }
      
      // Fallback for backward compatibility
      return successResponse(res, "Admin deleted successfully");
    } catch (err: any) {
      return errorResponse(res, err.message || "Something went wrong");
    }
  }

  async resetPassword(req: Request, res: Response) {
    const { id } = req.params;
    const { newPassword } = req.body;
  
    if (!newPassword) return errorResponse(res, "New password is required");
  
    try {
      const updated = await adminService.resetPassword(id, newPassword);
      return successResponseWithData(res, "Password reset successfully", updated);
    } catch (err: any) {
      return errorResponse(res, err.message || "Something went wrong");
    }
  }
  
  // SUSPEND CLIENT
async suspendClient(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const { duration } = req.body;
    if (!duration) return errorResponse(res, "Suspension duration is required");

    const updated = await adminService.suspendClient(id, duration);
    return successResponseWithData(res, "Client suspended", updated);
  } catch (err: any) {
    return errorResponse(res, err.message || "Something went wrong");
  }
}

// ACTIVATE CLIENT
async activateClient(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const updated = await adminService.activateClient(id);
    return successResponseWithData(res, "Client activated", updated);
  } catch (err: any) {
    return errorResponse(res, err.message || "Something went wrong");
  }
}

// RESET CLIENT PASSWORD
async resetClientPassword(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const { newPassword } = req.body;
    if (!newPassword) return errorResponse(res, "New password is required");

    const updated = await adminService.resetClientPassword(id, newPassword);
    return successResponseWithData(res, "Client password reset", updated);
  } catch (err: any) {
    return errorResponse(res, err.message || "Something went wrong");
  }
}

// SOFT DELETE CLIENT
async softDeleteClient(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await adminService.softDeleteClient(id);
    return successResponseWithData(res, "Client soft deleted", result);
  } catch (err: any) {
    return errorResponse(res, err.message || "Something went wrong");
  }
}

  

  
}
