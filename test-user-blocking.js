const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000'; // Adjust to your server URL
const ADMIN_EMAIL = '<EMAIL>'; // Replace with actual admin email
const ADMIN_PASSWORD = 'admin123'; // Replace with actual admin password

let adminToken = '';
let testUserId = '';

// Helper function to make authenticated requests
const makeAuthenticatedRequest = async (method, url, data = null) => {
  const config = {
    method,
    url: `${BASE_URL}${url}`,
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  return axios(config);
};

// Test user blocking functionality
async function testUserBlocking() {
  try {
    console.log('🚀 Starting User Blocking Tests...\n');

    // Step 1: Admin login
    console.log('1. Admin Login...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD
    });
    
    if (loginResponse.data.status === 'true') {
      console.log('   ✅ OTP sent to admin email');
      
      // You'll need to manually get the OTP from email and verify
      const otp = prompt('Enter OTP from admin email: ');
      
      const verifyResponse = await axios.post(`${BASE_URL}/auth/verify-login-otp`, {
        email: ADMIN_EMAIL,
        otp: otp
      });
      
      adminToken = verifyResponse.data.token;
      console.log('   ✅ Admin logged in successfully');
    }

    // Step 2: Create a test user (annotator)
    console.log('\n2. Creating test user...');
    const createUserResponse = await makeAuthenticatedRequest('POST', '/onboarding/create', {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'testpass123',
      role: 'ANNOTATOR'
    });
    
    testUserId = createUserResponse.data.user.id;
    console.log(`   ✅ Test user created with ID: ${testUserId}`);

    // Step 3: Test blocking the user
    console.log('\n3. Blocking the test user...');
    const blockResponse = await makeAuthenticatedRequest('PATCH', `/onboarding/update-status/${testUserId}/suspend`, {
      status: 'SUSPENDED'
    });
    
    console.log('   ✅ User blocked successfully');
    console.log('   📝 Response:', blockResponse.data);

    // Step 4: Test user trying to login (should fail)
    console.log('\n4. Testing blocked user login...');
    try {
      const blockedLoginResponse = await axios.post(`${BASE_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'testpass123'
      });
      console.log('   ❌ Blocked user was able to login (this should not happen)');
    } catch (error) {
      if (error.response && error.response.status === 403) {
        console.log('   ✅ Blocked user login correctly prevented');
        console.log('   📝 Error message:', error.response.data.message);
      } else {
        console.log('   ⚠️  Unexpected error:', error.response?.data || error.message);
      }
    }

    // Step 5: Test suspension with time limit
    console.log('\n5. Testing temporary suspension...');
    const suspendResponse = await makeAuthenticatedRequest('PATCH', `/onboarding/suspension/${testUserId}`, {
      suspendedUntil: 1 // 1 day
    });
    
    console.log('   ✅ User suspended temporarily');
    console.log('   📝 Response:', suspendResponse.data);

    // Step 6: Test reactivating the user
    console.log('\n6. Reactivating the test user...');
    const reactivateResponse = await makeAuthenticatedRequest('PATCH', `/onboarding/reactivate/${testUserId}`);
    
    console.log('   ✅ User reactivated successfully');
    console.log('   📝 Response:', reactivateResponse.data);

    // Step 7: Test user can login again
    console.log('\n7. Testing reactivated user login...');
    const reactivatedLoginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'testpass123'
    });
    
    if (reactivatedLoginResponse.data.status === 'true') {
      console.log('   ✅ Reactivated user can login (OTP sent)');
    }

    // Step 8: Get all users to verify status
    console.log('\n8. Fetching all users...');
    const usersResponse = await makeAuthenticatedRequest('GET', '/onboarding/getall');
    
    const testUser = usersResponse.data.data.data.find(user => user.id === testUserId);
    console.log('   ✅ User status in database:', testUser?.accountStatus);

    // Cleanup: Delete test user
    console.log('\n9. Cleaning up test user...');
    await makeAuthenticatedRequest('DELETE', `/onboarding/delete/${testUserId}`);
    console.log('   ✅ Test user deleted');

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    // Cleanup on error
    if (testUserId) {
      try {
        await makeAuthenticatedRequest('DELETE', `/onboarding/delete/${testUserId}`);
        console.log('🧹 Test user cleaned up after error');
      } catch (cleanupError) {
        console.log('⚠️  Could not cleanup test user');
      }
    }
  }
}

// Test different user types
async function testDifferentUserTypes() {
  console.log('\n🔄 Testing blocking for different user types...\n');
  
  const userTypes = ['CLIENT', 'ANNOTATOR', 'PROJECT_COORDINATOR', 'COWORKER'];
  
  for (const userType of userTypes) {
    try {
      console.log(`Testing ${userType}...`);
      
      // Create user
      const createResponse = await makeAuthenticatedRequest('POST', '/onboarding/create', {
        name: `Test ${userType}`,
        email: `test${userType.toLowerCase()}@example.com`,
        password: 'testpass123',
        role: userType
      });
      
      const userId = createResponse.data.user.id;
      console.log(`   ✅ ${userType} created`);
      
      // Block user
      await makeAuthenticatedRequest('PATCH', `/onboarding/update-status/${userId}/suspend`, {
        status: 'SUSPENDED'
      });
      console.log(`   ✅ ${userType} blocked`);
      
      // Cleanup
      await makeAuthenticatedRequest('DELETE', `/onboarding/delete/${userId}`);
      console.log(`   ✅ ${userType} cleaned up`);
      
    } catch (error) {
      console.log(`   ❌ Error testing ${userType}:`, error.response?.data?.message || error.message);
    }
  }
}

// Run tests
if (require.main === module) {
  testUserBlocking()
    .then(() => testDifferentUserTypes())
    .catch(console.error);
}

module.exports = { testUserBlocking, testDifferentUserTypes };