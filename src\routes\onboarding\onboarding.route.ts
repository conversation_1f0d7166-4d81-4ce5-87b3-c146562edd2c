import { Router } from "express";
import { asyncHandler } from "../../middlewares/asyncHandler";
import authMiddleware from "../../middlewares/checkAuth"; // ✅ fixed import
import { hasRole } from "../../middlewares/checkRole";
import { OnboardingController } from "../../controllers/onboarding/onboarding.controller";
import { validate } from "../../middlewares/validator.middleware";
import { createOnBoardingSchema } from "../../validators/onboarding";

const router = Router();
const controller = new OnboardingController();

router.post(
  "/create",
  authMiddleware,
  hasRole("ADMIN"),
  validate(createOnBoardingSchema),
  asyncHandler(controller.create.bind(controller))
);

router.get(
  "/getall",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(controller.getAll.bind(controller))
);

router.get(
  "/get/:id",
  authMiddleware,
  hasRole("ADMIN"),
  async<PERSON>and<PERSON>(controller.getbyId.bind(controller))
);

router.patch(
  "/update/:id",
  authMiddleware,
  hasRole("ADMIN"),
  async<PERSON><PERSON><PERSON>(controller.updateById.bind(controller))
);

router.patch(
  "/update-status/:id/suspend",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(controller.updateStatus.bind(controller))
);

router.delete(
  "/delete/:id",
  authMiddleware,
  hasRole("ADMIN"),
  asyncHandler(controller.deleteById.bind(controller))
);

router
  .route("/reset-password/:id")
  .patch(
    authMiddleware,
    hasRole("ADMIN"),
    asyncHandler(controller.resetPassword.bind(controller))
  );

router
  .route("/suspension/:id")
  .patch(
    authMiddleware,
    hasRole("ADMIN"),
    asyncHandler(controller.suspendUser.bind(controller))
  );

router
  .route("/activation/:id")
  .patch(
    authMiddleware,
    hasRole("ADMIN"),
    asyncHandler(controller.reactivateUser.bind(controller))
  );

// router
//   .route("/hard-delete/:id")
//   .delete(
//     authMiddleware,
//     hasRole("ADMIN"),
//     asyncHandler(controller.hardDeleteUser.bind(controller))
//   );

// router
//   .route("/deletion-preview/:id")
//   .get(
//     authMiddleware,
//     hasRole("ADMIN"),
//     asyncHandler(controller.getUserDeletionPreview.bind(controller))
//   );

export default router;
