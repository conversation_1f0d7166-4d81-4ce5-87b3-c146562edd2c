# Comprehensive Subscription System Documentation

## Overview

This document describes the comprehensive subscription expiry handling system that prevents customers from accessing features when their subscriptions expire. The system handles multiple subscriptions per user and provides granular feature-based access control.

## Key Features

### 1. Subscription Expiry Protection
- **Automatic blocking** when subscriptions expire
- **Multiple subscription support** - users can have multiple active subscriptions
- **Role-based access** - different rules for CLIENT, COWORKER, ANNOTATOR, PROJECT_COORDINATOR, and ADMIN
- **Graceful degradation** - clear error messages explaining why access is denied

### 2. Feature-Based Access Control
- **Granular permissions** based on package features
- **Combined feature access** from multiple active subscriptions
- **Feature constants** for consistent feature naming
- **Package tiers** with predefined feature sets

### 3. Protected Features

#### Project Management
- `CREATE_PROJECT` - Create new projects
- `UPDATE_PROJECT` - Update existing projects  
- `DELETE_PROJECT` - Delete projects

#### Task Management
- `CREATE_TASK` - Create new tasks
- `UPDATE_TASK` - Update existing tasks
- `DELETE_TASK` - Delete tasks
- `ASSIGN_TASK` - Assign tasks to annotators

#### Self Task Management
- `CREATE_SELF_TASK` - Create personal tasks
- `UPDATE_SELF_TASK` - Update personal tasks
- `DELETE_SELF_TASK` - Delete personal tasks

#### Team Management
- `INVITE_COWORKER` - Invite team members
- `MANAGE_COWORKER` - Manage team members
- `UPDATE_COWORKER_PERMISSION` - Update team member permissions

#### Communication
- `SEND_MESSAGE` - Send messages and chat
- `GROUP_CHAT` - Create and participate in group chats
- `UPLOAD_FILE` - Upload files and attachments

#### Advanced Features
- `BULK_OPERATIONS` - Perform bulk operations
- `ADVANCED_REPORTING` - Access advanced reports
- `API_ACCESS` - Access REST API
- `CUSTOM_INTEGRATIONS` - Set up custom integrations

## Protected Routes

### Projects (`/api/projects/`)
- `POST /create-project` - ✅ Protected (CREATE_PROJECT)
- `POST /cooworker/create-project` - ✅ Protected (CREATE_PROJECT)
- `PUT /update-project/:id` - ✅ Protected (UPDATE_PROJECT)
- `DELETE /delete-project/:id` - ✅ Protected (DELETE_PROJECT)

### Tasks (`/api/tasks/`)
- `POST /create-task` - ✅ Protected (CREATE_TASK)
- `POST /cooworker/create-task` - ✅ Protected (CREATE_TASK)
- `PUT /update-task/:id` - ✅ Protected (UPDATE_TASK)
- `DELETE /delete-task/:id` - ✅ Protected (DELETE_TASK)

### Coworkers (`/api/coworker/`)
- `POST /invite-coworker` - ✅ Protected (INVITE_COWORKER)
- `PATCH /:id/permission` - ✅ Protected (UPDATE_COWORKER_PERMISSION)
- `POST /:id/resend-invite` - ✅ Protected (MANAGE_COWORKER)

### Messages (`/api/messages/`)
- `GET /messages/conversation/:userId` - ✅ Protected (SEND_MESSAGE)
- `GET /get-all-message/:otherUserId` - ✅ Protected (SEND_MESSAGE)
- `GET /group-messages/:groupId` - ✅ Protected (GROUP_CHAT)

### File Uploads (`/api/chat/`)
- `POST /upload` - ✅ Protected (UPLOAD_FILE)

### Self Tasks (`/api/self-tasks/`)
- `POST /create-task` - ✅ Protected (CREATE_SELF_TASK)
- `PATCH /update-task/:id` - ✅ Protected (UPDATE_SELF_TASK)
- `DELETE /delete-task/:id` - ✅ Protected (DELETE_SELF_TASK)

## Middleware Types

### 1. `requireActiveSubscription`
- **Purpose**: Blocks access if no active subscription exists
- **Usage**: For critical features that require paid access
- **Error**: Returns detailed error message about subscription status
- **Logging**: Logs access attempts with user ID and reason for denial

### 2. `requireFeatureAccess(featureName)`
- **Purpose**: Checks if user's subscription includes specific feature
- **Usage**: For feature-specific access control
- **Error**: Returns message about missing feature in current plan
- **Validation**: Validates feature name at middleware creation time
- **Logging**: Logs feature access attempts and results

### 3. `requireAllFeatures(featureNames[])`
- **Purpose**: Checks if user has ALL specified features
- **Usage**: For operations requiring multiple features
- **Error**: Lists all missing features in error message
- **Performance**: Uses Promise.all for concurrent feature checks

### 4. `requireAnyFeature(featureNames[])`
- **Purpose**: Checks if user has ANY of the specified features
- **Usage**: For operations with alternative feature requirements
- **Error**: Shows all required features when none are available
- **Flexibility**: Allows access if user has at least one required feature

### 5. `checkSubscriptionWithWarning`
- **Purpose**: Checks subscription but allows access (for read-only operations)
- **Usage**: For operations that should show warnings but not block access
- **Behavior**: Sets subscription status in request object
- **Graceful**: Continues even if subscription check fails

## User Role Handling

### CLIENT
- Direct subscription check on their own account
- Full access to all features in their active subscriptions

### COWORKER, ANNOTATOR, PROJECT_COORDINATOR
- Checks their client owner's subscription status
- Access based on client's active subscriptions
- Clear error messages directing them to contact their client

### ADMIN
- Bypasses all subscription checks
- Full access to all features
- Can manage other users' subscriptions

## Multiple Subscription Support

### Combined Feature Access
When a user has multiple active subscriptions, the system:
1. **Combines all features** from all active subscriptions
2. **Uses the best subscription** for display purposes
3. **Provides comprehensive access** to all available features

### Example Scenario
```
User has:
- Basic Plan: CREATE_PROJECT, UPDATE_PROJECT, SEND_MESSAGE
- Add-on Plan: GROUP_CHAT, UPLOAD_FILE

Combined Access: CREATE_PROJECT, UPDATE_PROJECT, SEND_MESSAGE, GROUP_CHAT, UPLOAD_FILE
```

## Error Messages

### Subscription Expired
```json
{
  "success": false,
  "message": "Access denied. You have 2 expired subscription(s). Please renew your subscription to continue using this feature."
}
```

### Feature Not Available
```json
{
  "success": false,
  "message": "Access denied. Your current subscription plan does not include the 'CREATE_PROJECT' feature. You have 1 active subscription(s) with 5 features. Please upgrade your plan to access this feature."
}
```

### Client Subscription Issue (for team members)
```json
{
  "success": false,
  "message": "Access denied. The client has 1 expired subscription(s). Please contact your client to renew the subscription."
}
```

## API Endpoints

### Subscription Status
```
GET /api/subscription/status
```
Returns current user's subscription status and available features.

### Detailed Subscription Info
```
GET /api/subscription/details
```
Returns detailed subscription information including expiring subscriptions.

### Feature Access Check
```
GET /api/subscription/feature/:feature/access
```
Checks if user has access to a specific feature.

### Available Features
```
GET /api/subscription/features
```
Returns all available features and user's access status.

### Update Expired Subscriptions (Admin)
```
POST /api/subscription/update-expired
```
Manually updates expired subscriptions (admin only).

## Package Feature Sets

### BASIC
- CREATE_PROJECT, UPDATE_PROJECT
- CREATE_TASK, UPDATE_TASK
- CREATE_SELF_TASK, UPDATE_SELF_TASK
- SEND_MESSAGE, UPLOAD_FILE

### STANDARD
- All BASIC features +
- DELETE_PROJECT, DELETE_TASK, DELETE_SELF_TASK
- INVITE_COWORKER, MANAGE_COWORKER
- ASSIGN_TASK, GROUP_CHAT

### PREMIUM
- All STANDARD features +
- UPDATE_COWORKER_PERMISSION
- BULK_OPERATIONS, ADVANCED_REPORTING
- API_ACCESS, UNLIMITED_PROJECTS, UNLIMITED_TASKS

### ENTERPRISE
- All PREMIUM features +
- CUSTOM_INTEGRATIONS
- UNLIMITED_STORAGE, UNLIMITED_COWORKERS

## Setup Instructions

### 1. Initialize Features
Run the feature initialization script to set up default features for existing packages:

```bash
npx ts-node src/scripts/initialize-features.ts
```

### 2. Configure Package Features
Use the admin interface or API to assign features to packages:

```typescript
// Example: Create a feature and assign to packages
await packageService.createFeature({
  rule: "CREATE_PROJECT",
  packageIds: ["package-id-1", "package-id-2"]
});
```

### 3. Apply Middleware to Routes
Add subscription middleware to protected routes:

```typescript
// Single feature requirement
router.post(
  "/protected-endpoint",
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  subscriptionMiddleware.requireFeatureAccess(FEATURES.FEATURE_NAME),
  asyncHandler(controller.method)
);

// Multiple features required (ALL)
router.post(
  "/advanced-endpoint",
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  subscriptionMiddleware.requireAllFeatures([
    FEATURES.CREATE_PROJECT,
    FEATURES.ASSIGN_TASK,
    FEATURES.BULK_OPERATIONS
  ]),
  asyncHandler(controller.advancedMethod)
);

// Alternative features (ANY)
router.post(
  "/flexible-endpoint",
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  subscriptionMiddleware.requireAnyFeature([
    FEATURES.CREATE_TASK,
    FEATURES.CREATE_SELF_TASK
  ]),
  asyncHandler(controller.flexibleMethod)
);

// Read-only with warning
router.get(
  "/dashboard",
  authMiddleware,
  subscriptionMiddleware.checkSubscriptionWithWarning,
  asyncHandler(controller.getDashboard)
);
```

## Monitoring and Maintenance

### Automatic Expiry Updates
The system includes a batch update function to mark expired subscriptions:

```typescript
await subscriptionService.updateExpiredSubscriptions();
```

### Subscription Monitoring
Monitor subscription status through the admin dashboard or API endpoints.

### Feature Usage Analytics
Track feature usage to understand which features are most valuable to users.

## Best Practices

1. **Always use feature constants** instead of hardcoded strings
2. **Apply subscription middleware** to all paid features
3. **Provide clear error messages** explaining why access is denied
4. **Use appropriate middleware type** based on the operation criticality
5. **Test with multiple subscription scenarios** including expired, multiple active, and no subscriptions
6. **Monitor subscription expiry** and send notifications before expiry

## Troubleshooting

### Common Issues

1. **User can't access feature despite active subscription**
   - Check if feature is assigned to their package
   - Verify subscription status and end date
   - Check if user role is correctly configured

2. **Team member access issues**
   - Verify client owner relationship
   - Check client's subscription status
   - Ensure proper role assignment

3. **Multiple subscriptions not combining features**
   - Check if all subscriptions are active
   - Verify feature assignments in packages
   - Review subscription end dates

### Debug Commands

```typescript
// Check user's subscription summary
const summary = await subscriptionService.getSubscriptionSummary(userId);

// Check specific feature access
const hasAccess = await subscriptionService.checkFeatureAccess(userId, featureName);

// Get detailed subscription info
const details = await subscriptionService.getDetailedSubscriptionInfo(userId);
```