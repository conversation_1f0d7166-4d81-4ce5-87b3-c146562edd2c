# Zoho Desk SSO Integration

This document explains how to set up and use Single Sign-On (SSO) with Zoho Desk.

## Overview

Single Sign-On (SSO) allows users to access Zoho Desk using their existing credentials from your application. When a user is logged into your application, they can access Zoho Desk without having to log in again.

## Prerequisites

Before using SSO with Zoho Desk, you need to:

1. Have a Zoho Desk account with SSO enabled
2. Configure SSO in your Zoho Desk portal settings
3. Get your SSO key and portal ID from Zoho Desk

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```
ZOHO_DESK_PORTAL_ID=your_portal_id
ZOHO_DESK_SSO_KEY=your_sso_key
ZOHO_DESK_SSO_AUTH_URL=https://desk.zoho.in/support/login
```

### Zoho Desk Portal Configuration

1. Log in to your Zoho Desk portal as an administrator
2. Go to Setup > Channels > Customer Portal > Portal Settings
3. Enable SSO and configure the settings according to <PERSON><PERSON><PERSON>'s documentation
4. Save your SSO key securely

## Usage

### Backend API

The SSO functionality is exposed through the following endpoint:

```
GET /api/zoho-desk/sso
```

This endpoint requires authentication and returns an SSO URL that can be used to redirect users to Zoho Desk.

#### Query Parameters

- `redirectTo` (optional): The path within Zoho Desk to redirect to after login

#### Response

```json
{
  "status": "success",
  "message": "SSO URL generated successfully",
  "data": {
    "ssoUrl": "https://desk.zoho.in/support/login?portal=your_portal&EMAIL=<EMAIL>&FIRSTNAME=John&LASTNAME=Doe&CUSTOMERID=123&TIMESTAMP=1623456789&ssotoken=abcdef123456"
  }
}
```

### Frontend Integration

You can use the provided `ZohoSSOButton` component to add SSO functionality to your frontend:

```jsx
import ZohoSSOButton from './ZohoSSOButton';
import './ZohoSSOButton.css';

function Dashboard() {
  const authToken = localStorage.getItem('authToken');
  
  return (
    <div className="dashboard">
      <h1>Your Dashboard</h1>
      
      {/* Other dashboard content */}
      
      <ZohoSSOButton 
        authToken={authToken} 
        buttonText="Access Support Portal" 
      />
    </div>
  );
}
```

## How It Works

1. When a user clicks the SSO button, the frontend makes a request to `/api/zoho-desk/sso`
2. The backend verifies the user's authentication and generates an SSO URL with the user's information
3. The frontend redirects the user to the SSO URL
4. Zoho Desk verifies the SSO token and logs the user in automatically

## Security Considerations

- The SSO key should be kept secure and never exposed to the frontend
- Use HTTPS for all communications to prevent token interception
- Set appropriate expiration times for SSO tokens
- Regularly rotate your SSO keys

## Troubleshooting

### Common Issues

1. **Invalid SSO Token**: Ensure your system clock is synchronized and the SSO key is correct
2. **User Not Found**: Make sure the email address matches a contact in Zoho Desk
3. **SSO Not Enabled**: Verify that SSO is enabled in your Zoho Desk portal settings

### Debugging

If you encounter issues with SSO, check the following:

1. Verify that the environment variables are set correctly
2. Check the network requests to see if the SSO URL is being generated correctly
3. Look for any error messages in the browser console or server logs
4. Verify that the user's email address matches a contact in Zoho Desk