import { Server, Socket } from "socket.io";
import { PrismaClient, NotificationType } from "@prisma/client";
import { ClientAuthService } from "../services/auth.service";
import jwt from "jsonwebtoken";

const authService = new ClientAuthService();
const prisma = new PrismaClient();

// userId => socketId
export const onlineUsers = new Map<string, string>();

const { getUserById } = authService;

export class TaskSocket {
  io: Server;

  constructor(io: Server) {
    this.io = io;
    this.configureSocket();
  }

  private getUserSocketId(userId: string): string | undefined {
    return onlineUsers.get(userId);
  }

  private configureSocket() {
    this.io.on("connection", (socket: Socket) => {
      console.log("socket is connected");
      socket.on("user_connected", async (token: string) => {
        try {
          if (!token) {
            return socket.emit("error", { message: "Token is required" });
          }

          const decoded: any = jwt.verify(token, process.env.JWT_SECRET!);
          const userId = decoded.userId;

          if (!userId) {
            return socket.emit("error", { message: "Invalid token" });
          }

          const user = await getUserById(userId);
          if (!user) {
            return socket.emit("error", { message: "User not found" });
          }

          // Store socket association
          onlineUsers.set(userId, socket.id);

          // Notify the user they're online
          socket.emit("connected_success", { userId });

          // Broadcast to others that this user is online
          socket.broadcast.emit("user_online", userId);

          // Send list of other online users to this user
          const onlineUserIds = Array.from(onlineUsers.keys()).filter(
            (id) => id !== userId
          );
          socket.emit("online_users", onlineUserIds);

          console.log(`User ${userId} (${socket.id}) is online`);
        } catch (err) {
          console.error("Auth error:", err);
          socket.emit("error", { message: "Authentication failed" });
        }
      });

      // User Connects
      socket.on("setup", ({ userId }: { userId: string }) => {
        onlineUsers.set(userId, socket.id);
        console.log(`User ${userId} connected with socket ${socket.id}`);
      });

      // Join a direct message conversation
      socket.on("join_dm", async ({ userId, otherUserId }) => {
        try {
          if (!userId || !otherUserId) {
            return socket.emit("error", {
              message: "Both user IDs are required",
            });
          }

          // Find existing DM conversation between these users
          const conversation = await prisma.conversation.findFirst({
            where: {
              isGroup: false,
              participants: {
                every: {
                  userId: { in: [userId, otherUserId] },
                },
              },
              AND: [
                {
                  participants: {
                    some: { userId },
                  },
                },
                {
                  participants: {
                    some: { userId: otherUserId },
                  },
                },
              ],
            },
            include: {
              participants: {
                include: { user: true },
              },
              messages: {
                orderBy: { createdAt: "desc" },
                take: 20,
                include: {
                  sender: true,
                  reactions: true,
                },
              },
            },
          });

          // Create new DM if not exists
          let dmConversation = conversation;
          if (!conversation) {
            dmConversation = await prisma.conversation.create({
              data: {
                isGroup: false,
                participants: {
                  create: [{ userId }, { userId: otherUserId }],
                },
              },
              include: {
                participants: {
                  include: { user: true },
                },
                messages: {
                  include: {
                    sender: true,
                    reactions: true,
                  },
                },
              },
            });
            console.log(`Created new DM conversation: ${dmConversation.id}`);
          }

          // Join the socket room
          socket.join(dmConversation!.id);
          console.log(
            `User ${userId} joined DM conversation ${dmConversation!.id}`
          );

          // Return conversation data
          socket.emit("joined_dm", {
            conversation: dmConversation,
          });
        } catch (err) {
          console.error("Error joining DM:", err);
          socket.emit("error", {
            message: "Error joining direct message conversation",
          });
        }
      });

      socket.on("load_more_messages", async ({ conversationId, page = 1 }) => {
        try {
          const PAGE_SIZE = 20;
          const skipCount = (page - 1) * PAGE_SIZE;

          if (!conversationId) {
            return socket.emit("error", {
              message: "Conversation ID is required",
            });
          }

          // Fetch paginated messages from the DB
          const messages = await prisma.message.findMany({
            where: { conversationId },
            orderBy: { createdAt: "desc" },
            skip: skipCount,
            take: PAGE_SIZE,
            include: {
              sender: true,
              receiver: true,
              replyTo: true,
              reactions: true,
            },
          });

          // Emit the messages back to the requester
          socket.emit("loaded_more_messages", {
            messages,
            page,
          });
        } catch (err) {
          console.error("Error loading more messages:", err);
          socket.emit("error", {
            message: "Failed to load more messages",
          });
        }
      });

      // Leave a conversation (for DMs)
      socket.on("leave_conversation", async ({ conversationId, userId }) => {
        try {
          // if (!conversationId || !userId) {
          //   return socket.emit("error", {
          //     message: "Conversation ID and User ID are required",
          //   });
          // }

          const conversation = await prisma.conversation.findUnique({
            where: { id: conversationId },
            include: { participants: true },
          });

          if (!conversation) {
            return socket.emit("error", { message: "Conversation not found" });
          }

          const isParticipant = conversation.participants.some(
            (p) => p.userId === userId
          );
          // if (!isParticipant) {
          //   return socket.emit("error", {
          //     message: "User is not a participant in this conversation",
          //   });
          // }

          // For DMs, we might just want to leave the socket room but keep the participant
          // socket.leave(conversationId);

          // For actual leaving, remove the participant
          await prisma.conversationParticipant.delete({
            where: {
              userId_conversationId: {
                conversationId,
                userId,
              },
            },
          });

          // Leave the socket room
          socket.leave(conversationId);
          console.log(`User ${userId} left conversation ${conversationId}`);

          // Notify other participants
          socket.to(conversationId).emit("user_left_conversation", {
            conversationId,
            userId,
          });

          // Confirm to the user
          socket.emit("left_conversation", { conversationId });
        } catch (err) {
          console.error("Error leaving conversation:", err);
          // socket.emit("error", { message: "Error leaving conversation" });
        }
      });

      // socket.on("create_group", async ({ creatorId, groupName, memberIds }) => {
      //   try {
      //     if (!creatorId || !groupName || !memberIds || !Array.isArray(memberIds)) {
      //       return socket.emit("error", {
      //         message: "Creator ID, Group Name, and Member IDs are required",
      //       });
      //     }

      //     // Step 1: Create the group
      //     const newGroup = await prisma.groupChat.create({
      //       data: {
      //         name: groupName,
      //         creatorId, // optional: store who created the group
      //         members: {
      //           create: memberIds.map((id) => ({
      //             userId: id,
      //           })),
      //         },
      //       },
      //       include: {
      //         members: {
      //           include: {
      //             user: true, // if you want to get user details
      //           },
      //         },
      //       },
      //     });

      //     console.log(`Group ${newGroup.id} created by user ${creatorId}`);

      //     // Step 2: Join all members to the group socket room (if they are online)
      //     memberIds.forEach((id) => {
      //       const memberSocket = this.getUserSocketId(id); // you need to implement this
      //       if (memberSocket) {
      //         memberSocket.join(`group:${newGroup.id}`);
      //         memberSocket.emit("added_to_group", {
      //           group: newGroup,
      //         });
      //       }
      //     });

      //     // Step 3: Confirm group created to creator
      //     socket.emit("group_created", {
      //       group: newGroup,
      //     });

      //   } catch (err) {
      //     console.error("Error creating group:", err);
      //     socket.emit("error", { message: "Error creating group" });
      //   }
      // });

      // Join a group chat
      socket.on("join_group", async ({ userId, groupId }) => {
        try {
          if (!userId || !groupId) {
            return socket.emit("error", {
              message: "User ID and Group ID are required",
            });
          }

          // Check if group exists
          const group = await prisma.groupChat.findUnique({
            where: { id: groupId },
            include: {
              members: true,
              messages: {
                orderBy: { createdAt: "desc" },
                take: 20,
                include: {
                  sender: true,
                  reactions: true,
                },
              },
            },
          });

          if (!group) {
            return socket.emit("error", { message: "Group not found" });
          }

          console.log(group, "group data");
          // Check if user is a member of the group
          const isMember = group.members.some(
            (member) => member.userId === userId
          );
          if (!isMember) {
            return socket.emit("error", {
              message: "User is not a member of this group",
            });
          }

          // Join the group socket room
          socket.join(`group:${groupId}`);

          console.log(`User ${userId} joined group ${groupId}`);

          // Return group data
          socket.emit("joined_group", {
            group,
          });

          // Notify others in the group
          socket.to(`group:${groupId}`).emit("user_joined_group", {
            groupId,
            userId,
          });
        } catch (err) {
          console.error("Error joining group:", err);
          socket.emit("error", { message: "Error joining group chat" });
        }
      });

      // Leave a group chat
      socket.on("leave_group", async ({ groupId, userId }) => {
        try {
          if (!groupId || !userId) {
            return socket.emit("error", {
              message: "Group ID and User ID are required",
            });
          }

          const groupMember = await prisma.groupMember.findUnique({
            where: {
              userId_groupId: {
                userId,
                groupId,
              },
            },
          });

          if (!groupMember) {
            return socket.emit("error", {
              message: "User is not a member of this group",
            });
          }

          // Remove user from group
          await prisma.groupMember.delete({
            where: {
              userId_groupId: {
                userId,
                groupId,
              },
            },
          });

          // Leave the group socket room
          socket.leave(`group:${groupId}`);

          console.log(`User ${userId} left group ${groupId}`);

          // Notify other members
          socket.to(`group:${groupId}`).emit("user_left_group", {
            groupId,
            userId,
          });

          // Confirm to the user
          socket.emit("left_group", { groupId });
        } catch (err) {
          console.error("Error leaving group:", err);
          socket.emit("error", { message: "Error leaving group" });
        }
      });

      // Send direct message
      socket.on(
        "send_dm",
        async ({
          text,
          fileUrl,
          fileType,
          conversationId,
          senderId,
          receiverId,
          replyToId,
        }) => {
          try {
            // Validate required fields
            // if (!text && !fileUrl) {
            //   console.log("Validation failed: Either text or file is required");
            //   return socket.emit("error", {
            //     message: "Either text or file is required",
            //   });
            // }

            if (!conversationId || !senderId || !receiverId) {
              console.log("Validation failed: Missing required IDs");
              return socket.emit("error", {
                message:
                  "Conversation ID, sender ID, and receiver ID are required",
              });
            }

            // Ensure the sender is part of the conversation
            const participant = await prisma.conversationParticipant.findUnique(
              {
                where: {
                  userId_conversationId: {
                    conversationId,
                    userId: senderId,
                  },
                },
              }
            );

            console.log("Participant check result:", participant);

            if (!participant) {
              console.log("Participant validation failed for:", {
                conversationId,
                userId: senderId,
              });
              return socket.emit("error", {
                message: "User is not part of the conversation",
              });
            }

            // Create the message
            const message = await prisma.message.create({
              data: {
                text,
                fileUrl,
                fileType: fileType || undefined,
                senderId,
                receiverId,
                conversationId,
                replyToId: replyToId || undefined,
              },
              include: {
                sender: true,
                receiver: true,
                replyTo: true,
                reactions: true,
              },
            });

            console.log("Message saved to database:", message);

            // Send to conversation room
            this.io.to(conversationId).emit("new_message", message);

            // 2. Emit notification directly to receiver
            const receiverSocketId = this.getUserSocketId(receiverId); // You need to implement this function

            console.log(receiverSocketId, "receiver socket id");
            if (receiverSocketId) {
              this.io.to(receiverSocketId).emit("dm_notification", {
                title: "New Message",
                body: text ? text : "You received a new file",
                conversationId,
                sender: message.sender,
                messageId: message.id,
              });
            }

            // Update conversation's last activity
            await prisma.conversation.update({
              where: { id: conversationId },
              data: { updatedAt: new Date() },
            });

            console.log(
              `DM successfully sent in conversation ${conversationId}`
            );
          } catch (error) {
            console.error("Error sending DM:", error);
            //socket.emit("error", { message: "Error sending direct message"});
          }
        }
      );

      // Send group message
      socket.on(
        "send_group_message",
        async ({ text, fileUrl, fileType, groupId, senderId, replyToId }) => {
          try {
            // Validate required fields
            // if (!text && !fileUrl) {
            //   return socket.emit("error", {
            //     message: "Either text or file is required",
            //   });
            // }

            if (!groupId || !senderId) {
              return socket.emit("error", {
                message: "Group ID and sender ID are required",
              });
            }

            // Check if user is member of the group
            const member = await prisma.groupMember.findUnique({
              where: {
                userId_groupId: {
                  userId: senderId,
                  groupId,
                },
              },
            });

            if (!member) {
              return socket.emit("error", {
                message: "User is not a member of this group",
              });
            }

            // Fetch group and sender details
            const group = await prisma.groupChat.findUnique({
              where: { id: groupId },
              select: { name: true },
            });
            const sender = await prisma.user.findUnique({
              where: { id: senderId },
              select: { name: true },
            });

            // Parse mentions
            const mentionRegex = /@(\w+)/g;
            const mentionedUsernames = [
              ...(text?.matchAll(mentionRegex) || []),
            ].map((match) => match[1]);
            const mentionedUsers = await prisma.user.findMany({
              where: {
                name: { in: mentionedUsernames },
                GroupMember: { some: { groupId } },
              },
              select: { id: true, name: true },
            });

            // Create the message
            const message = await prisma.message.create({
              data: {
                text,
                fileUrl,
                fileType: fileType || undefined,
                senderId,
                groupId,
                replyToId: replyToId || undefined,
                mentions: {
                  create: mentionedUsers.map((user) => ({
                    userId: user.id,
                  })),
                },
              },
              include: {
                sender: true,
                replyTo: true,
                reactions: true,
                mentions: { include: { user: true } },
              },
            });

            // Send to group room
            this.io.to(`group:${groupId}`).emit("new_group_message", message);
            console.log(`Group message sent in group ${groupId}`);

            // Save and emit mention notifications
            for (const mentionedUser of mentionedUsers) {
              if (mentionedUser.id !== senderId) {
                // Save to Notification model
                await prisma.notification.create({
                  data: {
                    userId: mentionedUser.id,
                    type: "GROUP_MESSAGE",
                    message: `You were mentioned by ${sender?.name} in ${group?.name}`,
                    metadata: {
                      groupId,
                      messageId: message.id,
                      senderName: sender?.name,
                      groupName: group?.name,
                      messageText: text,
                      isMention: true,
                    },
                    isRead: false,
                  },
                });

                // Emit real-time notification
                const mentionedSocketId = this.getUserSocketId(
                  mentionedUser.id
                );
                if (mentionedSocketId) {
                  this.io.to(mentionedSocketId).emit("mention_notification", {
                    type: "MENTION",
                    groupId,
                    messageId: message.id,
                    content: `You were mentioned by ${sender?.name} in ${group?.name}`,
                    senderName: sender?.name,
                    groupName: group?.name,
                    messageText: text,
                  });
                }
              }
            }

            // Save and emit group message notifications to all members (except sender)
            const groupMembers = await prisma.groupMember.findMany({
              where: {
                groupId,
                NOT: { userId: senderId },
              },
              select: { userId: true },
            });

            for (const member of groupMembers) {
              // Save to Notification model
              await prisma.notification.create({
                data: {
                  userId: member.userId,
                  type: "GROUP_MESSAGE",
                  message: `New message in ${group?.name}`,
                  metadata: {
                    groupId,
                    messageId: message.id,
                    senderName: sender?.name,
                    groupName: group?.name,
                    messageText: text || "New file message",
                  },
                  isRead: false,
                },
              });

              // Emit real-time notification
              const memberSocketId = this.getUserSocketId(member.userId);
              if (memberSocketId) {
                this.io.to(memberSocketId).emit("new_notification", {
                  type: "GROUP_MESSAGE",
                  groupId,
                  messageId: message.id,
                  content: text || "You have a new file message",
                  senderName: sender?.name,
                  groupName: group?.name,
                  messageText: text || "New file message",
                });
              }
            }
          } catch (err) {
            console.error("Error sending group message:", err);
            // socket.emit("error", { message: "Error sending group message" });
          }
        }
      );

      // Add reaction to message
      socket.on("add_reaction", async ({ messageId, userId, emoji }) => {
        try {
          if (!messageId || !userId || !emoji) {
            return socket.emit("error", {
              message: "Message ID, User ID and emoji are required",
            });
          }

          const message = await prisma.message.findUnique({
            where: { id: messageId },
          });

          if (!message) {
            return socket.emit("error", { message: "Message not found" });
          }

          // Create reaction
          const reaction = await prisma.reaction.create({
            data: {
              messageId,
              userId,
              emoji,
            },
            include: { user: true },
          });

          // Emit to appropriate room
          if (message.groupId) {
            this.io.to(`group:${message.groupId}`).emit("message_reaction", {
              messageId,
              reaction,
            });
          } else if (message.conversationId) {
            this.io.to(message.conversationId).emit("message_reaction", {
              messageId,
              reaction,
            });
          }

          console.log(`Reaction added to message ${messageId}`);
        } catch (err) {
          console.error("Error adding reaction:", err);
          socket.emit("error", { message: "Error adding reaction" });
        }
      });

      // Remove reaction from message
      socket.on("remove_reaction", async ({ messageId, userId, emoji }) => {
        try {
          if (!messageId || !userId || !emoji) {
            return socket.emit("error", {
              message: "Message ID, User ID and emoji are required",
            });
          }

          const message = await prisma.message.findUnique({
            where: { id: messageId },
          });

          if (!message) {
            return socket.emit("error", { message: "Message not found" });
          }

          // Delete reaction
          await prisma.reaction.delete({
            where: {
              userId_messageId_emoji: {
                userId,
                messageId,
                emoji,
              },
            },
          });

          // Emit to appropriate room
          if (message.groupId) {
            this.io.to(`group:${message.groupId}`).emit("reaction_removed", {
              messageId,
              userId,
              emoji,
            });
          } else if (message.conversationId) {
            this.io.to(message.conversationId).emit("reaction_removed", {
              messageId,
              userId,
              emoji,
            });
          }

          console.log(`Reaction removed from message ${messageId}`);
        } catch (err) {
          console.error("Error removing reaction:", err);
          socket.emit("error", { message: "Error removing reaction" });
        }
      });

      // Mark message as read (custom implementation without MessageReadStatus table)
      socket.on("mark_read", async ({ messageId, userId }) => {
        try {
          if (!messageId || !userId) {
            return socket.emit("error", {
              message: "Message ID and User ID are required",
            });
          }

          const message = await prisma.message.findUnique({
            where: { id: messageId },
          });

          if (!message) {
            return socket.emit("error", { message: "Message not found" });
          }

          // Since there's no MessageReadStatus table in your schema,
          // we'll just emit the event without persisting the read status
          // You might want to create this table in your schema

          // Emit to appropriate room
          if (message.groupId) {
            this.io.to(`group:${message.groupId}`).emit("message_read", {
              messageId,
              userId,
              readAt: new Date(),
            });
          } else if (message.conversationId) {
            this.io.to(message.conversationId).emit("message_read", {
              messageId,
              userId,
              readAt: new Date(),
            });
          }

          console.log(`Message ${messageId} marked as read by user ${userId}`);
        } catch (err) {
          console.error("Error marking message as read:", err);
          socket.emit("error", { message: "Error marking message as read" });
        }
      });

      // Typing indicator
      socket.on("typing", ({ conversationId, groupId, userId, isTyping }) => {
        if (!userId || (!conversationId && !groupId)) return;

        if (conversationId) {
          // Broadcast to others in the conversation
          socket.to(conversationId).emit("user_typing", {
            conversationId,
            userId,
            isTyping,
          });
        } else if (groupId) {
          // Broadcast to others in the group
          socket.to(`group:${groupId}`).emit("user_typing", {
            groupId,
            userId,
            isTyping,
          });
        }
      });

      // Forward message
      socket.on(
        "forward_message",
        async ({ messageId, senderId, targetId, isGroup }) => {
          try {
            if (!messageId || !senderId || !targetId) {
              return socket.emit("error", {
                message:
                  "Original message ID, sender ID, and target ID are required",
              });
            }

            const originalMessage = await prisma.message.findUnique({
              where: { id: messageId },
              include: { sender: true },
            });

            if (!originalMessage) {
              return socket.emit("error", {
                message: "Original message not found",
              });
            }

            let newMessage;

            if (isGroup) {
              // Check if user is member of the target group
              const isMember = await prisma.groupMember.findUnique({
                where: {
                  userId_groupId: {
                    userId: senderId,
                    groupId: targetId,
                  },
                },
              });

              if (!isMember) {
                return socket.emit("error", {
                  message: "User is not a member of the target group",
                });
              }

              // Create forwarded message to group
              newMessage = await prisma.message.create({
                data: {
                  text: originalMessage.text,
                  fileUrl: originalMessage.fileUrl,
                  fileType: originalMessage.fileType,
                  senderId,
                  groupId: targetId,
                  forwardedFromId: messageId,
                },
                include: {
                  sender: true,
                  forwardedFrom: {
                    include: { sender: true },
                  },
                },
              });

              // Notify group
              this.io
                .to(`group:${targetId}`)
                .emit("new_group_message", newMessage);
            } else {
              // Find or create conversation with target user
              const conversation = await prisma.conversation.findFirst({
                where: {
                  isGroup: false,
                  participants: {
                    every: {
                      userId: { in: [senderId, targetId] },
                    },
                  },
                  AND: [
                    { participants: { some: { userId: senderId } } },
                    { participants: { some: { userId: targetId } } },
                  ],
                },
              });

              let conversationId;

              if (conversation) {
                conversationId = conversation.id;
              } else {
                // Create new conversation
                const newConversation = await prisma.conversation.create({
                  data: {
                    isGroup: false,
                    participants: {
                      create: [{ userId: senderId }, { userId: targetId }],
                    },
                  },
                });
                conversationId = newConversation.id;
              }

              // Create forwarded message
              newMessage = await prisma.message.create({
                data: {
                  text: originalMessage.text,
                  fileUrl: originalMessage.fileUrl,
                  fileType: originalMessage.fileType,
                  senderId,
                  receiverId: targetId,
                  conversationId,
                  forwardedFromId: messageId,
                },
                include: {
                  sender: true,
                  forwardedFrom: {
                    include: { sender: true },
                  },
                },
              });

              // Notify conversation
              this.io.to(conversationId).emit("new_message", newMessage);
            }

            console.log(
              `Message ${messageId} forwarded to ${
                isGroup ? "group" : "user"
              } ${targetId}`
            );
          } catch (err) {
            console.error("Error forwarding message:", err);
            socket.emit("error", { message: "Error forwarding message" });
          }
        }
      );

      // Disconnect logic
      socket.on("disconnect", () => {
        let disconnectedUserId: string | null = null;

        for (const [userId, sockId] of onlineUsers.entries()) {
          if (sockId === socket.id) {
            disconnectedUserId = userId;
            onlineUsers.delete(userId);
            break;
          }
        }

        if (disconnectedUserId) {
          // Only emit user_offline event, but don't remove from groups
          this.io.emit("user_offline", disconnectedUserId);
          console.log(`User ${disconnectedUserId} disconnected (socket only)`);
        }
      });
    });
  }
}
