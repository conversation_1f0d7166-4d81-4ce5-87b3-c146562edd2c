/**
 * Test script for subscription cancellation API
 * 
 * Usage: node test-subscription-cancellation.js
 */

const axios = require('axios');
require('dotenv').config();

async function testSubscriptionCancellation() {
  try {
    const baseUrl = process.env.API_BASE_URL || 'http://localhost:3000';
    const authToken = process.env.TEST_AUTH_TOKEN; // Client token
    const adminToken = process.env.TEST_ADMIN_TOKEN; // Admin token
    const testSubscriptionId = process.env.TEST_SUBSCRIPTION_ID; // Subscription to test with
    
    if (!authToken || !testSubscriptionId) {
      console.log('Please set TEST_AUTH_TOKEN and TEST_SUBSCRIPTION_ID in your .env file');
      console.log('Optionally set TEST_ADMIN_TOKEN for admin tests');
      return;
    }

    const clientHeaders = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    };

    const adminHeaders = adminToken ? {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    } : null;

    console.log('Testing subscription cancellation API...\n');
    
    // Test 1: Get subscription details (client)
    console.log('1. Testing GET subscription details (client)');
    try {
      const response = await axios.get(`${baseUrl}/api/billings/subscription/${testSubscriptionId}`, { 
        headers: clientHeaders 
      });
      console.log('✅ Client Subscription Details Success:', response.status);
      console.log('Subscription Status:', response.data.data.status);
      console.log('Package:', response.data.data.package.name);
      console.log('User:', response.data.data.user.name);
    } catch (error) {
      console.log('❌ Client Subscription Details Error:', error.response?.status, error.response?.data?.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Get subscription details (admin)
    if (adminHeaders) {
      console.log('2. Testing GET subscription details (admin)');
      try {
        const response = await axios.get(`${baseUrl}/api/billings/admin/subscription/${testSubscriptionId}`, { 
          headers: adminHeaders 
        });
        console.log('✅ Admin Subscription Details Success:', response.status);
        console.log('Subscription Status:', response.data.data.status);
        console.log('Package:', response.data.data.package.name);
        console.log('User:', response.data.data.user.name);
        console.log('Recent Payments:', response.data.data.Payment.length);
      } catch (error) {
        console.log('❌ Admin Subscription Details Error:', error.response?.status, error.response?.data?.message);
      }

      console.log('\n' + '='.repeat(50) + '\n');
    }

    // Test 3: Cancel subscription (client)
    console.log('3. Testing subscription cancellation (client)');
    const cancellationReason = "Testing subscription cancellation functionality";
    
    try {
      const response = await axios.post(
        `${baseUrl}/api/billings/subscription/${testSubscriptionId}/cancel`,
        { reason: cancellationReason },
        { headers: clientHeaders }
      );
      console.log('✅ Client Cancellation Success:', response.status);
      console.log('Message:', response.data.message);
      console.log('New Status:', response.data.data.subscription.status);
      console.log('Cancellation Details:', JSON.stringify(response.data.data.cancellationDetails, null, 2));
    } catch (error) {
      console.log('❌ Client Cancellation Error:', error.response?.status, error.response?.data?.message);
      
      if (error.response?.data?.message?.includes('already cancelled')) {
        console.log('ℹ️  Subscription is already cancelled - this is expected if running multiple tests');
      }
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 4: Try to cancel again (should fail)
    console.log('4. Testing duplicate cancellation (should fail)');
    try {
      const response = await axios.post(
        `${baseUrl}/api/billings/subscription/${testSubscriptionId}/cancel`,
        { reason: "Duplicate cancellation test" },
        { headers: clientHeaders }
      );
      console.log('❌ Duplicate cancellation should have failed but succeeded:', response.status);
    } catch (error) {
      if (error.response?.data?.message?.includes('already cancelled')) {
        console.log('✅ Duplicate cancellation correctly rejected:', error.response.data.message);
      } else {
        console.log('❌ Unexpected error:', error.response?.status, error.response?.data?.message);
      }
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 5: Admin cancellation (if admin token available)
    if (adminHeaders) {
      console.log('5. Testing admin cancellation (different subscription)');
      const adminTestSubscriptionId = process.env.TEST_ADMIN_SUBSCRIPTION_ID || testSubscriptionId;
      
      try {
        const response = await axios.post(
          `${baseUrl}/api/billings/admin/subscription/${adminTestSubscriptionId}/cancel`,
          { reason: "Admin cancellation test" },
          { headers: adminHeaders }
        );
        console.log('✅ Admin Cancellation Success:', response.status);
        console.log('Message:', response.data.message);
        console.log('New Status:', response.data.data.subscription.status);
      } catch (error) {
        if (error.response?.data?.message?.includes('already cancelled')) {
          console.log('ℹ️  Admin cancellation: Subscription already cancelled');
        } else {
          console.log('❌ Admin Cancellation Error:', error.response?.status, error.response?.data?.message);
        }
      }

      console.log('\n' + '='.repeat(50) + '\n');
    }

    // Test 6: Verify final status
    console.log('6. Verifying final subscription status');
    try {
      const response = await axios.get(`${baseUrl}/api/billings/subscription/${testSubscriptionId}`, { 
        headers: clientHeaders 
      });
      console.log('✅ Final Status Check Success');
      console.log('Final Status:', response.data.data.status);
      console.log('End Date:', response.data.data.endDate);
    } catch (error) {
      console.log('❌ Final Status Check Error:', error.response?.status, error.response?.data?.message);
    }

    console.log('\n✅ All tests completed!');
    console.log('\n📋 Test Summary:');
    console.log('- Subscription details retrieval (client & admin)');
    console.log('- Subscription cancellation (client & admin)');
    console.log('- External provider API integration');
    console.log('- Duplicate cancellation prevention');
    console.log('- Status validation and updates');
    
  } catch (error) {
    console.error('❌ Test setup error:', error.message);
  }
}

// Test different error scenarios
async function testErrorScenarios() {
  console.log('\n' + '='.repeat(60));
  console.log('TESTING ERROR SCENARIOS');
  console.log('='.repeat(60) + '\n');
  
  const baseUrl = process.env.API_BASE_URL || 'http://localhost:3000';
  const authToken = process.env.TEST_AUTH_TOKEN;
  
  if (!authToken) {
    console.log('Skipping error scenario tests - no auth token provided');
    return;
  }

  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };

  // Test 1: Invalid subscription ID
  console.log('1. Testing invalid subscription ID');
  try {
    await axios.post(
      `${baseUrl}/api/billings/subscription/invalid-id/cancel`,
      { reason: "Test" },
      { headers }
    );
    console.log('❌ Should have failed with invalid ID');
  } catch (error) {
    console.log('✅ Correctly rejected invalid ID:', error.response?.data?.message);
  }

  // Test 2: Missing subscription ID
  console.log('\n2. Testing missing subscription ID');
  try {
    await axios.post(
      `${baseUrl}/api/billings/subscription//cancel`,
      { reason: "Test" },
      { headers }
    );
    console.log('❌ Should have failed with missing ID');
  } catch (error) {
    console.log('✅ Correctly rejected missing ID:', error.response?.status);
  }

  // Test 3: Unauthorized access (no token)
  console.log('\n3. Testing unauthorized access');
  try {
    await axios.post(
      `${baseUrl}/api/billings/subscription/test-id/cancel`,
      { reason: "Test" }
    );
    console.log('❌ Should have failed without auth token');
  } catch (error) {
    console.log('✅ Correctly rejected unauthorized access:', error.response?.status);
  }

  console.log('\n✅ Error scenario tests completed!');
}

// Run the tests
testSubscriptionCancellation().then(() => {
  testErrorScenarios();
});