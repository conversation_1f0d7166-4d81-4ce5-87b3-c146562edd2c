generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// generator client {
//   provider        = "prisma-client-js"
//   previewFeatures = ["fullTextSearch", "fullTextIndex", "driverAdapters", "prismaSchemaFolder", "omitApi"]
// }

enum Role {
  ADMIN
  CLIENT
  COWORKER
  PROJECT_COORDINATOR
  ANNOTATOR
}

enum Priority {
  LOW
  MEDIUM
  HIGH
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
}

enum ProjectStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
}

enum AnnotatorStatus {
  ACTIVE
  BREAK
  INACTIVE
}

enum CoworkerPermission {
  EDIT
  VIEW
}

enum AccountStatus {
  ACTIVE
  SUSPENDED
  DELETED
}

model User {
  id                   String    @id @default(cuid())
  name                 String
  lastname             String?
  email                String    @unique
  passwordHash         String?
  emailVerified        DateTime?
  role                 Role      @default(CLIENT)
  domain               String?
  availableFrom        String? // Format: "09:00"
  availableTo          String? // Format: "18:00"
  timezone             String?
  industry             String?
  category             String?
  customer_id          String?
  business_id          String?
  isDeleted            Boolean   @default(false)
  invalidLoginAttempts Int       @default(0)

  description String?

  accountStatus  AccountStatus? @default(ACTIVE)
  suspendedUntil DateTime?

  clientOwnerId String? // only for Co-workers (refers to the client)
  clientOwner   User?   @relation("ClientCoWorkers", fields: [clientOwnerId], references: [id])
  coWorkers     User[]  @relation("ClientCoWorkers")

  assignedProjects ProjectCoordinatorAssignment[]
  projectsOwned    Project[]

  // Tasks created by user
  createdTasks   Task[] @relation(name: "CreatedTasks")
  annotatedTasks Task[] @relation("TaskAnnotators")

  selfTasks        SelfTask[]        @relation("SelfTasks")
  tokens           Token[]
  restrictedTokens RestrictedToken[]
  PartnerUser      PartnerUser[]

  annotatorStatus   AnnotatorStatus? // only for 
  annotatorProjects Project[]        @relation("AnnotatorProjects")
  timeLogs          TimeLog[]

  coworkerPermission CoworkerPermission?

  otpCode   String?
  otpExpiry DateTime?

  sentMessages     Message[] @relation("SentMessages")
  receivedMessages Message[] @relation("ReceivedMessages")

  createdAt                DateTime                  @default(now())
  updatedAt                DateTime                  @default(now())
  GroupMember              GroupMember[]
  Reaction                 Reaction[]
  MessageReadStatus        MessageReadStatus[]
  Subscription             Subscription[]
  UserProfile              UserProfile?
  UserSettings             UserSettings?
  UserActivity             UserActivity[]
  UserSession              UserSession[]
  UserNotification         UserNotification[]
  UserFeedback             UserFeedback[]
  UserActivityLog          UserActivityLog[]
  UserRole                 UserRole[]
  UserPermission           UserPermission[]
  packageId                String?
  // Package                  Package?                  @relation(fields: [packageId], references: [id])
  UserSessions             UserSessions[]
  ConversationParticipant  ConversationParticipant[]
  assignmentsAsClient      Assignment[]              @relation("ClientAssignments")
  assignmentsAsDeveloper   Assignment[]              @relation("DeveloperAssignments")
  assignmentsAsCoordinator Assignment[]              @relation("CoordinatorAssignments")

  projectsAsCoordinator Project[] @relation("ProjectCoordinator")

  Notification   Notification[]
  Payments       Payment[]
  Package        Package?            @relation(fields: [packageId], references: [id])
  attendanceLogs AttendanceSummary[]
  breakSessions  BreakSession[]      @relation("UserBreakSessions")

  CreatedAdminTasks    AdminTask[]            @relation(name: "CreatedAdminTasks")
  AssignedAdminTasks   AdminTask[]            @relation(name: "AssignedAdminTasks")
  createdById          String?
  createdBy            User?                  @relation("CreatedUsers", fields: [createdById], references: [id])
  createdUsers         User[]                 @relation("CreatedUsers")
  AuthSession          AuthSession[]
  // Client-side
  clientPackageDetails ClientPackageDetails[] @relation("ClientPackages")

  // Annotator-side
  assignedToClientPackages ClientPackageDetails[] @relation("AssignedAnnotators")

  // Shift change relations
  shiftRequestsAsAnnotator ShiftChangeRequest[] @relation("ShiftChange_Annotator")
  shiftRequestsAsRequester ShiftChangeRequest[] @relation("ShiftChange_RequestedBy")
  shiftRequestsAsApprover  ShiftChangeRequest[] @relation("ShiftChange_ApprovedBy")

  // Leave request relations
  leaveRequests  LeaveRequest[] @relation("AnnotatorLeaves")
  approvedLeaves LeaveRequest[] @relation("ApprovedLeaves")

  Profile               Profile?
  mentions              Mention[]
  BillingAddress        BillingAddress?
  subscriptionAlerts    SubscriptionAlert[]   @relation("UserSubscriptionAlerts")
  EmailLog              EmailLog[]
  verifiedBankTransfers BankTransferPayment[] @relation("BankTransferVerifier")
  BankTransferPayment   BankTransferPayment[]
}

model Profile {
  id            String   @id @default(cuid())
  userId        String   @unique
  user          User     @relation(fields: [userId], references: [id])
  companyName   String?
  phoneNumber   String?
  website       String?
  address       String?
  postalCode    String?
  country       String?
  stateProvince String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @default(now()) @updatedAt
}

model BillingAddress {
  id          String @id @default(cuid())
  userId      String @unique
  user        User   @relation(fields: [userId], references: [id])
  country     String
  address     String
  state       String
  postalCodel String
  street      String
}

model Payment {
  id                  String               @id @default(cuid())
  paymentId           String               @unique
  businessId          String?
  paymentLink         String?
  userId              String
  subscriptionId      String?
  dodoSubscriptionId  String?
  amount              Float
  currency            String               @default("JPY")
  paymentMethod       String
  status              PaymentStatus        @default(PENDING)
  rawResponse         Json
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  provider            Provider             @default(DODO)
  // New fields
  cardLastFour        String?
  cardNetwork         String?
  cardType            String?
  cardCountry         String?
  taxAmount           Float?
  settlementAmount    Float?
  settlementCurrency  String?
  billingInfo         Json?
  customerEmail       String?
  customerId          String?
  user                User                 @relation(fields: [userId], references: [id])
  subscription        Subscription?        @relation(fields: [subscriptionId], references: [id])
  error_message       String?
  BankTransferPayment BankTransferPayment?
}

enum Provider {
  DODO
  PAYPAL
  BANK_TRANSFER
}

enum PaymentStatus {
  PENDING
  SUCCESS
  FAILED
  REFUNDED
  CANCELLED
  PROCESSING
  DISPUTED
}

model Refund {
  id         String   @id @default(cuid()) // internal ID
  refundId   String   @unique // refund_id from Dodo
  paymentId  String // payment_id from Dodo
  businessId String // business_id from Dodo
  amount     Int // in minor currency units (e.g., fils or cents)
  currency   String // e.g., 'AED'
  reason     String? // nullable if no reason is provided
  status     String // e.g., 'succeeded', 'pending', etc.
  createdAt  DateTime // ISO 8601 timestamp from Dodo
  recordedAt DateTime @default(now()) // when this record was inserted locally

  @@map("refunds") // optional: maps this model to a table called 'refunds'
}

model TimeLog {
  id        String    @id @default(cuid())
  userId    String
  user      User      @relation(fields: [userId], references: [id])
  loginAt   DateTime
  logoutAt  DateTime?
  createdAt DateTime  @default(now())
}

model AttendanceSummary {
  id     String   @id @default(uuid())
  userId String
  date   DateTime

  // timeIn        DateTime?
  // timeOut       DateTime?
  status        AttendanceStatus @default(NOT_CLOCKED_IN)
  arrivalStatus ArrivalStatus?

  breakMinutes   Int @default(0)
  workingMinutes Int @default(0)

  totalLeave     Int @default(0)
  availableLeave Int @default(0)
  consumedLeave  Int @default(0)

  totalBreak     Int @default(60)
  availableBreak Int @default(60)
  consumedBreak  Int @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user          User           @relation(fields: [userId], references: [id])
  breakSessions BreakSession[]
  clockSessions ClockSession[] // <--- add this line

  @@unique([userId, date])
}

model ClockSession {
  id                  String    @id @default(uuid())
  attendanceSummaryId String
  timeIn              DateTime
  timeOut             DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  attendanceSummary AttendanceSummary @relation(fields: [attendanceSummaryId], references: [id])
}

enum ArrivalStatus {
  ON_TIME
  MINUTES_LATE
  HOURS_LATE
  ABSENT
}

enum AttendanceStatus {
  NOT_CLOCKED_IN
  ACTIVE
  ON_BREAK
  CLOCKED_OUT
}

model BreakSession {
  id           String @id @default(uuid())
  userId       String
  attendanceId String

  startTime DateTime
  endTime   DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  attendance AttendanceSummary @relation(fields: [attendanceId], references: [id])
  user       User              @relation("UserBreakSessions", fields: [userId], references: [id])
}

model Token {
  id         String    @id @default(cuid())
  name       String
  hashedKey  String    @unique
  partialKey String
  expires    DateTime?
  lastUsed   DateTime?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  //   user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId     String
  user       User?     @relation(fields: [userId], references: [id])

  @@index([userId])
}

// Sequelize model or equivalent
model AuthSession {
  id         String   @id @default(cuid())
  userId     String
  token      String // JWT token or session ID
  deviceInfo String // User-Agent or device name
  ipAddress  String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  expiresAt  DateTime
  isActive   Boolean  @default(true)

  // Optional relation if you have a User model
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model RestrictedToken {
  id             String    @id @default(cuid())
  name           String
  hashedKey      String    @unique
  partialKey     String
  scopes         String? // space separated (Eg: "links:write", "domains:read")
  expires        DateTime?
  lastUsed       DateTime?
  rateLimit      Int       @default(60) // rate limit per minute
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  userId         String
  projectId      String
  installationId String? // if the token is generated by an OAuth user

  user User? @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([projectId])
  @@index([installationId])
}

// Login tokens
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Email verification OTPs
model EmailVerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Password reset tokens
model PasswordResetToken {
  identifier String
  token      String
  expires    DateTime

  @@unique([identifier, token])
}

model Project {
  id            String        @id @default(cuid())
  name          String
  description   String
  priority      Priority
  status        ProjectStatus @default(PENDING)
  coordinatorId String?
  createdById   String
  createdBy     User          @relation(fields: [createdById], references: [id])

  annotators  User[] @relation("AnnotatorProjects")
  coordinator User?  @relation("ProjectCoordinator", fields: [coordinatorId], references: [id])

  startDate DateTime // Start Date
  dueDate   DateTime // Due Date

  tasks               Task[]                         @relation("ProjectTasks")
  projectCoordinators ProjectCoordinatorAssignment[]

  attachment String[] @default([])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model ProjectCoordinatorAssignment {
  id        String  @id @default(cuid())
  projectId String
  project   Project @relation(fields: [projectId], references: [id])

  projectCoordinatorId String
  projectCoordinator   User   @relation(fields: [projectCoordinatorId], references: [id])

  assignedAt DateTime @default(now())
}

model Task {
  id          String     @id @default(cuid())
  name        String
  description String
  color       String
  priority    Priority
  status      TaskStatus @default(PENDING)

  projectId String
  project   Project @relation(fields: [projectId], references: [id], name: "ProjectTasks")

  annotators User[] @relation("TaskAnnotators")

  createdById String
  createdBy   User   @relation(name: "CreatedTasks", fields: [createdById], references: [id])

  startDate DateTime
  dueDate   DateTime

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model SelfTask {
  id          String     @id @default(cuid())
  name        String
  description String
  color       String
  priority    Priority
  status      TaskStatus @default(PENDING)

  createdById String
  createdBy   User   @relation(name: "SelfTasks", fields: [createdById], references: [id])

  startDate DateTime
  dueDate   DateTime

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model AdminTask {
  id          String     @id @default(cuid())
  name        String
  description String
  color       String
  priority    Priority
  status      TaskStatus @default(PENDING)

  assignedToId String // Assigned Admin
  assignedTo   User   @relation(name: "AssignedAdminTasks", fields: [assignedToId], references: [id])

  createdById String // Admin who created the task
  createdBy   User   @relation(name: "CreatedAdminTasks", fields: [createdById], references: [id])

  startDate DateTime
  dueDate   DateTime

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum PartnerStatus {
  default
  verified
  featured
}

enum PartnerRole {
  owner
  member
}

model Partner {
  id        String        @id @default(cuid())
  name      String
  image     String?
  country   String?
  status    PartnerStatus @default(default)
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  dotsUserId String? @unique

  // programs     ProgramEnrollment[]
  // applications ProgramApplication[]
  users   PartnerUser[]
  invites PartnerInvite[]
  // payouts      Payout[]
  // sales        Sale[]
}

model PartnerUser {
  id        String      @id @default(cuid())
  role      PartnerRole @default(member)
  userId    String
  partnerId String
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  partner Partner @relation(fields: [partnerId], references: [id], onDelete: Cascade)

  @@unique([userId, partnerId])
  @@index([partnerId])
}

model PartnerInvite {
  email     String
  expires   DateTime
  partnerId String
  role      PartnerRole @default(member)
  createdAt DateTime    @default(now())

  partner Partner @relation(fields: [partnerId], references: [id], onDelete: Cascade)

  @@unique([email, partnerId])
  @@index([partnerId])
}

model Assignment {
  id            String   @id @default(cuid())
  clientId      String
  developerId   String
  coordinatorId String
  packageId     String
  createdAt     DateTime @default(now())

  client      User     @relation("ClientAssignments", fields: [clientId], references: [id])
  developer   User     @relation("DeveloperAssignments", fields: [developerId], references: [id])
  coordinator User     @relation("CoordinatorAssignments", fields: [coordinatorId], references: [id])
  package     Package? @relation(fields: [packageId], references: [id])
}

// model Message {
//   id              String    @id @default(uuid())
//   text            String?
//   fileUrl         String?
//   fileType        FileType?
//   senderId        String
//   groupId         String? // For group messages
//   replyToId       String? // For reply/quote functionality
//   forwardedFromId String? // For forwarded messages
//   isEdited        Boolean   @default(false)
//   isPinned        Boolean   @default(false)
//   createdAt       DateTime  @default(now())
//   updatedAt       DateTime  @updatedAt

//   sender          User                @relation("SentMessages", fields: [senderId], references: [id])
//   group           GroupChat?          @relation(fields: [groupId], references: [id])
//   replyTo         Message?            @relation("Reply", fields: [replyToId], references: [id])
//   replies         Message[]           @relation("Reply")
//   forwardedFrom   Message?            @relation("Forward", fields: [forwardedFromId], references: [id])
//   forwardedCopies Message[]           @relation("Forward")
//   receiverId      String?
//   receiver        User?               @relation("ReceivedMessages", fields: [receiverId], references: [id])
//   reactions       Reaction[]
//   readBy          MessageReadStatus[]
// }

// model GroupChat {
//   id          String        @id @default(uuid())
//   name        String
//   description String?
//   image       String?
//   isPrivate   Boolean       @default(false)
//   isArchived  Boolean       @default(false)
//   isDeleted   Boolean       @default(false)
//   createdAt   DateTime      @default(now())
//   members     GroupMember[]
//   messages    Message[]
// }

// model GroupMember {
//   id       String    @id @default(uuid())
//   userId   String
//   groupId  String
//   role     GroupRole @default(MEMBER)
//   joinedAt DateTime  @default(now())

//   user  User      @relation(fields: [userId], references: [id])
//   group GroupChat @relation(fields: [groupId], references: [id])

//   @@unique([userId, groupId])
// }

// model Reaction {
//   id        String @id @default(uuid())
//   userId    String
//   messageId String
//   emoji     String // 👍 ❤️ 😂 etc.

//   user    User    @relation(fields: [userId], references: [id])
//   message Message @relation(fields: [messageId], references: [id])

//   @@unique([userId, messageId, emoji]) // prevent duplicate reactions
// }

// model MessageReadStatus {
//   id        String   @id @default(uuid())
//   userId    String
//   messageId String
//   readAt    DateTime @default(now())

//   user    User    @relation(fields: [userId], references: [id])
//   message Message @relation(fields: [messageId], references: [id])

//   @@unique([userId, messageId])
// }

// enum GroupRole {
//   ADMIN
//   MODERATOR
//   MEMBER
// }

// enum FileType {
//   IMAGE
//   PDF
//   DOC
//   PPT
//   EXCEL
//   ZIP
//   OTHER
// }

model Conversation {
  id        String   @id @default(uuid())
  isGroup   Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  messages     Message[]
  participants ConversationParticipant[]
}

model ConversationParticipant {
  id             String @id @default(uuid())
  userId         String
  conversationId String

  user         User         @relation(fields: [userId], references: [id])
  conversation Conversation @relation(fields: [conversationId], references: [id])

  @@unique([userId, conversationId])
}

model Message {
  id              String    @id @default(uuid())
  text            String?
  fileUrl         String?
  fileType        FileType?
  senderId        String
  receiverId      String?
  conversationId  String? // For DM chats
  groupId         String? // For group chats
  replyToId       String? // For reply/quote functionality
  forwardedFromId String? // For forwarded messages
  isEdited        Boolean   @default(false)
  isPinned        Boolean   @default(false)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  sender          User          @relation("SentMessages", fields: [senderId], references: [id])
  receiver        User?         @relation("ReceivedMessages", fields: [receiverId], references: [id])
  conversation    Conversation? @relation(fields: [conversationId], references: [id])
  group           GroupChat?    @relation(fields: [groupId], references: [id])
  replyTo         Message?      @relation("Reply", fields: [replyToId], references: [id])
  replies         Message[]     @relation("Reply")
  forwardedFrom   Message?      @relation("Forward", fields: [forwardedFromId], references: [id])
  forwardedCopies Message[]     @relation("Forward")

  reactions Reaction[]
  readBy    MessageReadStatus[]
  mentions  Mention[]
}

model GroupChat {
  id          String        @id @default(uuid())
  name        String
  description String?
  image       String?
  isPrivate   Boolean       @default(false)
  isArchived  Boolean       @default(false)
  isDeleted   Boolean       @default(false)
  createdAt   DateTime      @default(now())
  members     GroupMember[]
  messages    Message[]
}

model GroupMember {
  id       String    @id @default(uuid())
  userId   String
  groupId  String
  role     GroupRole @default(MEMBER)
  joinedAt DateTime  @default(now())

  user  User      @relation(fields: [userId], references: [id])
  group GroupChat @relation(fields: [groupId], references: [id])

  @@unique([userId, groupId])
}

model Mention {
  id        String   @id @default(uuid())
  messageId String
  userId    String // The user being mentioned
  createdAt DateTime @default(now())

  message Message @relation(fields: [messageId], references: [id])
  user    User    @relation(fields: [userId], references: [id])

  @@unique([messageId, userId])
}

model Reaction {
  id        String @id @default(uuid())
  userId    String
  messageId String
  emoji     String

  user    User    @relation(fields: [userId], references: [id])
  message Message @relation(fields: [messageId], references: [id])

  @@unique([userId, messageId, emoji])
}

model MessageReadStatus {
  id        String   @id @default(uuid())
  userId    String
  messageId String
  readAt    DateTime @default(now())

  user    User    @relation(fields: [userId], references: [id])
  message Message @relation(fields: [messageId], references: [id])

  @@unique([userId, messageId])
}

model Notification {
  id        String           @id @default(cuid()) // Unique ID
  userId    String // Who receives the notification
  type      NotificationType // Type of notification (enum)
  message   String // Short message ("New group message" etc.)
  metadata  Json? // Extra info (groupId, messageId etc.)
  isRead    Boolean          @default(false) // Read/unread status
  createdAt DateTime         @default(now()) // Timestamp
  updatedAt DateTime         @updatedAt

  user User @relation(fields: [userId], references: [id])

  @@index([userId]) // Faster queries for user's notifications
}

enum NotificationType {
  GROUP_MESSAGE
  DIRECT_MESSAGE
  FRIEND_REQUEST
  SYSTEM_ALERT
}

enum GroupRole {
  ADMIN
  MODERATOR
  MEMBER
}

enum FileType {
  IMAGE
  PDF
  DOC
  PPT
  EXCEL
  ZIP
  OTHER
}

model Product {
  id          String   @id @default(uuid())
  name        String   @unique
  paypalId    String   @unique // PayPal's product ID
  description String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  // packages    Package[]
}

model Package {
  id            String           @id @default(uuid())
  name          String           @unique
  description   String?
  price         Float
  billingType   BillingType // ENUM: MONTHLY, YEARLY, etc.
  isActive      Boolean          @default(true)
  currency      String           @default("INR")
  discount      Float            @default(0)
  // Relations
  features      PackageFeature[] // Developer assignment rules
  subscriptions Subscription[] // Linked customer subscriptions
  product_id    String?          @unique
  business_id   String?

  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  deletedAt            DateTime? // Soft delete
  users                User[]
  Assignment           Assignment[]
  ClientPackageDetails ClientPackageDetails[]
  zohoProductId        String?
  paypalPlanId         String?
  // product              Product?               @relation(fields: [paypalProductId], references: [id])
  BankTransferPayment  BankTransferPayment[]
}

model Feature {
  id        String           @id @default(uuid())
  rule      String
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  packages  PackageFeature[]
}

model PackageFeature {
  id        String   @id @default(uuid())
  packageId String
  featureId String
  available Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  package Package @relation(fields: [packageId], references: [id])
  feature Feature @relation(fields: [featureId], references: [id])

  @@unique([packageId, featureId])
}

model Subscription {
  id                           String                @id @default(uuid())
  userId                       String
  packageId                    String
  product_id                   String?
  startDate                    DateTime              @default(now())
  endDate                      DateTime?
  status                       SubscriptionStatus    @default(PENDING)
  createdAt                    DateTime              @default(now())
  updatedAt                    DateTime              @updatedAt
  externalReference            String?               @unique
  subscription_id              String?
  paypal_subscription_id       String? // ← Specific to PayPal
  previous_billing_date        DateTime?
  next_billing_date            DateTime?
  quantity                     Int?
  trial_period_days            Int?
  subscription_period_interval String?
  subscription_period_count    Int?
  payment_frequency_interval   String?
  payment_frequency_count      Int?
  recurring_amount             Float?
  currency                     String?
  rawResponse                  Json?
  user                         User                  @relation(fields: [userId], references: [id])
  package                      Package               @relation(fields: [packageId], references: [id])
  Payment                      Payment[]
  alerts                       SubscriptionAlert[]   @relation("SubscriptionAlerts")
  BankTransferPayment          BankTransferPayment[]
}

enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  EXPIRED
  PENDING
  FAILED
  REFUNDED
  CANCELLED
  DISPUTED
  PAUSED
}

enum BillingType {
  MONTHLY
  YEARLY
  ONE_TIME
}

model UserProfile {
  id          String  @id @default(cuid())
  userId      String  @unique
  firstName   String?
  lastName    String?
  phoneNumber String?
  address     String?
  city        String?
  state       String?
  country     String?
  zipCode     String?

  user User @relation(fields: [userId], references: [id])
}

model UserSettings {
  id                   String   @id @default(cuid())
  userId               String   @unique
  theme                String? // Light, Dark, etc.
  language             String? // e.g., "en", "es", etc.
  notificationsEnabled Boolean  @default(true)
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])
}

model UserActivity {
  id        String   @id @default(cuid())
  userId    String
  activity  String // Description of the activity
  timestamp DateTime @default(now())

  user User @relation(fields: [userId], references: [id])
}

model UserSession {
  id           String   @id @default(cuid())
  userId       String
  sessionToken String   @unique
  expires      DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])
}

model UserSessions {
  id         String   @id @default(cuid())
  userId     String
  token      String // JWT token or session ID
  deviceInfo String // User-Agent or device name
  ipAddress  String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  expiresAt  DateTime
  isActive   Boolean  @default(true)

  // Optional relation if you have a User model
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model UserNotification {
  id        String   @id @default(cuid())
  userId    String
  message   String // Notification message
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id])
}

model UserFeedback {
  id        String   @id @default(cuid())
  userId    String
  feedback  String // Feedback message
  rating    Int // Rating out of 5
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id])
}

model UserActivityLog {
  id        String   @id @default(cuid())
  userId    String
  activity  String // Description of the activity
  timestamp DateTime @default(now())

  user User @relation(fields: [userId], references: [id])
}

model UserRole {
  id        String   @id @default(cuid())
  userId    String
  role      String // Role name
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id])
}

model UserPermission {
  id         String   @id @default(cuid())
  userId     String
  permission String // Permission name
  createdAt  DateTime @default(now())

  user User @relation(fields: [userId], references: [id])
}

model FAQ {
  id        String   @id @default(cuid())
  question  String
  answer    String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model ClientPackageDetails {
  id String @id @default(cuid())

  clientId String
  client   User   @relation("ClientPackages", fields: [clientId], references: [id])

  packageId String
  package   Package @relation(fields: [packageId], references: [id])

  availableFrom       String // Format: "09:00"
  availableTo         String // Format: "18:00"
  timezone            String?
  industry            String?
  category            String?
  startOn             DateTime?
  description         String?
  assignedAnnotatorId String?
  assignedAnnotator   User?     @relation("AssignedAnnotators", fields: [assignedAnnotatorId], references: [id])
  subscriptionId      String?
  createdAt           DateTime  @default(now())
}

model ShiftChangeRequest {
  id String @id @default(cuid())

  annotatorId String
  annotator   User   @relation("ShiftChange_Annotator", fields: [annotatorId], references: [id])

  requestedById String
  requestedBy   User   @relation("ShiftChange_RequestedBy", fields: [requestedById], references: [id])

  approvedById String?
  approvedBy   User?   @relation("ShiftChange_ApprovedBy", fields: [approvedById], references: [id])

  newFrom String
  newTo   String
  reason  String? // Optional reason field

  status    ShiftRequestStatus @default(PENDING)
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt
}

enum ShiftRequestStatus {
  PENDING
  APPROVED
  REJECTED
}

model LeaveRequest {
  id              String      @id @default(uuid())
  annotator       User        @relation("AnnotatorLeaves", fields: [annotatorId], references: [id])
  annotatorId     String
  startDate       DateTime
  endDate         DateTime
  reason          String
  status          LeaveStatus @default(PENDING)
  rejectionReason String?
  approvedBy      User?       @relation("ApprovedLeaves", fields: [approvedById], references: [id])
  approvedById    String?
  approvedAt      DateTime?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
}

enum LeaveStatus {
  PENDING
  APPROVED
  REJECTED
}

model SubscriptionAlert {
  id             String    @id @default(cuid())
  type           String
  severity       String
  userId         String
  subscriptionId String?
  message        String
  data           Json?
  isRead         Boolean   @default(false)
  readAt         DateTime?
  createdAt      DateTime  @default(now())

  user         User          @relation("UserSubscriptionAlerts", fields: [userId], references: [id], onDelete: Cascade)
  subscription Subscription? @relation("SubscriptionAlerts", fields: [subscriptionId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([type])
  @@index([severity])
  @@index([createdAt])
  @@index([isRead])
}

// Email logging and analytics models
enum EmailStatus {
  SENT
  DELIVERED
  OPENED
  CLICKED
  FAILED
  BOUNCED
}

model EmailLog {
  id            String      @id @default(cuid())
  to            String
  cc            String?
  bcc           String?
  subject       String
  template      String?
  status        EmailStatus @default(SENT)
  sentAt        DateTime    @default(now())
  deliveredAt   DateTime?
  openedAt      DateTime?
  clickedAt     DateTime?
  failureReason String?
  metadata      Json?
  userId        String?
  user          User?       @relation(fields: [userId], references: [id], onDelete: SetNull)
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  @@index([userId])
  @@index([template])
  @@index([status])
  @@index([sentAt])
}

model EmailTemplateUsage {
  id           String   @id @default(cuid())
  templateName String   @unique
  usageCount   Int      @default(1)
  lastUsed     DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

enum EmailPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum EmailTStatus {
  PENDING
  PROCESSING
  SENT
  FAILED
  CANCELLED
}

model EmailQueue {
  id           String        @id @default(cuid())
  to           String
  cc           String?
  bcc          String?
  subject      String
  templateName String?
  templateData Json
  priority     EmailPriority @default(NORMAL)
  status       EmailTStatus  @default(PENDING)
  scheduledFor DateTime?
  attempts     Int           @default(0)
  maxAttempts  Int           @default(3)
  errorMessage String?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  processedAt  DateTime?

  @@index([status])
  @@index([scheduledFor])
  @@index([priority])
  @@map("EmailQueue")
}

// Email Templates Configuration (optional)
model EmailTemplate {
  id          String   @id @default(cuid())
  name        String   @unique
  subject     String
  content     String
  variables   String[] // Array of variable names
  category    String?
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("EmailTemplate")
}

enum BankTransferStatus {
  PENDING
  VERIFIED
  REJECTED
}

model BankTransferPayment {
  id              String             @id @default(cuid())
  paymentId       String             @unique
  userId          String
  subscriptionId  String?
  packageId       String
  amount          Float
  currency        String             @default("USD")
  transactionId   String
  bankHolderName  String
  accountNumber   String
  ifscCode        String
  bankName        String
  screenshotUrl   String
  transactionDate String
  transferedAccNo String
  status          BankTransferStatus @default(PENDING)
  adminNotes      String?
  verifiedAt      DateTime?
  verifiedById    String?
  createdAt       DateTime           @default(now())
  updatedAt       DateTime           @updatedAt

  user         User          @relation(fields: [userId], references: [id])
  subscription Subscription? @relation(fields: [subscriptionId], references: [id])
  package      Package       @relation(fields: [packageId], references: [id])
  verifiedBy   User?         @relation("BankTransferVerifier", fields: [verifiedById], references: [id])
  payment      Payment       @relation(fields: [paymentId], references: [id])

  @@index([userId])
  @@index([status])
  @@index([createdAt])
}
