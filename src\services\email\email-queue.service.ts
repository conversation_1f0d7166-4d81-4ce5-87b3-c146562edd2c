// import { EnhancedEmailService, EmailData } from './enhanced-email.service';
// import prisma from '../../prisma';

// export interface QueuedEmail {
//   id: string;
//   emailData: EmailData;
//   priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
//   scheduledFor?: Date;
//   attempts: number;
//   maxAttempts: number;
//   status: 'PENDING' | 'PROCESSING' | 'SENT' | 'FAILED' | 'CANCELLED';
//   error?: string;
//   createdAt: Date;
//   updatedAt: Date;
// }

// export interface QueueConfig {
//   maxAttempts: number;
//   retryDelay: number; // in milliseconds
//   batchSize: number;
//   processingInterval: number; // in milliseconds
//   enableRateLimiting: boolean;
//   maxEmailsPerMinute: number;
// }

// export class EmailQueueService {
//   private emailService: EnhancedEmailService;
//   private config: QueueConfig;
//   private isProcessing: boolean = false;
//   private processingInterval?: NodeJS.Timeout;
//   private emailsSentThisMinute: number = 0;
//   private lastMinuteReset: Date = new Date();

//   constructor(config?: Partial<QueueConfig>) {
//     this.emailService = new EnhancedEmailService();
//     this.config = {
//       maxAttempts: 3,
//       retryDelay: 5 * 60 * 1000, // 5 minutes
//       batchSize: 10,
//       processingInterval: 30 * 1000, // 30 seconds
//       enableRateLimiting: true,
//       maxEmailsPerMinute: 60,
//       ...config,
//     };
//   }

//   /**
//    * Add email to queue
//    */
//   async queueEmail(
//     emailData: EmailData,
//     options?: {
//       priority?: QueuedEmail['priority'];
//       scheduledFor?: Date;
//       maxAttempts?: number;
//     }
//   ): Promise<string> {
//     try {
//       const queuedEmail = await prisma.emailQueue.create({
//         data: {
//           emailData: emailData as any,
//           priority: options?.priority || 'NORMAL',
//           scheduledFor: options?.scheduledFor,
//           attempts: 0,
//           maxAttempts: options?.maxAttempts || this.config.maxAttempts,
//           status: 'PENDING',
//         },
//       });

//       console.log(`📬 Email queued with ID: ${queuedEmail.id}`);
//       return queuedEmail.id;
//     } catch (error) {
//       console.error('Error queueing email:', error);
//       throw error;
//     }
//   }

//   /**
//    * Start queue processing
//    */
//   startProcessing(): void {
//     if (this.isProcessing) {
//       console.log('📬 Email queue is already processing');
//       return;
//     }

//     this.isProcessing = true;
//     console.log('🚀 Starting email queue processing...');

//     this.processingInterval = setInterval(() => {
//       this.processQueue();
//     }, this.config.processingInterval);

//     // Initial processing
//     this.processQueue();
//   }

//   /**
//    * Stop queue processing
//    */
//   stopProcessing(): void {
//     if (!this.isProcessing) {
//       console.log('📬 Email queue is not processing');
//       return;
//     }

//     this.isProcessing = false;
//     if (this.processingInterval) {
//       clearInterval(this.processingInterval);
//       this.processingInterval = undefined;
//     }

//     console.log('🛑 Email queue processing stopped');
//   }

//   /**
//    * Process queued emails
//    */
//   private async processQueue(): Promise<void> {
//     try {
//       // Reset rate limiting counter if needed
//       this.resetRateLimitingIfNeeded();

//       // Check if we can send more emails this minute
//       if (this.config.enableRateLimiting && this.emailsSentThisMinute >= this.config.maxEmailsPerMinute) {
//         console.log(`⏳ Rate limit reached (${this.config.maxEmailsPerMinute}/minute), waiting...`);
//         return;
//       }

//       // Get pending emails to process
//       const pendingEmails = await this.getPendingEmails();

//       if (pendingEmails.length === 0) {
//         return;
//       }

//       console.log(`📬 Processing ${pendingEmails.length} queued emails...`);

//       // Process emails in batches
//       for (const email of pendingEmails) {
//         if (this.config.enableRateLimiting && this.emailsSentThisMinute >= this.config.maxEmailsPerMinute) {
//           break;
//         }

//         await this.processEmail(email);
//       }
//     } catch (error) {
//       console.error('Error processing email queue:', error);
//     }
//   }

//   /**
//    * Get pending emails from queue
//    */
//   private async getPendingEmails(): Promise<QueuedEmail[]> {
//     const now = new Date();

//     const emails = await prisma.emailQueue.findMany({
//       where: {
//         status: 'PENDING',
//         attempts: {
//           lt: this.config.maxAttempts,
//         },
//         OR: [
//           { scheduledFor: null },
//           { scheduledFor: { lte: now } },
//         ],
//       },
//       orderBy: [
//         { priority: 'desc' },
//         { createdAt: 'asc' },
//       ],
//       take: this.config.batchSize,
//     });

//     return emails.map(email => ({
//       id: email.id,
//       emailData: email.emailData as EmailData,
//       priority: email.priority as QueuedEmail['priority'],
//       scheduledFor: email.scheduledFor || undefined,
//       attempts: email.attempts,
//       maxAttempts: email.maxAttempts,
//       status: email.status as QueuedEmail['status'],
//       error: email.error || undefined,
//       createdAt: email.createdAt,
//       updatedAt: email.updatedAt,
//     }));
//   }

//   /**
//    * Process individual email
//    */
//   private async processEmail(queuedEmail: QueuedEmail): Promise<void> {
//     try {
//       // Mark as processing
//       await this.updateEmailStatus(queuedEmail.id, 'PROCESSING');

//       // Send the email
//       await this.emailService.sendEmail(queuedEmail.emailData);

//       // Mark as sent
//       await this.updateEmailStatus(queuedEmail.id, 'SENT');

//       // Increment rate limiting counter
//       if (this.config.enableRateLimiting) {
//         this.emailsSentThisMinute++;
//       }

//       console.log(`✅ Email sent successfully: ${queuedEmail.id}`);
//     } catch (error) {
//       const errorMessage = error instanceof Error ? error.message : String(error);
//       console.error(`❌ Error sending email ${queuedEmail.id}:`, errorMessage);

//       // Increment attempt count
//       const newAttempts = queuedEmail.attempts + 1;

//       if (newAttempts >= queuedEmail.maxAttempts) {
//         // Mark as failed if max attempts reached
//         await this.updateEmailStatus(queuedEmail.id, 'FAILED', errorMessage, newAttempts);
//       } else {
//         // Schedule for retry
//         const retryAt = new Date(Date.now() + this.config.retryDelay);
//         await this.scheduleRetry(queuedEmail.id, retryAt, errorMessage, newAttempts);
//       }
//     }
//   }

//   /**
//    * Update email status in queue
//    */
//   private async updateEmailStatus(
//     emailId: string,
//     status: QueuedEmail['status'],
//     error?: string,
//     attempts?: number
//   ): Promise<void> {
//     await prisma.emailQueue.update({
//       where: { id: emailId },
//       data: {
//         status,
//         error,
//         attempts,
//         updatedAt: new Date(),
//       },
//     });
//   }

//   /**
//    * Schedule email for retry
//    */
//   private async scheduleRetry(
//     emailId: string,
//     retryAt: Date,
//     error: string,
//     attempts: number
//   ): Promise<void> {
//     await prisma.emailQueue.update({
//       where: { id: emailId },
//       data: {
//         status: 'PENDING',
//         scheduledFor: retryAt,
//         error,
//         attempts,
//         updatedAt: new Date(),
//       },
//     });

//     console.log(`🔄 Email ${emailId} scheduled for retry at ${retryAt.toISOString()}`);
//   }

//   /**
//    * Reset rate limiting counter if needed
//    */
//   private resetRateLimitingIfNeeded(): void {
//     const now = new Date();
//     const timeDiff = now.getTime() - this.lastMinuteReset.getTime();

//     if (timeDiff >= 60 * 1000) { // 1 minute
//       this.emailsSentThisMinute = 0;
//       this.lastMinuteReset = now;
//     }
//   }

//   /**
//    * Get queue statistics
//    */
//   async getQueueStats(): Promise<{
//     pending: number;
//     processing: number;
//     sent: number;
//     failed: number;
//     cancelled: number;
//     total: number;
//   }> {
//     const [pending, processing, sent, failed, cancelled, total] = await Promise.all([
//       prisma.emailQueue.count({ where: { status: 'PENDING' } }),
//       prisma.emailQueue.count({ where: { status: 'PROCESSING' } }),
//       prisma.emailQueue.count({ where: { status: 'SENT' } }),
//       prisma.emailQueue.count({ where: { status: 'FAILED' } }),
//       prisma.emailQueue.count({ where: { status: 'CANCELLED' } }),
//       prisma.emailQueue.count(),
//     ]);

//     return { pending, processing, sent, failed, cancelled, total };
//   }

//   /**
//    * Cancel queued email
//    */
//   async cancelEmail(emailId: string): Promise<void> {
//     await this.updateEmailStatus(emailId, 'CANCELLED');
//     console.log(`🚫 Email cancelled: ${emailId}`);
//   }

//   /**
//    * Retry failed email
//    */
//   async retryEmail(emailId: string): Promise<void> {
//     await prisma.emailQueue.update({
//       where: { id: emailId },
//       data: {
//         status: 'PENDING',
//         attempts: 0,
//         error: null,
//         scheduledFor: null,
//         updatedAt: new Date(),
//       },
//     });

//     console.log(`🔄 Email reset for retry: ${emailId}`);
//   }

//   /**
//    * Clean up old queue entries
//    */
//   async cleanupOldEntries(daysToKeep: number = 30): Promise<number> {
//     const cutoffDate = new Date();
//     cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

//     const result = await prisma.emailQueue.deleteMany({
//       where: {
//         status: { in: ['SENT', 'FAILED', 'CANCELLED'] },
//         updatedAt: { lt: cutoffDate },
//       },
//     });

//     console.log(`🧹 Cleaned up ${result.count} old queue entries`);
//     return result.count;
//   }

//   /**
//    * Get recent queue activity
//    */
//   async getRecentActivity(limit: number = 50): Promise<QueuedEmail[]> {
//     const emails = await prisma.emailQueue.findMany({
//       orderBy: { updatedAt: 'desc' },
//       take: limit,
//     });

//     return emails.map(email => ({
//       id: email.id,
//       emailData: email.templateData,
//       priority: email.priority ,
//       scheduledFor: email.scheduledFor || undefined,
//       attempts: email.attempts,
//       maxAttempts: email.maxAttempts,
//       status: email.status as QueuedEmail['status'],
//       error: email.errorMessage || undefined,
//       createdAt: email.createdAt,
//       updatedAt: email.updatedAt,
//     }));
//   }

//   /**
//    * Update queue configuration
//    */
//   updateConfig(newConfig: Partial<QueueConfig>): void {
//     this.config = { ...this.config, ...newConfig };
//     console.log('📝 Email queue configuration updated');
//   }

//   /**
//    * Get current configuration
//    */
//   getConfig(): QueueConfig {
//     return { ...this.config };
//   }
// }

// export default EmailQueueService;

import { EnhancedEmailService, EmailData } from "./enhanced-email.service";
import prisma from "../../prisma";
import { JsonValue } from "@prisma/client/runtime/library";

export interface QueuedEmail {
  id: string;
  emailData: EmailData;
  priority: "LOW" | "NORMAL" | "HIGH" | "URGENT";
  scheduledFor?: Date;
  attempts: number;
  maxAttempts: number;
  status: "PENDING" | "PROCESSING" | "SENT" | "FAILED" | "CANCELLED";
  error?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface QueueConfig {
  maxAttempts: number;
  retryDelay: number; // ms
  batchSize: number;
  processingInterval: number; // ms
  enableRateLimiting: boolean;
  maxEmailsPerMinute: number;
}

export class EmailQueueService {
  private emailService = new EnhancedEmailService();
  private config: QueueConfig;
  private isProcessing = false;
  private processingInterval?: NodeJS.Timeout;
  private emailsSentThisMinute = 0;
  private lastMinuteReset = new Date();

  constructor(config?: Partial<QueueConfig>) {
    this.config = {
      maxAttempts: 3,
      retryDelay: 5 * 60 * 1000,
      batchSize: 10,
      processingInterval: 30 * 1000,
      enableRateLimiting: true,
      maxEmailsPerMinute: 60,
      ...config,
    };

    // Graceful shutdown
    process.on("SIGINT", () => this.stopProcessing());
    process.on("SIGTERM", () => this.stopProcessing());
  }

  async queueEmail(
    emailData: EmailData,
    options?: {
      priority?: QueuedEmail["priority"];
      scheduledFor?: Date;
      maxAttempts?: number;
    }
  ): Promise<string> {
    const queuedEmail = await prisma.emailQueue.create({
      data: {
        to: emailData.to as string,
        cc: emailData.cc as string,
        bcc: emailData.bcc as string,
        subject: emailData.subject,
        templateName: emailData.template,
        templateData: emailData.data,
        priority: options?.priority || "NORMAL",
        scheduledFor: options?.scheduledFor,
        maxAttempts: options?.maxAttempts || this.config.maxAttempts,
        attempts: 0,
        status: "PENDING",
      },
    });
    console.log(`📬 Email queued with ID: ${queuedEmail.id}`);
    return queuedEmail.id;
  }

  startProcessing(): void {
    if (this.isProcessing) {
      console.log("📬 Email queue already processing");
      return;
    }

    this.isProcessing = true;
    console.log("🚀 Starting email queue processing...");

    this.processingInterval = setInterval(
      () => this.processQueue(),
      this.config.processingInterval
    );
    this.processQueue();
  }

  stopProcessing(): void {
    if (!this.isProcessing) return;

    this.isProcessing = false;
    if (this.processingInterval) clearInterval(this.processingInterval);
    console.log("🛑 Email queue stopped");
  }

  private async processQueue(): Promise<void> {
    try {
      this.resetRateLimitingIfNeeded();

      if (
        this.config.enableRateLimiting &&
        this.emailsSentThisMinute >= this.config.maxEmailsPerMinute
      ) {
        console.log(
          `⏳ Rate limit reached (${this.config.maxEmailsPerMinute}/minute)`
        );
        return;
      }

      const pendingEmails = await this.getPendingEmails();
      if (!pendingEmails.length) return;

      for (const email of pendingEmails) {
        if (
          this.config.enableRateLimiting &&
          this.emailsSentThisMinute >= this.config.maxEmailsPerMinute
        )
          break;

        await this.processEmail(email);
      }
    } catch (err) {
      console.error("❌ Queue processing error:", err);
    }
  }

  private async getPendingEmails(): Promise<QueuedEmail[]> {
    const now = new Date();
    const emails = await prisma.emailQueue.findMany({
      where: {
        status: "PENDING",
        attempts: { lt: this.config.maxAttempts },
        OR: [{ scheduledFor: null }, { scheduledFor: { lte: now } }],
      },
      orderBy: [{ priority: "desc" }, { createdAt: "asc" }],
      take: this.config.batchSize,
    });

    // export interface QueuedEmail {
    //   id: string;
    //   emailData: EmailData;
    //   priority: "LOW" | "NORMAL" | "HIGH" | "URGENT";
    //   scheduledFor?: Date;
    //   attempts: number;
    //   maxAttempts: number;
    //   status: "PENDING" | "PROCESSING" | "SENT" | "FAILED" | "CANCELLED";
    //   error?: string;
    //   createdAt: Date;
    //   updatedAt: Date;
    // }
    // export interface EmailData {
    //   to: string;
    //   subject: string;
    //   template: string;
    //   data: any;
    //   cc?: string;
    //   bcc?: string;
    //   templateName: string;
    //   attachments?: any[];
    // }

    return emails.map((email) => ({
      id: email.id,
      emailData: {
        bcc: email.bcc || "",
        cc: email.cc || "",
        template: email.templateData as string,
        templateName: email.templateName || "",
        data: "",
        to: email.to,
        subject: email.subject,
      },
      priority: email.priority,
      scheduledFor: email.scheduledFor || undefined,
      attempts: email.attempts,
      maxAttempts: email.maxAttempts,
      status: email.status,
      error: email.errorMessage || undefined,
      createdAt: email.createdAt,
      updatedAt: email.updatedAt,
    }));
  }

  private async processEmail(queuedEmail: QueuedEmail): Promise<void> {
    try {
      await this.updateEmailStatus(queuedEmail.id, "PROCESSING");

      await this.emailService.sendEmail(queuedEmail.emailData);

      await prisma.emailQueue.update({
        where: { id: queuedEmail.id },
        data: {
          status: "SENT",
          processedAt: new Date(),
          updatedAt: new Date(),
        },
      });

      if (this.config.enableRateLimiting) this.emailsSentThisMinute++;
      console.log(`✅ Email sent: ${queuedEmail.id}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      console.error(`❌ Sending error for ${queuedEmail.id}: ${errorMessage}`);

      const newAttempts = queuedEmail.attempts + 1;
      if (newAttempts >= queuedEmail.maxAttempts) {
        await this.updateEmailStatus(
          queuedEmail.id,
          "FAILED",
          errorMessage,
          newAttempts
        );
      } else {
        await this.scheduleRetry(
          queuedEmail.id,
          new Date(Date.now() + this.config.retryDelay),
          errorMessage,
          newAttempts
        );
      }
    }
  }

  private async updateEmailStatus(
    id: string,
    status: QueuedEmail["status"],
    error?: string,
    attempts?: number
  ): Promise<void> {
    await prisma.emailQueue.update({
      where: { id },
      data: {
        status,
        errorMessage: error,
        attempts,
        updatedAt: new Date(),
      },
    });
  }

  private async scheduleRetry(
    id: string,
    retryAt: Date,
    error: string,
    attempts: number
  ): Promise<void> {
    await prisma.emailQueue.update({
      where: { id },
      data: {
        status: "PENDING",
        scheduledFor: retryAt,
        errorMessage: error,
        attempts,
        updatedAt: new Date(),
      },
    });

    console.log(`🔁 Retry scheduled for ${id} at ${retryAt.toISOString()}`);
  }

  private resetRateLimitingIfNeeded(): void {
    const now = new Date();
    if (now.getTime() - this.lastMinuteReset.getTime() >= 60000) {
      this.emailsSentThisMinute = 0;
      this.lastMinuteReset = now;
    }
  }

  async getQueueStats() {
    const [pending, processing, sent, failed, cancelled, total] =
      await Promise.all([
        prisma.emailQueue.count({ where: { status: "PENDING" } }),
        prisma.emailQueue.count({ where: { status: "PROCESSING" } }),
        prisma.emailQueue.count({ where: { status: "SENT" } }),
        prisma.emailQueue.count({ where: { status: "FAILED" } }),
        prisma.emailQueue.count({ where: { status: "CANCELLED" } }),
        prisma.emailQueue.count(),
      ]);
    return { pending, processing, sent, failed, cancelled, total };
  }

  async cancelEmail(id: string): Promise<void> {
    await this.updateEmailStatus(id, "CANCELLED");
    console.log(`🚫 Email cancelled: ${id}`);
  }

  async retryEmail(id: string): Promise<void> {
    await prisma.emailQueue.update({
      where: { id },
      data: {
        status: "PENDING",
        attempts: 0,
        errorMessage: null,
        scheduledFor: null,
        updatedAt: new Date(),
      },
    });
    console.log(`🔄 Email reset for retry: ${id}`);
  }

  async cleanupOldEntries(daysToKeep = 30): Promise<number> {
    const cutoff = new Date();
    cutoff.setDate(cutoff.getDate() - daysToKeep);
    const result = await prisma.emailQueue.deleteMany({
      where: {
        status: { in: ["SENT", "FAILED", "CANCELLED"] },
        updatedAt: { lt: cutoff },
      },
    });
    console.log(`🧹 Cleaned up ${result.count} old emails`);
    return result.count;
  }

  async getRecentActivity(limit = 50): Promise<QueuedEmail[]> {
    const emails = await prisma.emailQueue.findMany({
      orderBy: { updatedAt: "desc" },
      take: limit,
    });
    return emails.map((email) => ({
      id: email.id,
      emailData: {
        bcc: email.bcc || "",
        cc: email.cc || "",
        template: email.templateData as string,
        templateName: email.templateName || "",
        data: "",
        to: email.to,
        subject: email.subject,
      } as EmailData,
      priority: email.priority,
      scheduledFor: email.scheduledFor || undefined,
      attempts: email.attempts,
      maxAttempts: email.maxAttempts,
      status: email.status,
      error: email.errorMessage || undefined,
      createdAt: email.createdAt,
      updatedAt: email.updatedAt,
    }));
  }

  updateConfig(newConfig: Partial<QueueConfig>) {
    this.config = { ...this.config, ...newConfig };
    console.log("⚙️ Config updated");
  }

  getConfig(): QueueConfig {
    return { ...this.config };
  }
}

export default EmailQueueService;
