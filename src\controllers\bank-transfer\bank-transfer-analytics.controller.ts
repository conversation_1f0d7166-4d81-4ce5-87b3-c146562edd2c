// import { Request, Response } from "express";
// import { EnhancedBankTransferService } from "../../services/bank-transfer/bank-transfer-enhanced.service";
// import { successResponse, errorResponse } from "../../helper/apiResponse";
// import { AuthenticatedRequest } from "../../middlewares/checkAuth";

// export class BankTransferAnalyticsController {
//   private bankTransferService: EnhancedBankTransferService;

//   constructor() {
//     this.bankTransferService = new EnhancedBankTransferService();
//   }

//   /**
//    * Get bank transfer analytics (admin only)
//    */
//   async getBankTransferAnalytics(req: AuthenticatedRequest, res: Response) {
//     try {
//       if (req.user?.userRole !== "ADMIN") {
//         return errorResponse(res, "Access denied. Admin access required.");
//       }

//       const analytics = await this.bankTransferService.getBankTransferAnalytics();

//       return successResponse(res, "Bank transfer analytics retrieved successfully", analytics);
//     } catch (error: any) {
//       console.error("Error getting bank transfer analytics:", error);
//       return errorResponse(res, error.message || "Failed to get bank transfer analytics");
//     }
//   }
// }