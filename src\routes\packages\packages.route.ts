import { Router } from "express";
import { PackageController } from "../../controllers/package/package.controller";
import { FeaturePackageController } from "../../controllers/package/features.package.controller";
import { asyncHandler } from "../../middlewares/asyncHandler";
import authMiddleware from "../../middlewares/checkAuth";

const router = Router();
const controller = new PackageController();
const featureController = new FeaturePackageController();

router.post(
  "/create-package",
  // authMiddleware,
  asyncHandler(controller.create.bind(controller))
);
router.get(
  "/get",
  authMiddleware,
  asyncHandler(controller.getAll.bind(controller))
);
router.get(
  "/get-package/:id",
  asyncHandler(controller.getById.bind(controller))
);
router.patch(
  "/update-package/:id",
  authMiddleware,
  asyncHandler(controller.update.bind(controller))
);
router.delete(
  "/delete-package/:id",
  authMiddleware,
  asyncHandler(controller.delete.bind(controller))
);

router
  .route("/get-packages")
  .get(asyncHandler(controller.getAllActivePackages.bind(controller)));

// Features

router.post(
  "/create-feature",
  authMiddleware,
  asyncHandler(featureController.createFeature.bind(controller))
);
router.get(
  "/get-feature/:packageId",
  authMiddleware,
  asyncHandler(featureController.getFeature.bind(controller))
);
router.patch(
  "/update-feature/:featureId",
  authMiddleware,
  asyncHandler(featureController.updateFeature.bind(controller))
);
router.delete(
  "/delete-feature/:featureId",
  authMiddleware,
  asyncHandler(featureController.deleteFeature.bind(controller))
);
router.get(
  "/get-package-features",
  authMiddleware,
  asyncHandler(featureController.getPackageFeatures.bind(controller))
);

export default router;
