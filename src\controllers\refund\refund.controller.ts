import { Request, Response } from "express";
import {
  successResponseWithData,
  errorResponse,
} from "../../helper/apiResponse";
import { RefundService } from "../../services/refund/refund.service";
import { JwtPayload } from "jsonwebtoken";

const taskService = new RefundService();

interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}

export class RefundController {
  async create(req: AuthenticatedRequest, res: Response) {
    // const createdById = req.user?.userId;
    // if (!createdById) return errorResponse(res, "Unauthorized");

    const task = await taskService.create({ ...req.body });
    return successResponseWithData(res, "Task created", task);
  }

  async getAll(req: Request, res: Response) {
    const { page = 1, limit = 10, search } = req.query;
    const tasks = await taskService.getAll({
      page: parseInt(page as string, 10) || 1,
      limit: parseInt(limit as string, 10) || 10,
      search: (search as string) || "",
    });
    return successResponseWithData(res, "Tasks fetched", tasks);
  }

  async getById(req: Request, res: Response) {
    const task = await taskService.getById(req.params.id);
    if (!task) return errorResponse(res, "Task not found");
    return successResponseWithData(res, "Task fetched", task);
  }
}
