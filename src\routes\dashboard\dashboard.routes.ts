import { Router } from "express";
import { asyncHandler } from "../../middlewares/asyncHandler";
import { DashboardController } from "../../controllers/dashboard/dashboard.controller";
import authMiddleware from "../../middlewares/checkAuth";
import { hasRole } from "../../middlewares/checkRole";

const router = Router();
const controller = new DashboardController();

router.get(
  "/get-dashboard-data",
  authMiddleware,
  asyncHandler(controller.getDashboardSummary.bind(controller))
);

router.get(
  "/get-coworker-dashboard-data",
  authMiddleware,
  // hasRole("COOWORKER"),
  asyncHandler(controller.getCooworkerDashboardSummary.bind(controller))
);

router.get(
  "/cooworker/get-dashboard-data",
  authMiddleware,
  asyncHandler(controller.getDashboardCooWorkerSummary.bind(controller))
);
router.get(
  "/client-annotators",
  authMiddleware,
  asyncHandler(controller.getClientAnnotators.bind(controller))
);

router.get(
  "/cooworker/client-annotators",
  authMiddleware,
  asyncHandler(controller.getCoowerkerClientAnnotators.bind(controller))
);

router.get(
  "/coordinator-dashboard",
  authMiddleware,
  asyncHandler(controller.getCoordinatorDashboardSummary.bind(controller))
);

router.get(
  "/admin-dashboard",
  authMiddleware,
  asyncHandler(controller.getAdminDashboardSummary.bind(controller))
);

export default router;
