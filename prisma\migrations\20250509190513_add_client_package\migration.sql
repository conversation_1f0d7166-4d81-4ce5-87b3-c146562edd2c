-- CreateTable
CREATE TABLE "ClientPackageDetails" (
    "id" TEXT NOT NULL,
    "clientId" TEXT NOT NULL,
    "packageId" TEXT NOT NULL,
    "availableFrom" TEXT NOT NULL,
    "availableTo" TEXT NOT NULL,
    "timezone" TEXT,
    "assignedAnnotatorId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ClientPackageDetails_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "ClientPackageDetails" ADD CONSTRAINT "ClientPackageDetails_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClientPackageDetails" ADD CONSTRAINT "ClientPackageDetails_packageId_fkey" FOREIGN KEY ("packageId") REFERENCES "Package"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddFore<PERSON>Key
ALTER TABLE "ClientPackageDetails" ADD CONSTRAINT "ClientPackageDetails_assignedAnnotatorId_fkey" FOREIGN KEY ("assignedAnnotatorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
