/*
  Warnings:

  - The `status` column on the `AttendanceSummary` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- CreateEnum
CREATE TYPE "AttendanceStatus" AS ENUM ('NOT_CLOCKED_IN', 'ACTIVE', 'ON_BREAK', 'CLOCKED_OUT');

-- AlterTable
ALTER TABLE "AttendanceSummary" DROP COLUMN "status",
ADD COLUMN     "status" "AttendanceStatus" NOT NULL DEFAULT 'NOT_CLOCKED_IN';

-- DropEnum
DROP TYPE "LogStatus";

-- CreateTable
CREATE TABLE "BreakSession" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "attendanceId" TEXT NOT NULL,
    "startTime" TIMESTAMP(3) NOT NULL,
    "endTime" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BreakSession_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "BreakSession" ADD CONSTRAINT "BreakSession_attendanceId_fkey" FOREIGN KEY ("attendanceId") REFERENCES "AttendanceSummary"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BreakSession" ADD CONSTRAINT "BreakSession_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
