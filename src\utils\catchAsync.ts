import { Request, Response, NextFunction } from "express";
import { Prisma } from "@prisma/client";

const catchAsync = (fn: (req: Request, res: Response, next: NextFunction) => Promise<any>) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch((err) => {
      try {
        next(err);
      } catch (error) {
        if (error instanceof Prisma.PrismaClientValidationError) {
          res.status(400).json({
            status: false,
            code: 400,
            message: error.message,
          });
        } else {
          res.status(400).json({
            status: false,
            code: 400,
            message: error instanceof Error ? error.message : "An unknown error occurred",
          });
        }
      }
    });
  };
};

export default catchAsync;
