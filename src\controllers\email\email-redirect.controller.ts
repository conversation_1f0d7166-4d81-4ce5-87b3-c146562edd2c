import { Request, Response, NextFunction } from 'express';
import { AppError } from '../../utils/ApiError';
import prisma from '../../prisma';

export class EmailRedirectController {
  /**
   * Handle coworker invitation redirect from email
   * This endpoint is called when user clicks on invitation link in email
   * It validates the token and redirects to frontend with proper parameters
   */
  async redirectCoworkerInvite(req: Request, res: Response, next: NextFunction) {
    try {
      const { email, token } = req.query;

      if (!email || !token) {
        throw new AppError('Invalid invitation link. Email and token are required.', 400);
      }

      // Validate the token
      const resetToken = await prisma.passwordResetToken.findFirst({
        where: {
          identifier: email as string,
          token: token as string,
          expires: {
            gt: new Date(), // Token should not be expired
          },
        },
      });

      if (!resetToken) {
        // Redirect to frontend with error
        const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
        return res.redirect(`${frontendUrl}/coworker/accept-invite?error=invalid_token&message=${encodeURIComponent('Invalid or expired invitation link')}`);
      }

      // Check if coworker invitation exists (coworker is stored in User table with role COWORKER)
      const coworkerInvitation = await prisma.user.findFirst({
        where: {
          email: email as string,
          role: 'COWORKER',
          passwordHash: null, // Invitation not yet accepted (no password set)
        },
      });

      if (!coworkerInvitation) {
        const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
        return res.redirect(`${frontendUrl}/coworker/accept-invite?error=invitation_not_found&message=${encodeURIComponent('Invitation not found or already accepted')}`);
      }

      // Token is valid, redirect to frontend with proper parameters
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const redirectUrl = `${frontendUrl}/coworker/accept-invite?email=${encodeURIComponent(email as string)}&token=${encodeURIComponent(token as string)}`;
      
      console.log('Redirecting coworker invitation to:', redirectUrl);
      
      return res.redirect(redirectUrl);
    } catch (error) {
      console.error('Error in coworker invitation redirect:', error);
      
      // Redirect to frontend with error
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const errorMessage = error instanceof AppError ? error.message : 'An error occurred while processing your invitation';
      return res.redirect(`${frontendUrl}/coworker/accept-invite?error=server_error&message=${encodeURIComponent(errorMessage)}`);
    }
  }

  /**
   * Handle general email link tracking and redirection
   * This can be used for other email links that need tracking
   */
  async trackAndRedirect(req: Request, res: Response, next: NextFunction) {
    try {
      const { url, emailId } = req.query;

      if (!url) {
        throw new AppError('Redirect URL is required', 400);
      }

      // Log the click if emailId is provided (for analytics)
      if (emailId) {
        try {
          await prisma.emailLog.update({
            where: { id: emailId as string },
            data: {
              status: 'CLICKED',
              clickedAt: new Date(),
            },
          });
        } catch (error) {
          console.error('Error logging email click:', error);
          // Don't fail the redirect if logging fails
        }
      }

      // Decode and redirect to the original URL
      const decodedUrl = decodeURIComponent(url as string);
      console.log('Redirecting to:', decodedUrl);
      
      return res.redirect(decodedUrl);
    } catch (error) {
      console.error('Error in email redirect:', error);
      next(error);
    }
  }

  /**
   * Handle email open tracking (tracking pixel)
   */
  async trackEmailOpen(req: Request, res: Response, next: NextFunction) {
    try {
      const { emailId } = req.params;

      if (emailId) {
        try {
          await prisma.emailLog.update({
            where: { id: emailId },
            data: {
              status: 'OPENED',
              openedAt: new Date(),
            },
          });
        } catch (error) {
          console.error('Error logging email open:', error);
          // Don't fail the request if logging fails
        }
      }

      // Return a 1x1 transparent pixel
      const pixel = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'base64'
      );

      res.set({
        'Content-Type': 'image/png',
        'Content-Length': pixel.length,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      });

      return res.send(pixel);
    } catch (error) {
      console.error('Error in email open tracking:', error);
      
      // Still return a pixel even if tracking fails
      const pixel = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'base64'
      );

      res.set({
        'Content-Type': 'image/png',
        'Content-Length': pixel.length,
      });

      return res.send(pixel);
    }
  }
}

export default EmailRedirectController;
