# Payment Notification Template

A comprehensive email template for sending payment confirmation emails (both success and failure) for PayPal, Dodo, and other payment methods.

## Features

- ✅ **Unified Template**: Single template for both success and failure notifications
- 🎨 **Professional Design**: Consistent with existing email templates (navbar, footer, styling)
- 📱 **Responsive**: Mobile-friendly design
- 🔧 **Flexible**: Supports PayPal, Dodo, Bank Transfer, Credit Card, and custom payment methods
- 🎯 **Dynamic Content**: Conditional content based on payment status
- 🚀 **Easy Integration**: Simple service layer for quick implementation

## Template Structure

### Header
- Company logo
- Consistent branding

### Content
- **Status Banner**: Success (green) or failure (red) with appropriate icons
- **Payment Details**: Transaction ID, method, amount, date, package
- **Next Steps**: Dynamic list based on payment status
- **Call-to-Action Buttons**: Dashboard access, support contact
- **Professional Signature**

### Footer
- Contact information
- Support links
- Consistent with other templates

## Usage

### 1. Basic Usage

```typescript
import { paymentNotificationService } from './services/payment/payment-notification.service';

// PayPal Success
await paymentNotificationService.sendPayPalNotification({
  email: '<EMAIL>',
  customerName: '<PERSON>',
  transactionId: 'ORDER123',
  amount: '$99.00',
  packageName: 'Professional Plan',
  status: 'success',
  paypalTransactionId: 'PAYPAL_TXN_789'
});

// Dodo Payment Failure
await paymentNotificationService.sendDodoNotification({
  email: '<EMAIL>',
  customerName: 'Jane Smith',
  transactionId: 'DODO_ORDER_456',
  amount: '$149.00',
  packageName: 'Enterprise Plan',
  status: 'failed'
});
```

### 2. Custom Payment Methods

```typescript
// Custom Success
await paymentNotificationService.sendPaymentSuccess({
  email: '<EMAIL>',
  customerName: 'Mike Johnson',
  transactionId: 'CUSTOM_TXN_789',
  paymentMethod: 'Credit Card',
  amount: '$199.00',
  packageName: 'Premium Plan'
});

// Custom Failure with specific reason
await paymentNotificationService.sendPaymentFailure({
  email: '<EMAIL>',
  customerName: 'Sarah Wilson',
  transactionId: 'BANK_TXN_456',
  paymentMethod: 'Bank Transfer',
  amount: '$299.00',
  packageName: 'Annual Subscription',
  failureReason: 'Insufficient funds in account'
});
```

### 3. Direct Email Service Usage

```typescript
import { EmailIntegrationService } from './services/email/email-integration.service';

const emailService = new EmailIntegrationService();

await emailService.sendPaymentNotification('<EMAIL>', {
  customerName: 'John Doe',
  paymentStatus: 'success',
  transactionId: 'TXN123456789',
  paymentMethod: 'PayPal',
  amount: '$99.00',
  paymentDate: '2024-01-15 14:30:00 UTC',
  packageName: 'Professional Plan',
  dashboardUrl: 'https://app.company.com/dashboard',
  supportUrl: 'https://app.company.com/support'
});
```

## Integration Examples

### PayPal Webhook Handler

```typescript
app.post('/webhook/paypal', async (req, res) => {
  const { status, transaction_id, amount, customer_email, customer_name } = req.body;
  
  await paymentNotificationService.sendPayPalNotification({
    email: customer_email,
    customerName: customer_name,
    transactionId: transaction_id,
    amount: amount,
    packageName: 'Your Package Name',
    status: status === 'completed' ? 'success' : 'failed',
    paypalTransactionId: transaction_id
  });
  
  res.status(200).send('OK');
});
```

### Dodo Payment Callback

```typescript
app.post('/payment/dodo/callback', async (req, res) => {
  const { success, orderId, amount, userEmail, userName } = req.body;
  
  await paymentNotificationService.sendDodoNotification({
    email: userEmail,
    customerName: userName,
    transactionId: orderId,
    amount: amount,
    packageName: 'Your Package Name',
    status: success ? 'success' : 'failed'
  });
  
  res.json({ message: 'Payment processed' });
});
```

## Template Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `customerName` | Customer's name | "John Doe" |
| `paymentStatus` | "success" or "failed" | "success" |
| `transactionId` | Transaction/Order ID | "TXN123456789" |
| `paymentMethod` | Payment method used | "PayPal", "Dodo", "Credit Card" |
| `amount` | Payment amount | "$99.00" |
| `paymentDate` | Payment date/time | "2024-01-15 14:30:00 UTC" |
| `packageName` | Package/plan name | "Professional Plan" |
| `dashboardUrl` | Dashboard URL (optional) | "https://app.company.com/dashboard" |
| `supportUrl` | Support URL (optional) | "https://app.company.com/support" |

## Environment Variables

Set these in your `.env` file:

```env
COMPANY_NAME=Your Company Name
SUPPORT_EMAIL=<EMAIL>
FRONTEND_URL=https://app.company.com
LOGO_URL=https://your-logo-url.com/logo.png
CONTACT_URL=https://company.com/contact
PHONE_NUMBER=******-567-8900
```

## Files Created

1. **Template**: `src/templates/payment-notification.ts`
2. **Service**: `src/services/payment/payment-notification.service.ts`
3. **Examples**: `src/examples/payment-notification-usage.ts`
4. **Integration**: Added to `src/templates/index.ts` and email services

## Testing

Preview the template at: `/api/email-preview/payment-notification`

## Customization

The template supports:
- Custom success/failure messages
- Custom next steps
- Custom CTA buttons
- Conditional content based on payment status
- Multiple payment methods

Perfect for any payment confirmation workflow! 🎉
