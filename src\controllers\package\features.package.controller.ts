import { Request, Response } from "express";
import { PackageService } from "../../services/package/package.service";
import * as ApiResponse from "../../helper/apiResponse";
import { JwtPayload } from "jsonwebtoken";

interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}

interface QueryParams {
  page: number;
  limit: number;
  search: string;
}

export class FeaturePackageController {
  private packageService = new PackageService();
  async createFeature(req: AuthenticatedRequest, res: Response) {
    const response = await this.packageService.createFeature(req.body);
    return ApiResponse.successResponseWithData(
      res,
      "Task created successfully",
      response
    );
  }
  async getFeature(req: AuthenticatedRequest, res: Response) {
    const response = await this.packageService.getFeatureById(
      req.params.packageId
    );
    return ApiResponse.successResponseWithData(
      res,
      "Task fetched successfully",
      response
    );
  }

  async updateFeature(req: AuthenticatedRequest, res: Response) {
    const response = await this.packageService.updateFeature(
      req.params.featureId,
      req.body
    );
    return ApiResponse.successResponseWithData(
      res,
      "Task updated successfully",
      response
    );
  }
  async deleteFeature(req: AuthenticatedRequest, res: Response) {
    const response = await this.packageService.deleteFeature(
      req.params.featureId
    );
    return ApiResponse.successResponseWithData(
      res,
      "Task deleted successfully",
      response
    );
  }

  async getPackageFeatures(req: AuthenticatedRequest, res: Response) {
    const { page = 1, limit = 10, search } = req.query;
    const query: QueryParams = {
      page: parseInt(page as string, 10) || 1,
      limit: parseInt(limit as string, 10) || 10,
      search: (search as string) || "",
    };
    const response = await this.packageService.getAll(
      query as unknown as QueryParams
    );
    return ApiResponse.successResponseWithData(
      res,
      "Task fetched successfully",
      response
    );
  }
}
