import { Prisma } from "@prisma/client";
import prisma from "../../prisma";
import { AppError } from "../../utils/ApiError";

export interface CreateProjectInput {
  name: string;
  description: string;
  priority: "LOW" | "MEDIUM" | "HIGH";
  createdById: string;
 annotatorIds: string[];
  coordinatorId?: string;
  attachment?: string[];
  startDate: Date;
  dueDate: Date;
}

export const createProject = async (data: CreateProjectInput) => {
  const assignment = await prisma.assignment.findFirst({
    where: { clientId: data.createdById },
  });

  const coordinatorId = assignment?.coordinatorId;

  // Validate that all annotatorIds exist
  const annotators = await prisma.user.findMany({
    where: { id: { in: data.annotatorIds } },
  });

  if (annotators.length !== data.annotatorIds.length) {
    throw new AppError("One or more annotators not found", 404);
  }

  return prisma.project.create({
    data: {
      name: data.name,
      description: data.description,
      priority: data.priority,
      createdById: data.createdById,
      annotators: {
        connect: data.annotatorIds.map((id) => ({ id })), // Connect multiple annotators
      },
      coordinatorId: coordinatorId,
      attachment: data.attachment || [],
      startDate: data.startDate,
      dueDate: data.dueDate,
    } as Prisma.ProjectUncheckedCreateInput,
  });
};

export const createCoowerkerProject = async (data: CreateProjectInput) => {
  const coowerker = await prisma.user.findUnique({
    where: {
      id: data.createdById,
    },
  });

  if (!coowerker) {
    throw new AppError("No Coowerker Found", 404);
  }

  const assignment = await prisma.assignment.findFirst({
    where: { clientId: coowerker?.clientOwnerId! },
  });

  const coordinatorId = assignment?.coordinatorId;

  // Validate that all annotatorIds exist
  const annotators = await prisma.user.findMany({
    where: { id: { in: data.annotatorIds } },
  });

  if (annotators.length !== data.annotatorIds.length) {
    throw new AppError("One or more annotators not found", 404);
  }

  if (!coowerker.clientOwnerId) {
    throw new AppError("Co-worker does not have a client owner assigned", 400);
  }

  return prisma.project.create({
    data: {
      name: data.name,
      description: data.description,
      priority: data.priority,
      createdById: coowerker.clientOwnerId,
      annotators: {
        connect: data.annotatorIds.map((id) => ({ id })), // Connect multiple annotators
      },
      coordinatorId: coordinatorId,
      attachment: data.attachment || [],
      startDate: data.startDate,
      dueDate: data.dueDate,
    },
  });
};

export const getAllProjects = async () => {
  return prisma.project.findMany({
    include: {
      createdBy: true,
      annotators: true,
      tasks: true,
    },
  });
};

export const getProjectById = async (id: string) => {
  return prisma.project.findUnique({
    where: { id },
    include: {
      createdBy: true,
      annotators: true,
      tasks: true,
    },
  });
};

export const updateProject = async (
  id: string,
  data: Partial<CreateProjectInput & { status: "PENDING" | "IN_PROGRESS" | "COMPLETED" }>
) => {
  const existingProject = await prisma.project.findUnique({
    where: { id },
    include: { annotators: true },
  });

  if (!existingProject) {
    throw new Error("Project not found");
  }

  // Validate new annotatorIds if provided
  if (data.annotatorIds) {
    const validAnnotators = await prisma.user.findMany({
      where: { id: { in: data.annotatorIds } },
    });

    if (validAnnotators.length !== data.annotatorIds.length) {
      throw new Error("One or more annotators not found");
    }
  }

  return prisma.project.update({
    where: { id },
    data: {
      name: data.name,
      description: data.description,
      priority: data.priority,
      status: data.status,
      startDate: data.startDate,
      dueDate: data.dueDate,
      coordinatorId: data.coordinatorId,
      attachment: data.attachment || existingProject.attachment,
      annotators: data.annotatorIds
        ? {
            set: data.annotatorIds.map((id) => ({ id })),
          }
        : undefined,
    },
  });
};


export const deleteProject = async (id: string) => {
  return prisma.project.delete({
    where: { id },
  });
};

export const getProjectsCreatedByUser = async (userId: string) => {
  return prisma.project.findMany({
    where: { createdById: userId },
    include: {
      createdBy: true,
      annotators: true,
      tasks: true,
    },
    orderBy: {
      createdAt: "desc",
    },
  });
};

export const getProjectsCreatedByUserCooWorker = async (userId: string) => {
  const user = await prisma.user.findFirst({
    where: {
      id: userId,
    },
  });

  if (!user) {
    throw new AppError("No User Found", 404);
  }
  return prisma.project.findMany({
    where: { createdById: user.clientOwnerId! },
    include: {
      createdBy: true,
      annotators: true,
      tasks: true,
    },
    orderBy: {
      createdAt: "desc",
    },
  });
};

export const getProjectsForAnnotator = async (annotatorId: string) => {
  return prisma.project.findMany({
    where: {
      annotators: {
        some: { id: annotatorId },
      },
    },
    include: {
      createdBy: true,
      annotators: true,
      tasks: true,
    },
    orderBy: {
      createdAt: "desc",
    },
  });
};

export const getProjectsByClientId = async (clientId: string) => {
  return prisma.project.findMany({
    where: { createdById: clientId },
    include: {
      createdBy: true,
      annotators: true,
      tasks: true,
    },
    orderBy: {
      createdAt: "desc",
    },
  });
};
