import { Request, Response } from "express";

import { successResponseWithData, errorResponse } from "../../helper/apiResponse";
import { ShiftChangeService } from "../../services/shiftChange/shiftChange.service";
import { JwtPayload } from "jsonwebtoken";

interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}

export const createShiftChangeRequest = async (req: AuthenticatedRequest, res: Response) => {
  const { annotatorId, newFrom, newTo, reason } = req.body;
  const { userId, userRole } = req.user as { userId: string; userRole: string };

  try {
    const result = await ShiftChangeService.createShiftChange({
      annotatorId,
      newFrom,
      newTo,
      userId,
      userRole,
      reason,
    });

    const message =
      userRole === "CLIENT"
        ? "Shift change request submitted for approval"
        : "Shift timing changed successfully";

    return successResponseWithData(res, message, result);
  } catch (err: any) {
    return errorResponse(res, err.message || "Internal server error");
  }
};

export const approveShiftChangeRequest = async (req: AuthenticatedRequest, res: Response) => {
  const { requestId } = req.params;
  const { userId, userRole } = req?.user as { userId: string; userRole: string };

  if (!["ADMIN", "PROJECT_COORDINATOR"].includes(userRole)) {
    return errorResponse(res, "You are not authorized to approve shift changes");
  }

  try {
    const result = await ShiftChangeService.approveShiftChange(requestId, userId);
    return successResponseWithData(res, "Shift change approved successfully", result);
  } catch (err: any) {
    return errorResponse(res, err.message || "Internal server error");
  }
};

export const getPendingShiftRequests = async (req: AuthenticatedRequest, res: Response) => {
  const pendingRequests = await ShiftChangeService.getPendingRequests();
  res.status(200).json({ status: 1, data: pendingRequests });
};

export const getAllShiftChangeRequests = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const allRequests = await ShiftChangeService.getAllShiftChangeRequests();
    return successResponseWithData(res, "All shift change requests fetched successfully", allRequests);
  } catch (err: any) {
    return errorResponse(res, err.message || "Internal server error");
  }
};


export const getRelevantShiftChangeRequests = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userId, userRole } = req.user || {};

    if (!userId || !userRole) {
      return errorResponse(res, "Unauthorized");
    }

    let relevantRequests;

    if (userRole === "ANNOTATOR") {
      relevantRequests = await ShiftChangeService.getShiftChangeRequestsByAnnotator(userId);
    } else if (["CLIENT", "PROJECT_COORDINATOR", "COWORKER"].includes(userRole)) {
      relevantRequests = await ShiftChangeService.getShiftChangeRequestsByRole(userId, userRole);
    } else {
      return errorResponse(res, "You don't have permission to view these shift change requests");
    }

    return successResponseWithData(res, "Shift change requests fetched successfully", relevantRequests);
  } catch (err: any) {
    return errorResponse(res, err.message || "Failed to fetch relevant shift change requests");
  }
};




