// model Token {
//   id         String    @id @default(cuid())
//   name       String
//   hashedKey  String    @unique
//   partialKey String
//   expires    DateTime?
//   lastUsed   DateTime?
//   createdAt  DateTime  @default(now())
//   updatedAt  DateTime  @updatedAt
// //   user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)
//   userId     String

//   @@index([userId])
// }

// model RestrictedToken {
//   id             String    @id @default(cuid())
//   name           String
//   hashedKey      String    @unique
//   partialKey     String
//   scopes         String? // space separated (Eg: "links:write", "domains:read")
//   expires        DateTime?
//   lastUsed       DateTime?
//   rateLimit      Int       @default(60) // rate limit per minute
//   createdAt      DateTime  @default(now())
//   updatedAt      DateTime  @updatedAt
//   userId         String
//   projectId      String
//   installationId String? // if the token is generated by an OAuth client

// //   user                 User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
// //   project              Project               @relation(fields: [projectId], references: [id], onDelete: Cascade)
//   // authorizedApp OAuthAuthorizedApp? @relation(fields: [installationId], references: [id], onDelete: Cascade)
// //   refreshTokens        OAuthRefreshToken[]
// //   installedIntegration InstalledIntegration? @relation(fields: [installationId], references: [id], onDelete: Cascade)

//   @@index([userId])
//   @@index([projectId])
//   @@index([installationId])
// }

// // Login tokens
// model VerificationToken {
//   identifier String
//   token      String   @unique
//   expires    DateTime

//   @@unique([identifier, token])
// }

// // Email verification OTPs
// model EmailVerificationToken {
//   identifier String
//   token      String   @unique
//   expires    DateTime

//   @@unique([identifier, token])
// }

// // Password reset tokens
// model PasswordResetToken {
//   identifier String
//   token      String   @unique
//   expires    DateTime

//   @@unique([identifier, token])
// }
