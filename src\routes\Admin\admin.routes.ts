import { Router } from "express";
import { AdminController } from "../../controllers/Admin/admin.controller";
import { async<PERSON>and<PERSON> } from "../../middlewares/asyncHandler";
import authMiddleware from "../../middlewares/checkAuth";

const router = Router();
const adminController = new AdminController();

router.post("/create-admin", authMiddleware, asyncHandler(adminController.create.bind(adminController)));

router.get("/get-all-admin", asyncHandler(adminController.getAll.bind(adminController)));

router.get("/get-by-id/:id", asyncHandler(adminController.getById.bind(adminController)));

router.put("/update-admin/:id", asyncHandler(adminController.update.bind(adminController)));

router.delete("/delete-admin/:id", asyncHandler(adminController.delete.bind(adminController)));

router.put("/suspend-admin/:id", asyncHandler(adminController.suspend.bind(adminController)));

router.put("/activate-admin/:id", asyncHandler(adminController.activate.bind(adminController)));

router.put("/reset-password/:id", asyncHandler(adminController.resetPassword.bind(adminController)));
router.put("/delete-admin/:id", authMiddleware, asyncHandler(adminController.deleteOtherAdmin.bind(adminController)));


//client action
router.put("/suspend-client/:id", asyncHandler(adminController.suspendClient.bind(adminController)));

router.put("/activate-client/:id", asyncHandler(adminController.activateClient.bind(adminController)));

router.put("/reset-client-password/:id", asyncHandler(adminController.resetClientPassword.bind(adminController)));

router.put("/delete-client/:id", asyncHandler(adminController.softDeleteClient.bind(adminController)));




export default router;
