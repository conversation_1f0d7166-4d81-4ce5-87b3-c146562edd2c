import { Request, Response } from "express";
import { UserCommunicationService } from "../../services/email/user-communication.service";
import prisma from "../../prisma";
import { ApiResponse } from "../../helper/apiResponse";
import catchAsync from "../../utils/catchAsync";

export class UserCommunicationController {
  private userCommunicationService: UserCommunicationService;

  constructor() {
    this.userCommunicationService = new UserCommunicationService();
  }

  /**
   * Send password creation link
   */
  sendPasswordCreationLink = catchAsync(async (req: Request, res: Response) => {
    const { email, firstName } = req.body;

    if (!email || !firstName) {
      return res.status(400);
    }

    // Generate secure token
    const token = this.userCommunicationService.generateSecureToken();
    const expiryTime = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Store token in database (you might want to create a separate tokens table)
    await prisma.user.upsert({
      where: { email },
      update: {
        // Store token in a custom field or create a separate tokens table
        // For now, we'll use the otpCode field temporarily
        otpCode: token,
        otpExpiry: expiryTime,
      },
      create: {
        email,
        name: firstName,
        otpCode: token,
        otpExpiry: expiryTime,
      },
    });

    // Generate password creation link
    const baseUrl = process.env.FRONTEND_URL || "https://app.company.com";
    const passwordCreationLink = `${baseUrl}/create-password?token=${token}&email=${encodeURIComponent(
      email
    )}`;

    // Prepare email data
    const emailData = {
      firstName,
      passwordCreationLink,
      timeLimit: "24 hours",
      companyName: process.env.COMPANY_NAME || "Your Company",
      supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
      websiteUrl: process.env.WEBSITE_URL || "https://company.com",
    };

    // Send password creation email
    await this.userCommunicationService.sendPasswordCreationLink(
      email,
      emailData
    );

    res.status(200).json();
  });

  /**
   * Send account suspended notification
   */
  sendAccountSuspended = catchAsync(async (req: Request, res: Response) => {
    const { userId, reason, duration } = req.body;

    if (!userId || !reason) {
      return res
        .status(400)
        .json(
          // ApiResponse.error("User ID and suspension reason are required", 400)
        );
    }

    // Get user details
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return res.status(404).json();
    }

    // Update user status
    const suspensionDate = new Date();
    const suspendedUntil = duration
      ? new Date(Date.now() + duration * 24 * 60 * 60 * 1000)
      : undefined;

    await prisma.user.update({
      where: { id: userId },
      data: {
        accountStatus: "SUSPENDED",
        suspendedUntil,
      },
    });

    // Prepare email data
    const emailData = {
      firstName: user.name,
      platformName: process.env.PLATFORM_NAME || "Our Platform",
      suspensionDate: this.userCommunicationService.formatDate(suspensionDate),
      suspensionReason: reason,
      suspensionDuration: duration ? `${duration} days` : undefined,
      companyName: process.env.COMPANY_NAME || "Your Company",
      supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
      supportPhone: process.env.SUPPORT_PHONE || "******-567-8900",
      websiteUrl: process.env.WEBSITE_URL || "https://company.com",
    };

    // Send suspension email
    await this.userCommunicationService.sendAccountSuspended(
      user.email,
      emailData
    );

    res.status(200).json(
      // ApiResponse.success("Account suspended and notification sent", {
      //   message: "User has been suspended and notified via email",
      //   suspendedUntil,
      // })
    );
  });

  /**
   * Send account reactivated notification
   */
  sendAccountReactivated = catchAsync(async (req: Request, res: Response) => {
    const { userId } = req.body;

    if (!userId) {
      return res
        .status(400)
        // .json(ApiResponse.error("User ID is required", 400));
    }

    // Get user details
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return res.status(404)
    }

    // Update user status
    await prisma.user.update({
      where: { id: userId },
      data: {
        accountStatus: "ACTIVE",
        suspendedUntil: null,
      },
    });

    // Generate login link
    const baseUrl = process.env.FRONTEND_URL || "https://app.company.com";
    const loginLink = `${baseUrl}/login`;

    // Prepare email data
    const emailData = {
      firstName: user.name,
      platformName: process.env.PLATFORM_NAME || "Our Platform",
      loginLink,
      companyName: process.env.COMPANY_NAME || "Your Company",
      supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
      websiteUrl: process.env.WEBSITE_URL || "https://company.com",
    };

    // Send reactivation email
    await this.userCommunicationService.sendAccountReactivated(
      user.email,
      emailData
    );

    res.status(200).json(
      // ApiResponse.success("Account reactivated and notification sent", {
      //   message:
      //     "User account has been reactivated and user notified via email",
      // })
    );
  });

  /**
   * Send welcome email with meeting link
   */
  sendWelcomeEmail = catchAsync(async (req: Request, res: Response) => {
    const { userId, meetingLink, yourName, teamName } = req.body;

    if (!userId || !meetingLink) {
      return res
        .status(400)
        // .json(ApiResponse.error("User ID and meeting link are required", 400));
    }

    // Get user details
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return res.status(404).json();
    }

    // Prepare email data
    const emailData = {
      firstName: user.name,
      meetingLink,
      companyName: process.env.COMPANY_NAME || "Your Company",
      yourName,
      teamName,
      supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
      websiteUrl: process.env.WEBSITE_URL || "https://company.com",
    };

    // Send welcome email
    await this.userCommunicationService.sendWelcomeEmail(user.email, emailData);

    res.status(200).json(
      // ApiResponse.success("Welcome email sent successfully", {
      //   message: "Welcome email with meeting link has been sent to the user",
      // })
    );
  });

  /**
   * Send password changed notification
   */
  sendPasswordChanged = catchAsync(async (req: Request, res: Response) => {
    const { userId, ipAddress } = req.body;

    if (!userId) {
      return res
        .status(400)
        // .json(ApiResponse.error("User ID is required", 400));
    }

    // Get user details
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return res.status(404).json();
    }

    // Generate reset link
    const resetToken = this.userCommunicationService.generateSecureToken();
    const baseUrl = process.env.FRONTEND_URL || "https://app.company.com";
    const resetLink = `${baseUrl}/reset-password?token=${resetToken}`;

    // Prepare email data
    const emailData = {
      firstName: user.name,
      platformName: process.env.PLATFORM_NAME || "Our Platform",
      dateTime: this.userCommunicationService.formatDate(new Date()),
      email: user.email,
      ipAddress: ipAddress || "Unknown",
      resetLink,
      companyName: process.env.COMPANY_NAME || "Your Company",
      supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
      websiteUrl: process.env.WEBSITE_URL || "https://company.com",
    };

    // Send password changed email
    await this.userCommunicationService.sendPasswordChanged(
      user.email,
      emailData
    );

    res.status(200).json(
      // ApiResponse.success("Password change notification sent", {
      //   message: "Password change notification has been sent to the user",
      // })
    );
  });

  /**
   * Send team assignment notification
   */
  sendTeamAssignment = catchAsync(async (req: Request, res: Response) => {
    const { userId, projectId, annotatorId, coordinatorId } = req.body;

    if (!userId || !projectId || !annotatorId || !coordinatorId) {
      return res
        .status(400)
        .json(
          // ApiResponse.error(
          //   "User ID, project ID, annotator ID, and coordinator ID are required",
          //   400
          // )
        );
    }

    // Get all required data
    const [user, project, annotator, coordinator] = await Promise.all([
      prisma.user.findUnique({ where: { id: userId } }),
      prisma.project.findUnique({ where: { id: projectId } }),
      prisma.user.findUnique({ where: { id: annotatorId } }),
      prisma.user.findUnique({ where: { id: coordinatorId } }),
    ]);

    if (!user || !project || !annotator || !coordinator) {
      return res
        .status(404)
        .json(
          // ApiResponse.error("One or more required entities not found", 404)
        );
    }

    // Prepare email data
    const emailData = {
      firstName: user.name,
      platformName: process.env.PLATFORM_NAME || "Our Platform",
      projectName: project.name,
      projectId: project.id,
      startDate: this.userCommunicationService.formatDate(project.startDate),
      annotatorName: annotator.name,
      annotatorInitials: this.userCommunicationService.getUserInitials(
        annotator.name
      ),
      annotatorExpertise: "Data Annotation & Quality Assurance", // You might want to store this in user profile
      annotatorEmail: annotator.email,
      annotatorPhone: undefined, // Add phone field to user model if needed
      coordinatorName: coordinator.name,
      coordinatorInitials: this.userCommunicationService.getUserInitials(
        coordinator.name
      ),
      coordinatorEmail: coordinator.email,
      coordinatorPhone: undefined, // Add phone field to user model if needed
      companyName: process.env.COMPANY_NAME || "Your Company",
      supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
      websiteUrl: process.env.WEBSITE_URL || "https://company.com",
    };

    // Send team assignment email
    await this.userCommunicationService.sendTeamAssignment(
      user.email,
      emailData
    );

    res.status(200).json(
      // ApiResponse.success("Team assignment notification sent", {
      //   message: "Team assignment notification has been sent to the user",
      //   assignedTeam: {
      //     annotator: annotator.name,
      //     coordinator: coordinator.name,
      //   },
      // })
    );
  });

  /**
   * Test email service connection
   */
  testEmailService = catchAsync(async (req: Request, res: Response) => {
    const isConnected = await this.userCommunicationService.testConnection();

    if (isConnected) {
      res.status(200).json(
        // ApiResponse.success("Email service is working correctly", {
        //   status: "connected",
        //   timestamp: new Date().toISOString(),
        // })
      );
    } else {
      res
        .status(500)
        // .json(ApiResponse.error("Email service connection failed", 500));
    }
  });
}

export default UserCommunicationController;
