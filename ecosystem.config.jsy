module.exports = {
  apps: [
    {
      name: "annotator-backend",
      script: "dist/app.js", // Changed to dist/app.js based on error reference
      instances: 1,
      exec_mode: "cluster",
      watch: false, // Disabled for production
      ignore_watch: ["node_modules", "uploads", "logs"],
      autorestart: true,
      max_memory_restart: "1G",
      merge_logs: true,
      pid_file: "/var/run/annotator-backend.pid", // Updated to match app name
      env: {
        NODE_ENV: "development",
        PORT: 4000,
        ORIGIN: "http://localhost:3000",
        FRONTEND_URL: "http://localhost:3000",
      },
      env_staging: {
        NODE_ENV: "staging",
        PORT: 6000,
        ORIGIN: "https://staging.getannotator.com",
        FRONTEND_URL: "https://staging.getannotator.com",
      },
      env_production: {
        NODE_ENV: "production",
        PORT: 5000,
        ORIGIN: "https://app.getannotator.com",
        FRONTEND_URL: "https://app.getannotator.com",
      },
      env_file: ".env", // Load .env file
    },
  ],
};

