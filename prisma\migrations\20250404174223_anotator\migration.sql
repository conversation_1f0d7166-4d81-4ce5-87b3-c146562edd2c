-- Drop<PERSON><PERSON><PERSON><PERSON>ey
ALTER TABLE "Task" DROP CONSTRAINT "Task_annotatorId_fkey";

-- DropF<PERSON><PERSON>Key
ALTER TABLE "Task" DROP CONSTRAINT "Task_createdById_fkey";

-- CreateTable
CREATE TABLE "Admin" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Admin_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Annotator" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "contactNumber" TEXT NOT NULL,
    "location" TEXT NOT NULL,
    "primaryRole" TEXT NOT NULL,
    "experience" INTEGER NOT NULL,
    "projectsCompleted" INTEGER NOT NULL,
    "profileImageUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Annotator_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Admin_email_key" ON "Admin"("email");

-- CreateIndex
CREATE UNIQUE INDEX "Annotator_email_key" ON "Annotator"("email");

-- CreateIndex
CREATE UNIQUE INDEX "Annotator_contactNumber_key" ON "Annotator"("contactNumber");
