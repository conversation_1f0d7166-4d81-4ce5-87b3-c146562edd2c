// import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
// import { EnhancedEmailService } from '../../services/email/enhanced-email.service';
// import { EmailAnalyticsService } from '../../services/email/email-analytics.service';
// import nodemailer from 'nodemailer';

// // Mock nodemailer
// jest.mock('nodemailer');
// const mockedNodemailer = nodemailer as jest.Mocked<typeof nodemailer>;

// // Mock EmailAnalyticsService
// jest.mock('../../services/email/email-analytics.service');
// const MockedEmailAnalyticsService = EmailAnalyticsService as jest.MockedClass<typeof EmailAnalyticsService>;

// describe('EnhancedEmailService', () => {
//   let emailService: EnhancedEmailService;
//   let mockTransporter: any;
//   let mockAnalyticsService: jest.Mocked<EmailAnalyticsService>;

//   beforeEach(() => {
//     // Reset all mocks
//     jest.clearAllMocks();

//     // Mock transporter
//     mockTransporter = {
//       sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
//       verify: jest.fn().mockResolvedValue(true),
//     };

//     mockedNodemailer.createTransporter.mockReturnValue(mockTransporter);

//     // Mock analytics service
//     mockAnalyticsService = {
//       logEmailSent: jest.fn().mockResolvedValue('test-email-id'),
//       markEmailDelivered: jest.fn().mockResolvedValue(undefined),
//       markEmailFailed: jest.fn().mockResolvedValue(undefined),
//       generateTrackingPixelUrl: jest.fn().mockReturnValue('http://test.com/track/open/test-email-id'),
//       generateClickTrackingUrl: jest.fn().mockReturnValue('http://test.com/track/click/test-email-id'),
//       trackEmailOpen: jest.fn().mockResolvedValue(undefined),
//       trackEmailClick: jest.fn().mockResolvedValue(undefined),
//       getEmailAnalytics: jest.fn().mockResolvedValue({
//         totalSent: 100,
//         totalDelivered: 95,
//         totalOpened: 80,
//         totalClicked: 20,
//         totalFailed: 5,
//         deliveryRate: 95,
//         openRate: 84.21,
//         clickRate: 25,
//         bounceRate: 5,
//       }),
//       getTemplateAnalytics: jest.fn().mockResolvedValue([]),
//       getRecentEmailActivity: jest.fn().mockResolvedValue([]),
//       cleanupOldLogs: jest.fn().mockResolvedValue(10),
//     } as any;

//     MockedEmailAnalyticsService.mockImplementation(() => mockAnalyticsService);

//     emailService = new EnhancedEmailService(true);
//   });

//   afterEach(() => {
//     jest.restoreAllMocks();
//   });

//   describe('constructor', () => {
//     it('should initialize with analytics enabled by default', () => {
//       const service = new EnhancedEmailService();
//       expect(MockedEmailAnalyticsService).toHaveBeenCalled();
//     });

//     it('should initialize with analytics disabled when specified', () => {
//       jest.clearAllMocks();
//       const service = new EnhancedEmailService(false);
//       expect(MockedEmailAnalyticsService).toHaveBeenCalled();
//     });
//   });

//   describe('sendEmail', () => {
//     const testEmailData = {
//       to: '<EMAIL>',
//       subject: 'Test Email',
//       template: 'otp-verification',
//       data: {
//         customerName: 'John Doe',
//         otpCode: '123456',
//         timeLimit: 10,
//         companyName: 'Test Company',
//         supportEmail: '<EMAIL>',
//         phoneNumber: '+1234567890',
//         website: 'https://test.com',
//       },
//     };

//     it('should send email successfully with analytics', async () => {
//       const emailId = await emailService.sendEmail(testEmailData);

//       expect(mockAnalyticsService.logEmailSent).toHaveBeenCalledWith({
//         to: testEmailData.to,
//         subject: testEmailData.subject,
//         template: testEmailData.template,
//         metadata: testEmailData.data,
//       });

//       expect(mockTransporter.sendMail).toHaveBeenCalledWith(
//         expect.objectContaining({
//           to: testEmailData.to,
//           subject: testEmailData.subject,
//           from: expect.any(String),
//           html: expect.stringContaining('John Doe'),
//         })
//       );

//       expect(mockAnalyticsService.markEmailDelivered).toHaveBeenCalledWith('test-email-id');
//       expect(emailId).toBe('test-email-id');
//     });

//     it('should add tracking pixel to email content', async () => {
//       await emailService.sendEmail(testEmailData);

//       const sendMailCall = mockTransporter.sendMail.mock.calls[0][0];
//       expect(sendMailCall.html).toContain('http://test.com/track/open/test-email-id');
//       expect(sendMailCall.html).toContain('width="1" height="1" style="display:none;"');
//     });

//     it('should handle email sending failure', async () => {
//       const error = new Error('SMTP connection failed');
//       mockTransporter.sendMail.mockRejectedValue(error);

//       await expect(emailService.sendEmail(testEmailData)).rejects.toThrow('SMTP connection failed');

//       expect(mockAnalyticsService.markEmailFailed).toHaveBeenCalledWith(
//         'test-email-id',
//         'SMTP connection failed'
//       );
//     });

//     it('should handle multiple recipients', async () => {
//       const multiRecipientData = {
//         ...testEmailData,
//         to: ['<EMAIL>', '<EMAIL>'],
//         cc: ['<EMAIL>'],
//         bcc: ['<EMAIL>'],
//       };

//       await emailService.sendEmail(multiRecipientData);

//       expect(mockTransporter.sendMail).toHaveBeenCalledWith(
//         expect.objectContaining({
//           to: '<EMAIL>, <EMAIL>',
//           cc: '<EMAIL>',
//           bcc: '<EMAIL>',
//         })
//       );
//     });

//     it('should work without analytics when disabled', async () => {
//       const serviceWithoutAnalytics = new EnhancedEmailService(false);
      
//       const emailId = await serviceWithoutAnalytics.sendEmail(testEmailData);

//       expect(mockAnalyticsService.logEmailSent).not.toHaveBeenCalled();
//       expect(mockAnalyticsService.markEmailDelivered).not.toHaveBeenCalled();
//       expect(emailId).toBeNull();
//     });
//   });

//   describe('renderTemplate', () => {
//     it('should render TypeScript template with data', async () => {
//       const testData = {
//         customerName: 'John Doe',
//         otpCode: '123456',
//         timeLimit: 10,
//         companyName: 'Test Company',
//       };

//       // Access private method for testing
//       const renderTemplate = (emailService as any).renderTemplate.bind(emailService);
//       const result = await renderTemplate('otp-verification', testData);

//       expect(result).toContain('John Doe');
//       expect(result).toContain('123456');
//       expect(result).toContain('Test Company');
//     });

//     it('should handle conditional blocks in templates', async () => {
//       const testData = {
//         showSection: true,
//         hideSection: false,
//         userName: 'Test User',
//       };

//       const templateWithConditionals = `
//         <div>
//           {{#showSection}}
//           <p>This section should be shown</p>
//           {{/showSection}}
//           {{#hideSection}}
//           <p>This section should be hidden</p>
//           {{/hideSection}}
//           <p>Hello {{userName}}</p>
//         </div>
//       `;

//       // Mock EMAIL_TEMPLATES to include our test template
//       const originalTemplates = require('../../templates').EMAIL_TEMPLATES;
//       (require('../../templates') as any).EMAIL_TEMPLATES = {
//         ...originalTemplates,
//         'test-conditional': templateWithConditionals,
//       };

//       const renderTemplate = (emailService as any).renderTemplate.bind(emailService);
//       const result = await renderTemplate('test-conditional', testData);

//       expect(result).toContain('This section should be shown');
//       expect(result).not.toContain('This section should be hidden');
//       expect(result).toContain('Hello Test User');
//     });

//     it('should handle nested object properties', async () => {
//       const testData = {
//         user: {
//           name: 'John Doe',
//           email: '<EMAIL>',
//         },
//         company: {
//           name: 'Test Corp',
//         },
//       };

//       const templateWithNested = `
//         <div>
//           <p>Hello {{user.name}}</p>
//           <p>Email: {{user.email}}</p>
//           <p>Company: {{company.name}}</p>
//         </div>
//       `;

//       const originalTemplates = require('../../templates').EMAIL_TEMPLATES;
//       (require('../../templates') as any).EMAIL_TEMPLATES = {
//         ...originalTemplates,
//         'test-nested': templateWithNested,
//       };

//       const renderTemplate = (emailService as any).renderTemplate.bind(emailService);
//       const result = await renderTemplate('test-nested', testData);

//       expect(result).toContain('Hello John Doe');
//       expect(result).toContain('Email: <EMAIL>');
//       expect(result).toContain('Company: Test Corp');
//     });

//     it('should fallback to basic template when template not found', async () => {
//       const renderTemplate = (emailService as any).renderTemplate.bind(emailService);
//       const result = await renderTemplate('non-existent-template', {
//         userName: 'Test User',
//         message: 'Test message',
//       });

//       expect(result).toContain('Test User');
//       expect(result).toContain('Test message');
//       expect(result).toContain('<!DOCTYPE html>');
//     });
//   });

//   describe('sendBulkEmails', () => {
//     it('should send multiple emails', async () => {
//       const emails = [
//         {
//           to: '<EMAIL>',
//           subject: 'Email 1',
//           template: 'otp-verification',
//           data: { customerName: 'User 1', otpCode: '111111' },
//         },
//         {
//           to: '<EMAIL>',
//           subject: 'Email 2',
//           template: 'otp-verification',
//           data: { customerName: 'User 2', otpCode: '222222' },
//         },
//       ];

//       await emailService.sendBulkEmails(emails);

//       expect(mockTransporter.sendMail).toHaveBeenCalledTimes(2);
//       expect(mockAnalyticsService.logEmailSent).toHaveBeenCalledTimes(2);
//     });

//     it('should handle partial failures in bulk sending', async () => {
//       const emails = [
//         {
//           to: '<EMAIL>',
//           subject: 'Email 1',
//           template: 'otp-verification',
//           data: { customerName: 'User 1' },
//         },
//         {
//           to: '<EMAIL>',
//           subject: 'Email 2',
//           template: 'otp-verification',
//           data: { customerName: 'User 2' },
//         },
//       ];

//       // Make second email fail
//       mockTransporter.sendMail
//         .mockResolvedValueOnce({ messageId: 'msg1' })
//         .mockRejectedValueOnce(new Error('Failed to send'));

//       await emailService.sendBulkEmails(emails);

//       expect(mockTransporter.sendMail).toHaveBeenCalledTimes(2);
//       expect(mockAnalyticsService.markEmailDelivered).toHaveBeenCalledTimes(1);
//       expect(mockAnalyticsService.markEmailFailed).toHaveBeenCalledTimes(1);
//     });
//   });

//   describe('testConnection', () => {
//     it('should return true when connection is successful', async () => {
//       mockTransporter.verify.mockResolvedValue(true);

//       const result = await emailService.testConnection();

//       expect(result).toBe(true);
//       expect(mockTransporter.verify).toHaveBeenCalled();
//     });

//     it('should return false when connection fails', async () => {
//       mockTransporter.verify.mockRejectedValue(new Error('Connection failed'));

//       const result = await emailService.testConnection();

//       expect(result).toBe(false);
//       expect(mockTransporter.verify).toHaveBeenCalled();
//     });
//   });

//   describe('analytics methods', () => {
//     it('should get analytics when enabled', async () => {
//       const startDate = new Date('2023-01-01');
//       const endDate = new Date('2023-01-31');

//       const result = await emailService.getAnalytics(startDate, endDate, 'otp-verification');

//       expect(mockAnalyticsService.getEmailAnalytics).toHaveBeenCalledWith(
//         startDate,
//         endDate,
//         'otp-verification'
//       );
//       expect(result.totalSent).toBe(100);
//     });

//     it('should throw error when analytics disabled', async () => {
//       const serviceWithoutAnalytics = new EnhancedEmailService(false);
//       const startDate = new Date('2023-01-01');
//       const endDate = new Date('2023-01-31');

//       await expect(
//         serviceWithoutAnalytics.getAnalytics(startDate, endDate)
//       ).rejects.toThrow('Analytics is not enabled');
//     });

//     it('should track email opens', async () => {
//       await emailService.trackOpen('test-email-id');

//       expect(mockAnalyticsService.trackEmailOpen).toHaveBeenCalledWith('test-email-id');
//     });

//     it('should track email clicks', async () => {
//       await emailService.trackClick('test-email-id');

//       expect(mockAnalyticsService.trackEmailClick).toHaveBeenCalledWith('test-email-id');
//     });
//   });

//   describe('sendLegacyEmail', () => {
//     it('should send email using legacy format', async () => {
//       const legacyEmailData = {
//         name: 'John Doe',
//         email: '<EMAIL>',
//         subject: 'Legacy Email',
//         body: '<p>This is a legacy email</p>',
//       };

//       const result = await emailService.sendLegacyEmail(legacyEmailData);

//       expect(result).toBe(true);
//       expect(mockTransporter.sendMail).toHaveBeenCalledWith(
//         expect.objectContaining({
//           to: '<EMAIL>',
//           subject: 'Legacy Email',
//           html: '<p>This is a legacy email</p>',
//         })
//       );
//     });

//     it('should handle legacy email sending failure', async () => {
//       mockTransporter.sendMail.mockRejectedValue(new Error('Send failed'));

//       const legacyEmailData = {
//         name: 'John Doe',
//         email: '<EMAIL>',
//         subject: 'Legacy Email',
//         body: '<p>This is a legacy email</p>',
//       };

//       const result = await emailService.sendLegacyEmail(legacyEmailData);

//       expect(result).toBe(false);
//     });
//   });
// });