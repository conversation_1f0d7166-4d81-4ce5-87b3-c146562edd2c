import { Router } from "express";
import { async<PERSON>and<PERSON> } from "../../middlewares/asyncHandler";
import authMiddleware from "../../middlewares/checkAuth";
import { hasRole } from "../../middlewares/checkRole";
import { BillingsController } from "../../controllers/billings/billings.controller";

const router = Router();
const billingsController = new BillingsController();

router.get(
  "/clients",
  authMiddleware,
  hasRole("CLIENT"),
  asyncHandler(billingsController.clientsBillingsList.bind(billingsController))
);

router.get(
  "/subscriptions",
  authMiddleware,
  hasRole("CLIENT"),
  asyncHandler(
    billingsController.clientsSubscriptionsList.bind(billingsController)
  )
);

router.get(
  "/admin/payments",
  authMiddleware,
  hasRole("ADMIN"),
  async<PERSON>and<PERSON>(billingsController.adminPaymentsList.bind(billingsController))
);

router.get("/invoice/:id", authMiddleware, hasRole("CLIENT"),
  asyncHandler(billingsController.downloadInvoice.bind(billingsController))
);

router.get("/admin/invoice/:id", authMiddleware, hasRole("ADMIN"),
  asyncHandler(billingsController.downloadInvoiceAdmin.bind(billingsController))
);

// Subscription management routes
router.get("/subscription/:id", authMiddleware, hasRole("CLIENT"),
  asyncHandler(billingsController.getSubscriptionDetails.bind(billingsController))
);

router.post("/subscription/:id/cancel", authMiddleware, hasRole("CLIENT"),
  asyncHandler(billingsController.cancelSubscription.bind(billingsController))
);

// Admin subscription management routes
router.get("/admin/subscription/:id", authMiddleware, hasRole("ADMIN"),
  asyncHandler(billingsController.getSubscriptionDetailsAdmin.bind(billingsController))
);

router.post("/admin/subscription/:id/cancel", authMiddleware, hasRole("ADMIN"),
  asyncHandler(billingsController.cancelSubscriptionAdmin.bind(billingsController))
);

export default router;
