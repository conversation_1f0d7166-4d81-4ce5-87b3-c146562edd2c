import { JwtPayload } from "jsonwebtoken";
import { CoworkerService } from "../../services/coworker/coworker.service";
import { Request, Response } from "express";
import {
  errorResponse,
  successResponse,
  successResponseWithData,
} from "../../helper/apiResponse";
import { BillingsService } from "../../services/billings/billings.service";

interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}

export class BillingsController {
  private billingService: BillingsService;

  constructor() {
    this.billingService = new BillingsService();
  }

  async clientsBillingsList(req: AuthenticatedRequest, res: Response) {
    const clientId = req.user?.userId;
    const result = await this.billingService.getClientsBillingsdetails(
      clientId,
      req.query
    );

    return successResponseWithData(
      res,
      "Transaction Details fetched successfully",
      result
    );
  }

  async clientsSubscriptionsList(req: AuthenticatedRequest, res: Response) {
    const clientId = req.user?.userId;
    const result = await this.billingService.getCLientsSubscriptionsList(
      clientId,
      req.query
    );

    return successResponseWithData(
      res,
      "Transaction Details fetched successfully",
      result
    );
  }

  async adminPaymentsList(req: AuthenticatedRequest, res: Response) {
    const result = await this.billingService.getPaymentsList(
      req.query
    );

    return successResponseWithData(
      res,
      "Transaction Details fetched successfully",
      result
    );
  }

  async downloadInvoice(req: AuthenticatedRequest, res: Response) {
    try {
      const paymentId = req.params.id;
      const clientId = req.user?.userId;

      if (!paymentId) {
        return errorResponse(res, "Payment ID is required");
      }

      const result = await this.billingService.downloadInvoice(paymentId, clientId);

      if (!result) {
        return errorResponse(res, "Invoice not found or access denied");
      }

      // Determine content type based on the data
      const isTextReceipt = result.invoiceData.toString().startsWith('\nPAYMENT RECEIPT');
      
      if (isTextReceipt) {
        res.setHeader('Content-Type', 'text/plain');
        res.setHeader('Content-Disposition', `attachment; filename="receipt-${paymentId}.txt"`);
      } else {
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="invoice-${paymentId}.pdf"`);
      }
      
      return res.send(result.invoiceData);
    } catch (error: any) {
      console.error("Error downloading invoice:", error);
      return errorResponse(res, error.message || "Failed to download invoice");
    }
  }

  async downloadInvoiceAdmin(req: AuthenticatedRequest, res: Response) {
    try {
      const paymentId = req.params.id;

      if (!paymentId) {
        return errorResponse(res, "Payment ID is required");
      }

      // Admin can access any invoice, so don't pass clientId
      const result = await this.billingService.downloadInvoice(paymentId);

      if (!result) {
        return errorResponse(res, "Invoice not found");
      }

      // Determine content type based on the data
      const isTextReceipt = result.invoiceData.toString().startsWith('\nPAYMENT RECEIPT');
      
      if (isTextReceipt) {
        res.setHeader('Content-Type', 'text/plain');
        res.setHeader('Content-Disposition', `attachment; filename="receipt-${paymentId}.txt"`);
      } else {
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="invoice-${paymentId}.pdf"`);
      }
      
      return res.send(result.invoiceData);
    } catch (error: any) {
      console.error("Error downloading invoice (admin):", error);
      return errorResponse(res, error.message || "Failed to download invoice");
    }
  }

  async cancelSubscription(req: AuthenticatedRequest, res: Response) {
    try {
      const subscriptionId = req.params.id;
      const clientId = req.user?.userId;
      const { reason } = req.body;

      if (!subscriptionId) {
        return errorResponse(res, "Subscription ID is required");
      }

      // Validate reason if provided
      if (reason && (typeof reason !== 'string' || reason.length > 500)) {
        return errorResponse(res, "Reason must be a string with maximum 500 characters");
      }

      const result = await this.billingService.cancelSubscription(subscriptionId, clientId, reason);

      return successResponseWithData(res, "Subscription cancelled successfully", result);
    } catch (error: any) {
      console.error("Error cancelling subscription:", error);
      return errorResponse(res, error.message || "Failed to cancel subscription");
    }
  }

  async cancelSubscriptionAdmin(req: AuthenticatedRequest, res: Response) {
    try {
      const subscriptionId = req.params.id;
      const { reason } = req.body;

      if (!subscriptionId) {
        return errorResponse(res, "Subscription ID is required");
      }

      // Validate reason if provided
      if (reason && (typeof reason !== 'string' || reason.length > 500)) {
        return errorResponse(res, "Reason must be a string with maximum 500 characters");
      }

      // Admin can cancel any subscription, so don't pass clientId
      const result = await this.billingService.cancelSubscription(subscriptionId, undefined, reason);

      return successResponseWithData(res, "Subscription cancelled successfully", result);
    } catch (error: any) {
      console.error("Error cancelling subscription (admin):", error);
      return errorResponse(res, error.message || "Failed to cancel subscription");
    }
  }

  async getSubscriptionDetails(req: AuthenticatedRequest, res: Response) {
    try {
      const subscriptionId = req.params.id;
      const clientId = req.user?.userId;

      if (!subscriptionId) {
        return errorResponse(res, "Subscription ID is required");
      }

      const result = await this.billingService.getSubscriptionDetails(subscriptionId, clientId);

      return successResponseWithData(res, "Subscription details fetched successfully", result);
    } catch (error: any) {
      console.error("Error fetching subscription details:", error);
      return errorResponse(res, error.message || "Failed to fetch subscription details");
    }
  }

  async getSubscriptionDetailsAdmin(req: AuthenticatedRequest, res: Response) {
    try {
      const subscriptionId = req.params.id;

      if (!subscriptionId) {
        return errorResponse(res, "Subscription ID is required");
      }

      // Admin can access any subscription, so don't pass clientId
      const result = await this.billingService.getSubscriptionDetails(subscriptionId);

      return successResponseWithData(res, "Subscription details fetched successfully", result);
    } catch (error: any) {
      console.error("Error fetching subscription details (admin):", error);
      return errorResponse(res, error.message || "Failed to fetch subscription details");
    }
  }
}
