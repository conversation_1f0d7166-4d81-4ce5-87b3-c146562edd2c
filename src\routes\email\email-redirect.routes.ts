import { Router } from 'express';
import { EmailRedirectController } from '../../controllers/email/email-redirect.controller';
import { asyncHandler } from '../../middlewares/asyncHandler';

const router = Router();
const emailRedirectController = new EmailRedirectController();

// Test route
router.get('/test', (req, res) => {
  res.json({ message: 'Email redirect routes working!' });
});

/**
 * @swagger
 * /api/email/redirect/coworker-invite:
 *   get:
 *     summary: Redirect coworker invitation from email
 *     description: Validates invitation token and redirects to frontend accept-invite page
 *     tags: [Email Redirect]
 *     parameters:
 *       - in: query
 *         name: email
 *         required: true
 *         schema:
 *           type: string
 *         description: Coworker email address
 *       - in: query
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *         description: Invitation token
 *     responses:
 *       302:
 *         description: Redirects to frontend with appropriate parameters
 *       400:
 *         description: Invalid parameters
 */
router.get('/coworker-invite', as<PERSON><PERSON><PERSON><PERSON>(emailRedirectController.redirectCoworkerInvite.bind(emailRedirectController)));

/**
 * @swagger
 * /api/email/redirect:
 *   get:
 *     summary: General email link tracking and redirection
 *     description: Tracks email link clicks and redirects to the original URL
 *     tags: [Email Redirect]
 *     parameters:
 *       - in: query
 *         name: url
 *         required: true
 *         schema:
 *           type: string
 *         description: The URL to redirect to (URL encoded)
 *       - in: query
 *         name: emailId
 *         required: false
 *         schema:
 *           type: string
 *         description: Email ID for tracking purposes
 *     responses:
 *       302:
 *         description: Redirects to the specified URL
 *       400:
 *         description: Invalid parameters
 */
router.get('/', asyncHandler(emailRedirectController.trackAndRedirect.bind(emailRedirectController)));

/**
 * @swagger
 * /api/email/track/open/{emailId}:
 *   get:
 *     summary: Track email open (tracking pixel)
 *     description: Records email open event and returns a 1x1 transparent pixel
 *     tags: [Email Tracking]
 *     parameters:
 *       - in: path
 *         name: emailId
 *         required: true
 *         schema:
 *           type: string
 *         description: Email ID to track
 *     responses:
 *       200:
 *         description: Returns a 1x1 transparent PNG pixel
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 */
router.get('/track/open/:emailId', asyncHandler(emailRedirectController.trackEmailOpen.bind(emailRedirectController)));

/**
 * @swagger
 * /api/email/track/click/{emailId}:
 *   get:
 *     summary: Track email click and redirect
 *     description: Records email click event and redirects to the original URL
 *     tags: [Email Tracking]
 *     parameters:
 *       - in: path
 *         name: emailId
 *         required: true
 *         schema:
 *           type: string
 *         description: Email ID to track
 *       - in: query
 *         name: url
 *         required: true
 *         schema:
 *           type: string
 *         description: The URL to redirect to (URL encoded)
 *     responses:
 *       302:
 *         description: Redirects to the specified URL
 *       400:
 *         description: Invalid parameters
 */
router.get('/track/click/:emailId', asyncHandler(emailRedirectController.trackAndRedirect.bind(emailRedirectController)));

export default router;
