import prisma from "../../prisma";

export class AdminTaskService {
  async create(data: {
    name: string;
    description: string;
    color: string;
    priority: "LOW" | "MEDIUM" | "HIGH";
    assignedToId: string;
    createdById: string;
    startDate: Date;
    dueDate: Date;
  }) {
    return prisma.adminTask.create({
      data: {
        name: data.name,
        description: data.description,
        color: data.color,
        priority: data.priority,
        assignedToId: data.assignedToId,
        createdById: data.createdById,
        startDate: data.startDate,
        dueDate: data.dueDate
      }
    });
  }

  async getAll() {
    return prisma.adminTask.findMany({
      include: {
        assignedTo: true,
        createdBy: true,
      }
    });
  }

  async getById(id: string) {
    return prisma.adminTask.findUnique({ where: { id } });
  }

  async update(id: string, data: Partial<{
    name: string;
    description: string;
    color: string;
    priority: "LOW" | "MEDIUM" | "HIGH";
    status: "PENDING" | "IN_PROGRESS" | "COMPLETED";
    startDate: Date;
    dueDate: Date;
  }>) {
    return prisma.adminTask.update({
      where: { id },
      data
    });
  }

  async delete(id: string) {
    return prisma.adminTask.delete({ where: { id } });
  }
}
