/**
 * Example script to create a Zoho Desk ticket using the exact format expected by Zoho Desk API
 * 
 * Usage: node zoho-desk-ticket-exact-format.js
 */

const axios = require('axios');

async function createZohoTicket() {
  try {
    // Replace with your API base URL and authentication token
    const baseUrl = 'http://localhost:3000/api';
    const authToken = 'YOUR_AUTH_TOKEN'; // Replace with your actual auth token
    
    // This structure exactly matches the Zoho Desk API requirements
    const ticketData = {
      // Core ticket fields
      subject: "Real Time analysis Requirement",
      description: "Hai This is Description",
      departmentId: "1892000000006907",
      contactId: "1892000000042032",
      assigneeId: "1892000000056007",
      email: "<EMAIL>",
      phone: "**************",
      status: "Open",
      priority: "High",
      category: "general",
      subCategory: "Sub General",
      dueDate: "2016-06-21T16:16:16.000Z",
      language: "English",
      channel: "Email",
      classification: "",
      productId: "",
      
      // Entity skills as an array of strings
      entitySkills: [
        "18921000000379001", 
        "18921000000364001", 
        "18921000000379055", 
        "18921000000379031"
      ],
      
      // Custom fields using the 'cf' object
      cf: {
        cf_permanentaddress: null,
        cf_dateofpurchase: null,
        cf_phone: null,
        cf_numberofitems: null,
        cf_url: null,
        cf_secondaryemail: null,
        cf_severitypercentage: "0.0",
        cf_modelname: "F3 2017"
      }
    };

    console.log('Sending ticket data:', JSON.stringify(ticketData, null, 2));

    const response = await axios.post(`${baseUrl}/zoho-desk/tickets`, ticketData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      }
    });

    console.log('Ticket created successfully:');
    console.log(JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('Error creating ticket:');
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error:', error.message);
    }
  }
}

createZohoTicket();