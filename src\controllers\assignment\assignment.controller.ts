import { Request, Response } from "express";
import { JwtPayload } from "jsonwebtoken";
import { AssignmentService } from "../../services/assignment/assignment.service";
import { AuthenticatedRequest } from "../../middlewares/checkAuth";

const assignmentService = new AssignmentService();

export class AssignmentController {
  async getClientsWithPlans(
    req: AuthenticatedRequest,
    res: Response
  ): Promise<void> {
    const client = await assignmentService.getClientsData(req.query);

    res.status(201).json({
      message: "Annotator created successfully",
      data: client,
    });
  }

  async assignDeveloper(req: AuthenticatedRequest, res: Response) {
    const {
      developerId,
      coordinatorId,
      availableFrom,
      availableTo,
      packageId,
    } = req.body;
    const { id } = req.params;

    const client = await assignmentService.assignTeam({
      userId: id,
      developerId,
      packageId,
      coordinatorId,
      availableFrom,
      availableTo,
    });

    res
      .status(200)
      .json({ message: "Developer assigned successfully", client });
  }

  async getQuestionariesDetails(req: AuthenticatedRequest, res: Response) {
    const { subscriptionId } = req.params;
    const getDetails = await assignmentService.getQuestionariesData(
      subscriptionId
    );
    res.status(200).json({
      message: "Questionaries Data fetched Successfully",
      getDetails,
    });
  }
}
