.zoho-ticket-comments-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 1.5rem;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.zoho-ticket-comments-container h3 {
  margin-bottom: 1.5rem;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.75rem;
}

.comments-list {
  margin-bottom: 2rem;
}

.loading-comments,
.no-comments,
.no-ticket-selected {
  padding: 1.5rem;
  text-align: center;
  color: #777;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.comment-item {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #f9f9f9;
  border-radius: 6px;
  border-left: 4px solid #4a90e2;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
}

.comment-author {
  font-weight: 600;
  color: #333;
}

.comment-time {
  margin-left: auto;
  color: #777;
}

.comment-private {
  margin-left: 0.75rem;
  background-color: #f1f1f1;
  color: #777;
  padding: 0.2rem 0.5rem;
  border-radius: 3px;
  font-size: 0.8rem;
}

.comment-content {
  color: #444;
  line-height: 1.5;
  white-space: pre-wrap;
}

.comment-form textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  min-height: 100px;
  margin-bottom: 1rem;
  transition: border-color 0.2s;
  resize: vertical;
}

.comment-form textarea:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.comment-form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.visibility-toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.visibility-toggle input {
  margin-right: 0.5rem;
}

.comment-form button {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.6rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.comment-form button:hover {
  background-color: #3a7bc8;
}

.comment-form button:disabled {
  background-color: #a0c0e8;
  cursor: not-allowed;
}

.alert-error {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ef9a9a;
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: 4px;
}