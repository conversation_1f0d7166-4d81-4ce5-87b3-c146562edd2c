export const passwordchangedTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Password Changed</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #171617;
            padding: 20px;
            text-align: center;
        }
        .logo {
            max-width: 140px;
            height: auto;
            display: block;
            margin: 0 auto;
        }
        .content {
            padding: 30px;
        }
        .success-banner {
            background-color: #f0fdf4;
            border-left: 4px solid #059669;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        .success-banner-title {
            font-size: 20px;
            font-weight: 600;
            color: #059669;
            padding-bottom: 10px;
        }
        .security-notice {
            background-color: #fef3c7;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 2px solid #92400e;
        }
        .security-notice p {
            font-size: 14px;
            line-height: 22px;
            color: #92400e;
            margin: 0;
        }
        .contact-support {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background-color: #1a3c34;
            color: #ffffff !important;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
        }
        .signature {
            font-size: 14px;
            line-height: 22px;
            color: #4b5563;
            margin: 0;
        }
        .signature span {
            color: #1a3c34;
            font-weight: 600;
        }
        .footer {
            padding: 20px;
            text-align: center;
            font-size: 13px;
            line-height: 20px;
            background-color: #171617;
            color: #ffffff;
        }
        .footer a {
            color: #e1e1e1 !important;
            text-decoration: none;
        }
        .footer span {
            color: #f0f0f0;
        }
        /* Email client compatibility */
        table {
            width: 100%;
            border-collapse: collapse;
        }
        @media only screen and (max-width: 600px) {
            .container {
                padding: 15px;
            }
            .content {
                padding: 20px;
            }
            .header {
                padding: 15px;
            }
            .success-banner-title {
                font-size: 18px;
            }
            .cta-button {
                width: 100%;
                box-sizing: border-box;
            }
        }
        /* Accessibility */
        a:focus, .cta-button:focus {
            outline: 2px solid #1a3c34;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <table role="presentation" class="container">
        <tr>
            <td>
                <div class="header">
                    <img src="https://macgence.s3.ap-south-1.amazonaws.com/whitemode.png" alt="GetAnnotator Logo" class="logo">
                </div>

                <div class="content">
                    <div class="success-banner">
                        <div class="success-banner-title">🔐 Password Successfully Changed</div>
                    </div>

                    <p>Hi {{ params.firstName }},</p>
                    <p>Your password has been successfully changed.</p>

                    <div class="security-notice">
                        <p><strong>🔒 Security Notice:</strong> If you did not make this change, please contact our support team immediately and secure your account via our <a href="http://app.getannotator.com/contact" style="color: #1a3c34; text-decoration: none;">contact form</a>.</p>
                    </div>

                    <div class="contact-support">
                        <a href="http://app.getannotator.com/contact" class="cta-button">📧 Contact Support</a>
                    </div>

                    <p class="signature">
                        Best Regards,<br>
                        <span>The Security Team</span>
                    </p>
                </div>

                <div class="footer">
                    <span><EMAIL></span> | 
                    <a href="http://app.getannotator.com/" style="color: #e1e1e1ff; text-decoration: none;">app.getannotator.com</a> | 
                    <a href="tel:+***********">******-209-8904</a><br>
                    <span>Need help? Reach us via our <a href="http://app.getannotator.com/contact">contact form</a>.</span>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>
`;
