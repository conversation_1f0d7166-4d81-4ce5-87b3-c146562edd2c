# Invoice Download API

This API allows clients and administrators to download invoices for payments made through Dodo and PayPal providers.

## Endpoints

### Client Invoice Download
```
GET /api/billings/invoice/:id
```

**Description**: Allows clients to download invoices for their own payments.

**Authentication**: Required (CLIENT role)

**Parameters**:
- `id` (path parameter): The payment ID

**Response**:
- Success: Returns the invoice as a PDF file or text receipt
- Error: JSON error response

**Example**:
```bash
curl -X GET "http://localhost:3000/api/billings/invoice/pay_123456" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  --output invoice.pdf
```

### Admin Invoice Download
```
GET /api/billings/admin/invoice/:id
```

**Description**: Allows administrators to download invoices for any payment.

**Authentication**: Required (ADMIN role)

**Parameters**:
- `id` (path parameter): The payment ID

**Response**:
- Success: Returns the invoice as a PDF file or text receipt
- Error: JSON error response

**Example**:
```bash
curl -X GET "http://localhost:3000/api/billings/admin/invoice/pay_123456" \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN" \
  --output invoice.pdf
```

## Supported Providers

The API supports invoice downloads for the following payment providers:
- **Dodo**: Fetches invoices from Dodo Payments API
- **PayPal**: Fetches invoices from PayPal API

**Note**: Bank transfer payments are not supported for invoice downloads.

## Response Types

### PDF Invoice
When a PDF invoice is available from the payment provider:
- Content-Type: `application/pdf`
- Content-Disposition: `attachment; filename="invoice-{paymentId}.pdf"`

### Text Receipt (Fallback)
When PDF invoices are not available, a text receipt is generated:
- Content-Type: `text/plain`
- Content-Disposition: `attachment; filename="receipt-{paymentId}.txt"`

The text receipt includes:
- Payment details (ID, amount, status, method)
- Customer information
- Package information (if applicable)
- Generation timestamp

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "message": "Payment ID is required"
}
```

### 403 Forbidden
```json
{
  "success": false,
  "message": "Invoice not found or access denied"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Invoice not found"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Failed to download invoice"
}
```

## Implementation Details

### API Endpoint Fallbacks

#### Dodo Payments
The system tries multiple endpoints in order:
1. `/payments/{paymentId}/invoice`
2. `/invoices/{paymentId}`
3. `/payments/{paymentId}/receipt`

#### PayPal
The system tries multiple endpoints in order:
1. `/v2/invoicing/invoices/{paymentId}/pdf`
2. `/v2/payments/payment/{paymentId}/receipt`
3. `/v1/billing/subscriptions/{paymentId}/transactions`
4. `/v2/checkout/orders/{paymentId}/receipt`

### Security

- Clients can only access invoices for their own payments
- Administrators can access invoices for any payment
- Only payments from DODO and PAYPAL providers are supported
- JWT authentication is required for all endpoints

### Logging

The system logs:
- Invoice fetch attempts and results
- API errors from payment providers
- Fallback receipt generation

## Testing

Use the provided test script:
```bash
# Set environment variables
export TEST_AUTH_TOKEN="your_jwt_token"
export TEST_PAYMENT_ID="your_payment_id"

# Run the test
node test-invoice-download.js
```

## Environment Variables

Ensure the following environment variables are set:
- `DODO_BASE_URL`: Dodo Payments API base URL
- `DODO_PAYMENTS_API_KEY`: Dodo Payments API key
- `PAYPAL_API`: PayPal API base URL
- `PAYPAL_CLIENT_ID`: PayPal client ID
- `PAYPAL_CLIENT_SECRET`: PayPal client secret