import { Router } from "express";
import { Project<PERSON>ontroller } from "../../controllers/projects/project.controller";
import { asyncHand<PERSON> } from "../../middlewares/asyncHandler";
import authMiddleware from "../../middlewares/checkAuth";
import { subscriptionMiddleware } from "../../middlewares/subscription.middleware";
import upload from "../../utils/multer";

const router = Router();
const controller = new ProjectController();

router.post(
  "/create-project",
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  upload.array("attachment", 10),
  asyncHandler(controller.create.bind(controller))
);
router.post(
  "/cooworker/create-project",
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  upload.array("attachment", 10),
  async<PERSON>andler(controller.createdByCoowrker.bind(controller))
);
router.get(
  "/get-all-projects",
  asyncHandler(controller.getAll.bind(controller))
);
router.get(
  "/project-by-id/:id",
  async<PERSON>andler(controller.getById.bind(controller))
);
router.put(
  "/update-project/:id",
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  upload.array("attachment", 10),
  asyncHandler(controller.update.bind(controller))
);
router.delete(
  "/delete-project/:id",
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  asyncHandler(controller.delete.bind(controller))
);

router.get(
  "/client-projects",
  authMiddleware,
  asyncHandler(controller.getCreatedProjectsByUser.bind(controller))
);

router.get(
  "/cooworker/client-projects",
  authMiddleware,
  asyncHandler(controller.getCreatedProjectsByUserCooWorker.bind(controller))
);

router.get(
  "/annotator-projects",
  authMiddleware,
  asyncHandler(controller.getAnnotatorProjects.bind(controller))
);

router.get(
  "/projects-by-client/:clientId",
  authMiddleware,
  asyncHandler(controller.getProjectsByClientId.bind(controller))
);

export default router;
