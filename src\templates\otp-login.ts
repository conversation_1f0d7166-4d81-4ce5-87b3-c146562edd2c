export const otpLoginTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Login OTP</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #171617;
            padding: 20px;
            text-align: center;
        }
        .logo {
            max-width: 140px;
            height: auto;
            display: block;
            margin: 0 auto;
        }
        .content {
            padding: 30px;
            background-color: #ffffff;
        }
        .otp-banner {
            background-color: #f0f9ff;
            border-left: 4px solid #0ea5e9;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        .otp-banner-title {
            font-size: 20px;
            font-weight: 600;
            color: #0ea5e9;
            padding-bottom: 10px;
        }
        .otp-code {
            background-color: #f8fafc;
            border: 2px dashed #0ea5e9;
            padding: 20px;
            text-align: center;
            margin: 30px 0;
            border-radius: 8px;
        }
        .otp-number {
            font-size: 32px;
            font-weight: bold;
            color: #0ea5e9;
            letter-spacing: 4px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }
        .warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .signature {
            font-size: 14px;
            line-height: 22px;
            color: #4b5563;
            margin: 0;
        }
        .signature span {
            color: #1a3c34;
            font-weight: 600;
        }
        .footer {
            padding: 20px;
            text-align: center;
            font-size: 13px;
            line-height: 20px;
            background-color: #171617;
            color: #ffffff;
        }
        .footer a {
            color: #e1e1e1 !important;
            text-decoration: none;
        }
        .footer span {
            color: #f0f0f0;
        }
        /* Email client compatibility */
        table {
            width: 100%;
            border-collapse: collapse;
        }
        @media only screen and (max-width: 600px) {
            .container {
                padding: 15px;
            }
            .content {
                padding: 20px;
            }
            .header {
                padding: 15px;
            }
            .otp-banner-title {
                font-size: 18px;
            }
            .otp-number {
                font-size: 28px;
                letter-spacing: 2px;
            }
        }
        /* Accessibility */
        a:focus {
            outline: 2px solid #1a3c34;
            outline-offset: 2px;
        }
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #1a1a1a;
            }
            .container {
                background-color: #ffffff;
            }
            .content {
                background-color: #ffffff;
                color: #000000;
            }
            .otp-banner {
                background-color: #1e3a8a;
                border-left-color: #0ea5e9;
            }
            .otp-banner-title {
                color: #ffffff;
            }
            .signature {
                color: #999999;
            }
            .signature span {
                color: #000000;
            }
        }
    </style>
</head>
<body>
    <table role="presentation" class="container">
        <tr>
            <td>
                <div class="header">
                    <img src="https://macgence.s3.ap-south-1.amazonaws.com/whitemode.png" alt="Logo" class="logo">
                </div>

                <div class="content">
                    <div class="otp-banner">
                        <div class="otp-banner-title">🔐 Login OTP</div>
                    </div>

                    <p>Hello {{customerName}},</p>
                    <p>Use this One-Time Password (OTP) to login to your account:</p>

                    <div class="otp-code">
                        <div>🔑 Your OTP Code</div>
                        <div class="otp-number">{{otpCode}}</div>
                    </div>

                    <p>This code is valid for <strong>{{timeLimit}} minutes</strong>. Please do not share this code with anyone for security reasons.</p>

                    <div class="warning">
                        <strong>⚠️ Security Notice:</strong> If you did not request this code, please ignore this email or contact our support team immediately.
                    </div>

                    <p class="signature">
                        Best Regards,<br>
                        <span>{{companyName}} Team</span>
                    </p>
                </div>

                <div class="footer">
                    <span><EMAIL></span> | 
                    <a href="http://app.getannotator.com" style="color: #e1e1e1ff; text-decoration: none;">app.getannotator.com</a> | 
                    <a href="tel:*************">*************</a><br>
                    <span>Need help? Reach us via our <a href="http://getannotator.com"/contact">contact form</a>.</span>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>
`;
