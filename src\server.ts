import dotenv from "dotenv";
import TaskManagementApp from "./app";
import prisma from "./prisma";

dotenv.config();

const app = new TaskManagementApp();
const port = process.env.PORT || 3100;

const server = app.start().listen(port, () => {
  console.log(`Server is running on port ${port}`);
});

// Graceful shutdown
async function shutdown() {
  console.log("Shutting down server...");
  try {
    await prisma.$disconnect();
    app.stop();
    process.exit(0);
  } catch (err) {
    console.error("Error during shutdown:", err);
    process.exit(1);
  }
}

process.on("uncaughtException", (err) => {
  console.error("Uncaught Exception:", err);
  shutdown();
});

process.on("unhandledRejection", (err) => {
  console.error("Unhandled Rejection:", err);
  shutdown();
});

process.on("SIGINT", shutdown);
process.on("SIGTERM", shutdown);