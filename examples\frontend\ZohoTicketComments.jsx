import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './ZohoTicketComments.css';

const ZohoTicketComments = ({ ticketId, authToken, onError }) => {
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [isPublic, setIsPublic] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);

  // Fetch comments when component mounts or ticketId changes
  useEffect(() => {
    if (!ticketId) return;
    
    const fetchComments = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await axios.get(`/api/zoho-desk/tickets/${ticketId}/comments`, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });
        
        setComments(response.data.data.comments || []);
      } catch (err) {
        console.error('Failed to fetch comments:', err);
        setError('Failed to load comments. Please try again later.');
        
        if (onError) {
          onError(err);
        }
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchComments();
  }, [ticketId, authToken, onError]);

  const handleSubmitComment = async (e) => {
    e.preventDefault();
    
    if (!newComment.trim()) return;
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      const commentData = {
        content: newComment,
        contentType: 'plainText',
        isPublic
      };
      
      const response = await axios.post(
        `/api/zoho-desk/tickets/${ticketId}/comments`,
        commentData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
          }
        }
      );
      
      // Add the new comment to the list
      setComments(prevComments => [
        ...prevComments,
        response.data.data
      ]);
      
      // Clear the comment input
      setNewComment('');
    } catch (err) {
      console.error('Failed to add comment:', err);
      setError(err.response?.data?.message || 'Failed to add comment. Please try again.');
      
      if (onError) {
        onError(err);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  if (!ticketId) {
    return <div className="no-ticket-selected">No ticket selected</div>;
  }

  return (
    <div className="zoho-ticket-comments-container">
      <h3>Comments</h3>
      
      {error && (
        <div className="alert alert-error">
          {error}
        </div>
      )}
      
      <div className="comments-list">
        {isLoading ? (
          <div className="loading-comments">Loading comments...</div>
        ) : comments.length === 0 ? (
          <div className="no-comments">No comments yet</div>
        ) : (
          comments.map(comment => (
            <div key={comment.id} className="comment-item">
              <div className="comment-header">
                <span className="comment-author">{comment.authorName || 'User'}</span>
                <span className="comment-time">{formatDate(comment.createdTime)}</span>
                {!comment.isPublic && <span className="comment-private">Private</span>}
              </div>
              <div className="comment-content">{comment.content}</div>
            </div>
          ))
        )}
      </div>
      
      <form onSubmit={handleSubmitComment} className="comment-form">
        <textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="Add a comment..."
          required
        />
        
        <div className="comment-form-actions">
          <label className="visibility-toggle">
            <input
              type="checkbox"
              checked={isPublic}
              onChange={() => setIsPublic(!isPublic)}
            />
            <span>Public comment</span>
          </label>
          
          <button 
            type="submit" 
            disabled={isSubmitting || !newComment.trim()}
          >
            {isSubmitting ? 'Sending...' : 'Add Comment'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ZohoTicketComments;