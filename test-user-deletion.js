const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000'; // Adjust to your server URL
const ADMIN_EMAIL = '<EMAIL>'; // Replace with actual admin email
const ADMIN_PASSWORD = 'admin123'; // Replace with actual admin password

let adminToken = '';
let testUserId = '';

// Helper function to make authenticated requests
const makeAuthenticatedRequest = async (method, url, data = null) => {
  const config = {
    method,
    url: `${BASE_URL}${url}`,
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  return axios(config);
};

// Test user deletion functionality
async function testUserDeletion() {
  try {
    console.log('🧪 Testing User Deletion System...\n');

    // Step 1: Admin login
    console.log('1. Admin Login...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD
    });
    
    if (loginResponse.data.status === 'true') {
      console.log('   ✅ OTP sent to admin email');
      
      // You'll need to manually get the OTP from email and verify
      const otp = prompt('Enter OTP from admin email: ');
      
      const verifyResponse = await axios.post(`${BASE_URL}/auth/verify-login-otp`, {
        email: ADMIN_EMAIL,
        otp: otp
      });
      
      adminToken = verifyResponse.data.token;
      console.log('   ✅ Admin logged in successfully');
    }

    // Step 2: Create a test user
    console.log('\n2. Creating test user...');
    const createUserResponse = await makeAuthenticatedRequest('POST', '/onboarding/create', {
      name: 'Test User for Deletion',
      email: '<EMAIL>',
      password: 'testpass123',
      role: 'ANNOTATOR'
    });
    
    testUserId = createUserResponse.data.user.id;
    console.log(`   ✅ Test user created with ID: ${testUserId}`);

    // Step 3: Get deletion preview
    console.log('\n3. Getting deletion preview...');
    const previewResponse = await makeAuthenticatedRequest('GET', `/onboarding/deletion-preview/${testUserId}`);
    
    console.log('   ✅ Deletion preview retrieved');
    console.log('   📊 Relationship Summary:', previewResponse.data.relationshipSummary);

    // Step 4: Test soft deletion (default)
    console.log('\n4. Testing soft deletion...');
    const softDeleteResponse = await makeAuthenticatedRequest('DELETE', `/onboarding/delete/${testUserId}`, {
      deleteType: 'soft'
    });
    
    console.log('   ✅ Soft deletion completed');
    console.log('   📝 Response:', {
      message: softDeleteResponse.data.message,
      deleteType: softDeleteResponse.data.deleteType,
      accountStatus: softDeleteResponse.data.user.accountStatus,
      isDeleted: softDeleteResponse.data.user.isDeleted
    });

    // Step 5: Verify user cannot login after soft delete
    console.log('\n5. Testing login prevention after soft delete...');
    try {
      const blockedLoginResponse = await axios.post(`${BASE_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'testpass123'
      });
      console.log('   ❌ Soft deleted user was able to login (this should not happen)');
    } catch (error) {
      if (error.response && error.response.status === 403) {
        console.log('   ✅ Soft deleted user login correctly prevented');
        console.log('   📝 Error message:', error.response.data.message);
      } else {
        console.log('   ⚠️  Unexpected error:', error.response?.data || error.message);
      }
    }

    // Step 6: Create another test user for hard deletion
    console.log('\n6. Creating another test user for hard deletion...');
    const createUser2Response = await makeAuthenticatedRequest('POST', '/onboarding/create', {
      name: 'Test User for Hard Deletion',
      email: '<EMAIL>',
      password: 'testpass123',
      role: 'PROJECT_COORDINATOR'
    });
    
    const testUser2Id = createUser2Response.data.user.id;
    console.log(`   ✅ Second test user created with ID: ${testUser2Id}`);

    // Step 7: Test hard deletion with confirmation
    console.log('\n7. Testing hard deletion...');
    const hardDeleteResponse = await makeAuthenticatedRequest('DELETE', `/onboarding/hard-delete/${testUser2Id}`, {
      confirmDelete: 'YES_DELETE_ALL_DATA'
    });
    
    console.log('   ✅ Hard deletion completed');
    console.log('   📝 Response:', {
      message: hardDeleteResponse.data.message,
      deleteType: hardDeleteResponse.data.deleteType,
      relationshipSummary: hardDeleteResponse.data.relationshipSummary
    });

    // Step 8: Test hard deletion without proper confirmation (should fail)
    console.log('\n8. Testing hard deletion without confirmation...');
    try {
      // Create another user first
      const createUser3Response = await makeAuthenticatedRequest('POST', '/onboarding/create', {
        name: 'Test User 3',
        email: '<EMAIL>',
        password: 'testpass123',
        role: 'CLIENT'
      });
      
      const testUser3Id = createUser3Response.data.user.id;
      
      await makeAuthenticatedRequest('DELETE', `/onboarding/hard-delete/${testUser3Id}`, {
        confirmDelete: 'WRONG_CONFIRMATION'
      });
      console.log('   ❌ Hard deletion without proper confirmation succeeded (should have failed)');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('   ✅ Hard deletion correctly requires proper confirmation');
        console.log('   📝 Error message:', error.response.data.message);
      } else {
        console.log('   ⚠️  Unexpected error:', error.response?.data || error.message);
      }
    }

    // Step 9: Test deletion of different user types
    console.log('\n9. Testing deletion of different user types...');
    const userTypes = ['CLIENT', 'COWORKER'];
    
    for (const userType of userTypes) {
      try {
        console.log(`   Testing ${userType}...`);
        
        // Create user
        const createResponse = await makeAuthenticatedRequest('POST', '/onboarding/create', {
          name: `Test ${userType}`,
          email: `test${userType.toLowerCase()}<EMAIL>`,
          password: 'testpass123',
          role: userType
        });
        
        const userId = createResponse.data.user.id;
        console.log(`     ✅ ${userType} created`);
        
        // Get preview
        const preview = await makeAuthenticatedRequest('GET', `/onboarding/deletion-preview/${userId}`);
        console.log(`     📊 ${userType} relationships:`, preview.data.relationshipSummary);
        
        // Soft delete
        await makeAuthenticatedRequest('DELETE', `/onboarding/delete/${userId}`, {
          deleteType: 'soft'
        });
        console.log(`     ✅ ${userType} soft deleted`);
        
      } catch (error) {
        console.log(`     ❌ Error testing ${userType}:`, error.response?.data?.message || error.message);
      }
    }

    // Step 10: Test admin protection
    console.log('\n10. Testing admin deletion protection...');
    try {
      // Try to get admin user ID
      const usersResponse = await makeAuthenticatedRequest('GET', '/onboarding/getall?role=ADMIN');
      const adminUser = usersResponse.data.data.data.find(user => user.role === 'ADMIN');
      
      if (adminUser) {
        await makeAuthenticatedRequest('DELETE', `/onboarding/delete/${adminUser.id}`, {
          deleteType: 'soft'
        });
        console.log('   ❌ Admin deletion succeeded (should have failed)');
      }
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('   ✅ Admin users are protected from deletion');
        console.log('   📝 Error message:', error.response.data.message);
      } else {
        console.log('   ⚠️  Unexpected error:', error.response?.data || error.message);
      }
    }

    console.log('\n🎉 All deletion tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Test relationship handling
async function testRelationshipHandling() {
  console.log('\n🔗 Testing Relationship Handling...\n');
  
  try {
    // This would require creating actual relationships (tasks, projects, etc.)
    // For now, we'll just test the preview functionality
    
    console.log('1. Testing relationship detection...');
    const usersResponse = await makeAuthenticatedRequest('GET', '/onboarding/getall');
    const users = usersResponse.data.data.data;
    
    for (const user of users.slice(0, 3)) { // Test first 3 users
      if (user.role !== 'ADMIN') {
        const preview = await makeAuthenticatedRequest('GET', `/onboarding/deletion-preview/${user.id}`);
        console.log(`   User ${user.name} (${user.role}):`, preview.data.relationshipSummary);
      }
    }
    
    console.log('   ✅ Relationship detection working');
    
  } catch (error) {
    console.error('❌ Relationship test failed:', error.response?.data || error.message);
  }
}

// Run tests
if (require.main === module) {
  testUserDeletion()
    .then(() => testRelationshipHandling())
    .catch(console.error);
}

module.exports = { testUserDeletion, testRelationshipHandling };