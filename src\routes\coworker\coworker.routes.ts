import { Router } from "express";
import { <PERSON><PERSON>er<PERSON>ontroller } from "../../controllers/coworker/coworker.controller";
import { asyncHandler } from "../../middlewares/asyncHandler";
import authMiddleware from "../../middlewares/checkAuth";
import { hasRole } from "../../middlewares/checkRole";
import { subscriptionMiddleware } from "../../middlewares/subscription.middleware";

const router = Router();
const coworkerController = new CoworkerController();

router.post(
  "/invite-coworker",
  authMiddleware,
  hasRole("CLIENT"),
  subscriptionMiddleware.requireActiveSubscription,
  asyncHandler(coworkerController.inviteCoworker.bind(coworkerController))
);

router.get(
  "/get-coworkers",
  authMiddleware,
  hasRole("CLIENT"),
  asyncHandler(coworkerController.listCoworkers.bind(coworkerController))
);
router.patch(
  "/:id/permission",
  authMiddleware,
  hasRole("CLIENT"),
  subscriptionMiddleware.requireActiveSubscription,
  asyncHandler(
    coworkerController.updateCoworkerPermission.bind(coworkerController)
  )
);
router.post(
  "/:id/resend-invite",
  authMiddleware,
  hasRole("CLIENT"),
  subscriptionMiddleware.requireActiveSubscription,
  asyncHandler(coworkerController.resendInvite.bind(coworkerController))
);
router.post(
  "/accept-invite",
  // authMiddleware,
  // hasRole("COWORKER"),
  asyncHandler(coworkerController.acceptInvite.bind(coworkerController))
);

export default router;
