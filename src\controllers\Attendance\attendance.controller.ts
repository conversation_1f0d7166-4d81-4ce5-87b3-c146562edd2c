import { Request, Response } from "express";
import { JwtPayload } from "jsonwebtoken";
import attendanceService from "../../services/Attendance/attendance.service";
import * as ApiResponse from "../../helper/apiResponse";

interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}

class AttendanceController {
  async clockIn(req: AuthenticatedRequest, res: Response) {
    // try {
      const data = await attendanceService.clockIn(req.user?.userId);
      return ApiResponse.successResponseWithData(res, "Clock-in successful", data);
    // } catch (error: any) {
    //   return ApiResponse.errorResponse(res, error.message);
    // }
  }

  async clockOut(req: AuthenticatedRequest, res: Response) {
    try {
      const data = await attendanceService.clockOut(req.user?.userId);
      
      // Provide specific message based on whether break was auto-ended
      let message = "Clock-out successful";
      if (data.breakAutoEnded) {
        message = `Clock-out successful. Break was automatically ended (${data.breakDuration} minutes consumed)`;
      }
      
      return ApiResponse.successResponseWithData(res, message, data);
    } catch (error: any) {
      return ApiResponse.errorResponse(res, error.message);
    }
  }

  async startBreak(req: AuthenticatedRequest, res: Response) {
    try {
      const data = await attendanceService.startBreak(req.user?.userId);
      return ApiResponse.successResponseWithData(res, "Break started", data);
    } catch (error: any) {
      return ApiResponse.errorResponse(res, error.message);
    }
  }

  async endBreak(req: AuthenticatedRequest, res: Response) {
    try {
      const data = await attendanceService.endBreak(req.user?.userId);
      return ApiResponse.successResponseWithData(res, "Break ended", data);
    } catch (error: any) {
      return ApiResponse.errorResponse(res, error.message);
    }
  }

   async getTodayAttendance(req: AuthenticatedRequest, res: Response) {
    try {
      const data = await attendanceService.getTodayAttendance(req.user?.userId);
      if (!data) {
        return ApiResponse.errorResponse(res, "No attendance data found for today.");
      }
      return ApiResponse.successResponseWithData(res, "Today attendance fetched successfully", data);
    } catch (error: any) {
      return ApiResponse.errorResponse(res, error.message);
    }
  }


  async getUserHistory(req: Request, res: Response) {
    try {
      const { userId } = req.params;  // Getting userId from route params
      const { from, to, page = 1, limit = 10 } = req.query;

      if (!userId) {
        return ApiResponse.errorResponse(res, "User ID is required.");
      }

      // Calling the service with optional 'from' and 'to'
      const data = await attendanceService.getUserHistory(
        userId,
        parseInt(page as string),
        parseInt(limit as string),
        from as string,
        to as string
      );

      return ApiResponse.successResponseWithData(res, "Attendance history fetched successfully", data, data.length);
    } catch (error: any) {
      return ApiResponse.errorResponse(res, error.message);
    }
  }

  async downloadAttendanceReport(req: Request, res: Response) {
    try {
      const { from, to } = req.query;

      const filePath = await attendanceService.downloadAttendanceReport(String(from), String(to));

      res.download(filePath, (err) => {
        if (err) {
          return ApiResponse.errorResponse(res, "Error downloading the report.");
        }
      });
    } catch (error: any) {
      return ApiResponse.errorResponse(res, error.message);
    }
  }

  async getClockLog(req: AuthenticatedRequest, res: Response) {
    try {
      const data = await attendanceService.getClockLog(req.user?.userId);
      if (!data) {
        return ApiResponse.errorResponse(res, "No clock-in log found for today.");
      }
      return ApiResponse.successResponseWithData(res, "Clock-in log fetched successfully", data);
    } catch (error: any) {
      return ApiResponse.errorResponse(res, error.message);
    }
  }

  async getAnnotatorAttendanceHistory(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      const { from, to, page, limit } = req.query;
      
      const data = await attendanceService.getAnnotatorAttendanceHistory(
        userId,
        { from, to, page, limit }
      );
      
      return ApiResponse.successResponseWithData(
        res, 
        "Attendance history fetched successfully", 
        data
      );
    } catch (error: any) {
      return ApiResponse.errorResponse(res, error.message);
    }
  }
}

export default new AttendanceController();
