/**
 * Test script for the billing address API endpoints
 * 
 * Usage: node test-billing-address.js
 */

const axios = require('axios');
require('dotenv').config();

async function testBillingAddressAPI() {
  try {
    const baseUrl = process.env.API_BASE_URL || 'http://localhost:3000';
    const authToken = process.env.TEST_AUTH_TOKEN; // You'll need to provide this
    
    if (!authToken) {
      console.log('Please set TEST_AUTH_TOKEN in your .env file');
      return;
    }

    const headers = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    };

    console.log('Testing billing address API endpoints...\n');
    
    // Test 1: Get existing billing address
    console.log('1. Testing GET /api/auth/getaddress');
    try {
      const getResponse = await axios.get(`${baseUrl}/api/auth/getaddress`, { headers });
      console.log('✅ GET Success:', getResponse.status);
      console.log('Response:', JSON.stringify(getResponse.data, null, 2));
    } catch (error) {
      if (error.response?.status === 404 || error.response?.data?.data === null) {
        console.log('ℹ️  No billing address found (expected for new users)');
      } else {
        console.log('❌ GET Error:', error.response?.status, error.response?.data);
      }
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Create/Update billing address
    console.log('2. Testing POST /api/auth/address');
    const testAddress = {
      country: 'United States',
      address: '123 Main Street, Apt 4B',
      state: 'California',
      postalCodel: '90210', // Note: keeping the typo as it exists in schema
      street: 'Main Street'
    };

    try {
      const postResponse = await axios.post(`${baseUrl}/api/auth/address`, testAddress, { headers });
      console.log('✅ POST Success:', postResponse.status);
      console.log('Response:', JSON.stringify(postResponse.data, null, 2));
    } catch (error) {
      console.log('❌ POST Error:', error.response?.status, error.response?.data);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 3: Get billing address again to verify creation
    console.log('3. Testing GET /api/auth/getaddress (after creation)');
    try {
      const getResponse2 = await axios.get(`${baseUrl}/api/auth/getaddress`, { headers });
      console.log('✅ GET Success:', getResponse2.status);
      console.log('Response:', JSON.stringify(getResponse2.data, null, 2));
    } catch (error) {
      console.log('❌ GET Error:', error.response?.status, error.response?.data);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 4: Update billing address
    console.log('4. Testing PUT /api/auth/address');
    const updatedAddress = {
      country: 'Canada',
      address: '456 Oak Avenue, Suite 10',
      state: 'Ontario',
      postalCodel: 'K1A 0A6',
      street: 'Oak Avenue'
    };

    try {
      const putResponse = await axios.put(`${baseUrl}/api/auth/address`, updatedAddress, { headers });
      console.log('✅ PUT Success:', putResponse.status);
      console.log('Response:', JSON.stringify(putResponse.data, null, 2));
    } catch (error) {
      console.log('❌ PUT Error:', error.response?.status, error.response?.data);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 5: Validation test - missing fields
    console.log('5. Testing validation (missing fields)');
    const invalidAddress = {
      country: 'United States',
      // Missing other required fields
    };

    try {
      const validationResponse = await axios.post(`${baseUrl}/api/auth/address`, invalidAddress, { headers });
      console.log('❌ Validation should have failed but succeeded:', validationResponse.status);
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ Validation working correctly:', error.response.data.message);
      } else {
        console.log('❌ Unexpected validation error:', error.response?.status, error.response?.data);
      }
    }

    console.log('\n✅ All tests completed!');
    
  } catch (error) {
    console.error('❌ Test setup error:', error.message);
  }
}

testBillingAddressAPI();