/**
 * Test script for the enhanced clock-out functionality with break handling
 * 
 * Usage: node test-clockout-break.js
 */

const axios = require('axios');
require('dotenv').config();

async function testClockOutWithBreak() {
  try {
    const baseUrl = process.env.API_BASE_URL || 'http://localhost:3000';
    const authToken = process.env.TEST_AUTH_TOKEN; // You'll need to provide this
    
    if (!authToken) {
      console.log('Please set TEST_AUTH_TOKEN in your .env file');
      return;
    }

    const headers = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    };

    console.log('Testing enhanced clock-out functionality with break handling...\n');
    
    // Test 1: Get current attendance status
    console.log('1. Getting current attendance status');
    try {
      const statusResponse = await axios.get(`${baseUrl}/api/attendance/today`, { headers });
      console.log('✅ Current Status:', statusResponse.data.data.status);
      console.log('Available Break:', statusResponse.data.data.availableBreak, 'minutes');
      console.log('Consumed Break:', statusResponse.data.data.consumedBreak, 'minutes');
    } catch (error) {
      console.log('❌ Status Error:', error.response?.status, error.response?.data?.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Clock in (if not already clocked in)
    console.log('2. Testing clock-in');
    try {
      const clockInResponse = await axios.post(`${baseUrl}/api/attendance/clock-in`, {}, { headers });
      console.log('✅ Clock-in Success:', clockInResponse.data.message);
    } catch (error) {
      if (error.response?.data?.message?.includes('Already clocked in')) {
        console.log('ℹ️  Already clocked in');
      } else {
        console.log('❌ Clock-in Error:', error.response?.status, error.response?.data?.message);
      }
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 3: Start break
    console.log('3. Testing start break');
    try {
      const startBreakResponse = await axios.post(`${baseUrl}/api/attendance/start-break`, {}, { headers });
      console.log('✅ Break Started:', startBreakResponse.data.message);
      
      // Wait a few seconds to simulate break time
      console.log('⏳ Simulating break time (5 seconds)...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
    } catch (error) {
      if (error.response?.data?.message?.includes('already on a break')) {
        console.log('ℹ️  Already on break');
      } else {
        console.log('❌ Start Break Error:', error.response?.status, error.response?.data?.message);
      }
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 4: Check status while on break
    console.log('4. Checking status while on break');
    try {
      const statusResponse = await axios.get(`${baseUrl}/api/attendance/today`, { headers });
      console.log('✅ Status while on break:', statusResponse.data.data.status);
      console.log('Available Break:', statusResponse.data.data.availableBreak, 'minutes');
      console.log('Consumed Break:', statusResponse.data.data.consumedBreak, 'minutes');
    } catch (error) {
      console.log('❌ Status Error:', error.response?.status, error.response?.data?.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 5: Clock out while on break (this should auto-end the break)
    console.log('5. Testing clock-out while on break (should auto-end break)');
    try {
      const clockOutResponse = await axios.post(`${baseUrl}/api/attendance/clock-out`, {}, { headers });
      console.log('✅ Clock-out Success:', clockOutResponse.data.message);
      console.log('Response Data:', JSON.stringify(clockOutResponse.data.data, null, 2));
      
      if (clockOutResponse.data.data.breakAutoEnded) {
        console.log('🎯 Break was automatically ended!');
        console.log('Break Duration:', clockOutResponse.data.data.breakDuration, 'minutes');
      }
      
    } catch (error) {
      console.log('❌ Clock-out Error:', error.response?.status, error.response?.data?.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 6: Check final status
    console.log('6. Checking final attendance status');
    try {
      const finalStatusResponse = await axios.get(`${baseUrl}/api/attendance/today`, { headers });
      console.log('✅ Final Status:', finalStatusResponse.data.data.status);
      console.log('Final Available Break:', finalStatusResponse.data.data.availableBreak, 'minutes');
      console.log('Final Consumed Break:', finalStatusResponse.data.data.consumedBreak, 'minutes');
      console.log('Working Minutes:', finalStatusResponse.data.data.workingMinutes, 'minutes');
    } catch (error) {
      console.log('❌ Final Status Error:', error.response?.status, error.response?.data?.message);
    }

    console.log('\n✅ All tests completed!');
    console.log('\n📋 Test Summary:');
    console.log('- Clock-in functionality');
    console.log('- Break start functionality');
    console.log('- Auto break-end during clock-out');
    console.log('- Break time calculation and tracking');
    console.log('- Status updates throughout the process');
    
  } catch (error) {
    console.error('❌ Test setup error:', error.message);
  }
}

// Test scenario for break exceeding available time
async function testBreakExceedsAvailableTime() {
  console.log('\n' + '='.repeat(60));
  console.log('TESTING BREAK EXCEEDING AVAILABLE TIME SCENARIO');
  console.log('='.repeat(60) + '\n');
  
  console.log('⚠️  This test would require manually setting a very long break');
  console.log('⚠️  or modifying available break time in the database');
  console.log('⚠️  The system should cap break time to available minutes');
  console.log('⚠️  and log a warning when this occurs');
}

// Run the tests
testClockOutWithBreak().then(() => {
  testBreakExceedsAvailableTime();
});