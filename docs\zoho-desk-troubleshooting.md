# Zoho Desk API Troubleshooting Guide

This guide provides solutions for common issues when working with the Zoho Desk API.

## Authentication Issues

### Invalid Token

**Symptom:** You receive a 401 Unauthorized error.

**Solution:**
1. Ensure your access token is valid and not expired
2. Check that you're using the correct format: `Zoho-oauthtoken YOUR_TOKEN`
3. Verify that your token has the required scopes (e.g., `Desk.tickets.ALL`)

### Missing or Invalid orgId

**Symptom:** You receive a 400 Bad Request error mentioning "orgId".

**Solution:**
1. Ensure you're including the `orgId` header in your request
2. Verify that the orgId value is correct
3. Check that the orgId is associated with your Zoho Desk account

## Validation Errors

### The data is invalid due to validation restrictions

**Symptom:** You receive a 400 Bad Request error with a message about validation restrictions.

**Solution:**

1. **Check Required Fields:**
   - Ensure all required fields are present: `subject`, `description`, `contactId`, etc.
   - Verify that the field values meet Zoho's requirements (e.g., length limits)

2. **Field Format Issues:**
   - Ensure date fields are in ISO format (e.g., `2023-06-21T16:16:16.000Z`)
   - Check that IDs (contactId, departmentId, etc.) are valid and exist in your Zoho Desk account
   - Verify that enum fields (priority, status) have valid values

3. **Field Name Mismatches:**
   - Zoho Desk API is case-sensitive - ensure field names match exactly
   - Use `cf` for custom fields, not `customFields`
   - Use `entitySkills` as an array of strings, not objects

4. **Common Field Issues:**
   - `contactId`: Must be a valid contact ID in your Zoho Desk account
   - `departmentId`: Must be a valid department ID in your Zoho Desk account
   - `assigneeId`: Must be a valid agent ID in your Zoho Desk account
   - `priority`: Must be one of "Low", "Medium", or "High"
   - `status`: Must be one of "Open", "In Progress", "On Hold", "Escalated", or "Closed"

## Debugging Tips

1. **Enable Verbose Logging:**
   - Log the complete request and response for debugging
   - Check for any differences between your request and the expected format

2. **Test with Minimal Data:**
   - Start with only the required fields
   - Add optional fields one by one to identify problematic fields

3. **Use the Zoho Desk API Explorer:**
   - Test your API calls using Zoho's API Explorer
   - Compare your request with the example provided by Zoho

4. **Check API Version:**
   - Ensure you're using the correct API version (v1)
   - Review the Zoho Desk API documentation for any changes

## Example of a Valid Ticket Creation Payload

```json
{
  "subject": "Test Ticket",
  "description": "This is a test ticket",
  "contactId": "****************",
  "departmentId": "****************",
  "priority": "High",
  "status": "Open",
  "category": "general",
  "channel": "Email",
  "email": "<EMAIL>"
}
```

## Common Error Codes

- **400**: Bad Request - Invalid data or missing required fields
- **401**: Unauthorized - Invalid or expired token
- **403**: Forbidden - Insufficient permissions
- **404**: Not Found - Resource not found
- **429**: Too Many Requests - Rate limit exceeded
- **500**: Internal Server Error - Server-side issue

## Contact Support

If you continue to experience issues after trying these solutions, contact Zoho Desk support with:

1. Your request payload (with sensitive data redacted)
2. The complete error response
3. Your Zoho Desk organization ID
4. Steps to reproduce the issue