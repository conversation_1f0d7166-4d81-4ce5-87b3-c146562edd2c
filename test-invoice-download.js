/**
 * Test script for the invoice download API
 * 
 * Usage: node test-invoice-download.js
 */

const axios = require('axios');
require('dotenv').config();

async function testInvoiceDownload() {
  try {
    const baseUrl = process.env.API_BASE_URL || 'http://localhost:3000';
    const authToken = process.env.TEST_AUTH_TOKEN; // You'll need to provide this
    const testPaymentId = process.env.TEST_PAYMENT_ID; // You'll need to provide this
    
    if (!authToken || !testPaymentId) {
      console.log('Please set TEST_AUTH_TOKEN and TEST_PAYMENT_ID in your .env file');
      return;
    }

    console.log('Testing invoice download API...');
    
    // Test client invoice download
    const response = await axios.get(`${baseUrl}/api/billings/invoice/${testPaymentId}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      responseType: 'arraybuffer'
    });

    console.log('Success! Invoice downloaded:');
    console.log('Status:', response.status);
    console.log('Content-Type:', response.headers['content-type']);
    console.log('Content-Disposition:', response.headers['content-disposition']);
    console.log('Data size:', response.data.length, 'bytes');
    
    // Save the file for inspection
    const fs = require('fs');
    const contentType = response.headers['content-type'];
    const extension = contentType.includes('pdf') ? 'pdf' : 'txt';
    const filename = `test-invoice-${testPaymentId}.${extension}`;
    
    fs.writeFileSync(filename, response.data);
    console.log(`Invoice saved as: ${filename}`);
    
  } catch (error) {
    console.error('Error testing invoice download:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Headers:', error.response.headers);
      console.error('Data:', error.response.data.toString());
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Error:', error.message);
    }
  }
}

testInvoiceDownload();