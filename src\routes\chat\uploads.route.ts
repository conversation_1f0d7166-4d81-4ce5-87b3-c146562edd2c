import { Router } from "express";
import UploadController from "../../controllers/uploads/uploads.controller";
import { asyncHandler } from "../../middlewares/asyncHandler";
import authMiddleware from "../../middlewares/checkAuth";
import { upload } from "../../utils/chats.multer";
import { subscriptionMiddleware } from "../../middlewares/subscription.middleware";

const router = Router();

// File upload route for chat messages
router.post(
  "/upload",
  authMiddleware,
  subscriptionMiddleware.requireActiveSubscription,
  upload.single("file"),
  asyncHandler(UploadController.create)
);

export default router;