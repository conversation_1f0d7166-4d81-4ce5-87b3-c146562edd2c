#!/bin/bash

# Example curl command to create a Zoho Desk ticket using your API
# Replace the URL and token with your actual values

curl -X POST http://localhost:3000/api/zoho-desk/tickets \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -d '{
  "entitySkills": ["18921000000379001", "18921000000364001", "18921000000379055", "18921000000379031"],
  "subCategory": "Sub General",
  "cf": {
    "cf_permanentaddress": null,
    "cf_dateofpurchase": null,
    "cf_phone": null,
    "cf_numberofitems": null,
    "cf_url": null,
    "cf_secondaryemail": null,
    "cf_severitypercentage": "0.0",
    "cf_modelname": "F3 2017"
  },
  "productId": "",
  "contactId": "1892000000042032",
  "subject": "Real Time analysis Requirement",
  "dueDate": "2016-06-21T16:16:16.000Z",
  "departmentId": "1892000000006907",
  "channel": "Email",
  "description": "Hai This is Description",
  "language": "English",
  "priority": "High",
  "classification": "",
  "assigneeId": "1892000000056007",
  "phone": "**************",
  "category": "general",
  "email": "<EMAIL>",
  "status": "Open"
}'