import { Request, Response } from "express";
import { MessageService } from "../../services/message/message.service";
import { AuthenticatedRequest } from "../../middlewares/checkAuth";

export class MessageController {
  private messageService = new MessageService();

  getSidebar = async (req: AuthenticatedRequest, res: Response) => {
    const sidebar = await this.messageService.getSidebar(req.user!.userId);
    res.status(200).json({
      data: sidebar,
      message: "Sidebar fetched successfully",
    });
  };

  // sendMessage = async (req: Request, res: Response) => {
  //   try {
  //     const message = await this.messageService.sendMessage(req.body);
  //     req.app.get("io").emit("new_message", message); // broadcast to all
  //     res.status(201).json(message);
  //   } catch (err) {
  //     res.status(500).json({ error: "Failed to send message", details: err });
  //   }
  // };

  editMessage = async (req: Request, res: Response) => {
    const { messageId } = req.params;
    const updatedMessage = await this.messageService.editMessage(
      messageId,
      req.body
    );
    res.json(updatedMessage);
  };

  deleteMessage = async (req: Request, res: Response) => {
    const { messageId } = req.params;
    await this.messageService.deleteMessage(messageId);
    res.status(204).send();
  };

  // replyToMessage = async (req: Request, res: Response) => {
  //   const { messageId } = req.params;
  //   const reply = await this.messageService.replyToMessage(messageId, req.body);
  //   res.json(reply);
  // };

  getConversation = async (req: Request, res: Response) => {
    const { userId } = req.params;
    const messages = await this.messageService.getConversation(userId);
    res.json(messages);
  };


  // MessageController.ts
getMessagesBetweenUsers = async (req: AuthenticatedRequest, res: Response) => {
  const { userId } = req.user!;
  const { otherUserId } = req.params; 

  if (!otherUserId) {
    return res.status(400).json({ message: "otherUserId is required" });
  }

  const messages = await this.messageService.getMessagesBetweenUsers(userId, otherUserId);

  res.status(200).json({
    data: messages,
    length: messages.length,
    message: "Messages fetched successfully",
  });
};

getGroupMessages = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user?.userId;
    const { groupId } = req.params;

    if (!userId) return res.status(401).json({ message: 'Unauthorized' });

    const messages = await this.messageService.getGroupMessages(userId, groupId);

    res.status(200).json({
      message: 'Group messages fetched successfully',
      data: messages,
    });
  } catch (error: any) {
    console.error('Error fetching group messages:', error);
    res.status(403).json({ message: error.message || 'Forbidden' });
  }
};

  getConversationMedia = async (req: AuthenticatedRequest, res: Response) => {
    try {
      const { userId } = req.user!;
      const { conversationId } = req.params;
      const { fileType } = req.query;

      if (!conversationId) {
        return res.status(400).json({ message: "conversationId is required" });
      }

      if (fileType && typeof fileType === "string" && !["PDF", "IMAGE"].includes(fileType.toUpperCase())) {
        return res.status(400).json({ message: "Invalid fileType. Must be 'PDF' or 'IMAGE'" });
      }

      const media = await this.messageService.getConversationMedia(
        userId,
        conversationId,
        typeof fileType === "string" ? fileType : undefined
      );

      res.status(200).json({
        data: media,
        length: media.length,
        message: "Media fetched successfully",
      });
    } catch (error: any) {
      console.error("Error fetching conversation media:", error);
      res.status(403).json({ message: error.message || "Forbidden" });
    }
  };


  getGroupMembers = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { groupId } = req.params;

    if (!groupId) {
      return res.status(400).json({ message: "groupId is required" });
    }

    const members = await this.messageService.getGroupMembers(groupId);

    res.status(200).json({
      message: "Group members fetched successfully",
      data: members,
      count: members.length,
    });
  } catch (error: any) {
    console.error("Error fetching group members:", error);
    res.status(500).json({ message: error.message || "Internal Server Error" });
  }
};




}
