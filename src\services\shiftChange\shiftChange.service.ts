import prisma from "../../prisma";


export class ShiftChangeService {
  static async createShiftChange({
    annotatorId,
    newFrom,
    newTo,
    userId,
    userRole,
    reason,
  }: {
    annotatorId: string;
    newFrom: string;
    newTo: string;
    userId: string;
    userRole: string;
    reason?: string;
  }) {
    if (userRole === "ADMIN" || userRole === "PROJECT_COORDINATOR") {
      await prisma.user.update({
        where: { id: annotatorId },
        data: {
          availableFrom: newFrom,
          availableTo: newTo,
        },
      });

      const shiftRequest = await prisma.shiftChangeRequest.create({
        data: {
          annotatorId,
          newFrom,
          newTo,
          status: "APPROVED",
          requestedById: userId,
          approvedById: userId,
          reason,
        },
      });

      return shiftRequest;
    } else if (userRole === "CLIENT") {
      const shiftRequest = await prisma.shiftChangeRequest.create({
        data: {
          annotatorId,
          newFrom,
          newTo,
          status: "PENDING",
          requestedById: userId,
          reason,
        },
      });

      return shiftRequest;
    } else {
      throw new Error("You are not authorized to request shift changes");
    }
  }

  static async approveShiftChange(requestId: string, approverId: string) {
    const request = await prisma.shiftChangeRequest.findUnique({
      where: { id: requestId },
    });

    if (!request) throw new Error("Shift change request not found");
    if (request.status !== "PENDING") throw new Error("Request already processed");

    await prisma.user.update({
      where: { id: request.annotatorId },
      data: {
        availableFrom: request.newFrom,
        availableTo: request.newTo,
      },
    });

    const updatedRequest = await prisma.shiftChangeRequest.update({
      where: { id: requestId },
      data: {
        status: "APPROVED",
        approvedById: approverId,
      },
    });

    return updatedRequest;
  }

  static async getPendingRequests() {
  return prisma.shiftChangeRequest.findMany({
    where: { status: "PENDING" },
    include: { annotator: true, requestedBy: true },
  });
};

static async getAllShiftChangeRequests() {
  return prisma.shiftChangeRequest.findMany({
    include: { annotator: true, requestedBy: true, approvedBy: true },
    orderBy: { createdAt: "desc" }, 
  });
}

static async getShiftChangeRequestsByRole(userId: string, userRole: string) {
  let actualUserId = userId;
  let actualRole = userRole;

  if (userRole === "COWORKER") {
    const coworker = await prisma.user.findUnique({
      where: { id: userId },
      select: { clientOwnerId: true },
    });

    if (!coworker?.clientOwnerId) {
      throw new Error("Coworker is not linked to any client");
    }

    actualUserId = coworker.clientOwnerId;
    actualRole = "CLIENT";
  }

  if (actualRole === "CLIENT") {
    return prisma.shiftChangeRequest.findMany({
      where: {
        annotator: {
          assignmentsAsDeveloper: {
            some: { clientId: actualUserId },
          },
        },
      },
      include: { annotator: true, requestedBy: true, approvedBy: true },
      orderBy: { createdAt: "desc" },
    });
  }

  if (actualRole === "PROJECT_COORDINATOR") {
    return prisma.shiftChangeRequest.findMany({
      where: {
        annotator: {
          assignmentsAsDeveloper: {
            some: { coordinatorId: actualUserId },
          },
        },
      },
      include: { annotator: true, requestedBy: true, approvedBy: true },
      orderBy: { createdAt: "desc" },
    });
  }

  return [];
}

static async getShiftChangeRequestsByAnnotator(userId: string) {
  return prisma.shiftChangeRequest.findMany({
    where: { annotatorId: userId },
    include: { 
      approvedBy: { select: { id: true, name: true, email: true } },
      requestedBy: { select: { id: true, name: true, email: true } },
    },
    orderBy: { createdAt: "desc" },
  });
}



}
