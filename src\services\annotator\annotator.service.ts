import bcrypt from "bcryptjs";
import prisma from "../../prisma";
import { AnnotatorStatus, Role } from "@prisma/client";

class AnnotatorService {
  async createAnnotator({
    name,
    email,
    password,
  }: {
    name: string;
    email: string;
    password: string;
  }) {
    const hashedPassword = await bcrypt.hash(password, 10);

    const annotator = await prisma.user.create({
      data: {
        name,
        email,
        passwordHash: hashedPassword,
        role: Role.ANNOTATOR,
        annotatorStatus: "ACTIVE",
        emailVerified: new Date(),
      },
    });

    return annotator;
  }

  async getAnnotator(query: any) {
    const { page = 1, limit = 10, includeDeleted = false } = query;

    const whereClause: any = {
      role: Role.ANNOTATOR,
      accountStatus: "ACTIVE",
    };

    // Exclude soft-deleted users by default
    if (!includeDeleted) {
      whereClause.isDeleted = { not: true };
    }

    const annotators = await prisma.user.findMany({
      where: whereClause,
      orderBy: {
        createdAt: "desc",
      },
      select: {
        passwordHash: false,
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
        packageId: true,
        // updatedAt: true,
        assignmentsAsDeveloper: {
        select: {
          clientId: true, // To check if assigned
        },
      },
      },
      skip: (page - 1) * limit,
      take: Number(limit),
    });
    const totalCount = await prisma.user.count({
      where: whereClause,
    });

    // Enhance annotators with assignment status
  const enrichedAnnotators = annotators.map((annotator) => ({
    ...annotator,
    isAssigned: annotator.assignmentsAsDeveloper.length > 0,
  }));

  return {
    data: enrichedAnnotators,
    totalCount,
    totalPages: Math.ceil(totalCount / limit),
    hasNextPage: page < Math.ceil(totalCount / limit),
    hasPreviousPage: page > 1,
    nextPage: page < Math.ceil(totalCount / limit) ? page + 1 : null,
    previousPage: page > 1 ? page - 1 : null,
  };
}


  async getAllCoordinator(query: any) {
    const { page = 1, limit = 10, includeDeleted = false } = query;
    
    const whereClause: any = {
      role: Role.PROJECT_COORDINATOR,
      accountStatus: "ACTIVE",
    };

    // Exclude soft-deleted users by default
    if (!includeDeleted) {
      whereClause.isDeleted = { not: true };
    }

    const annotators = await prisma.user.findMany({
      where: whereClause,
      orderBy: {
        createdAt: "desc",
      },
      select: {
        passwordHash: false,
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
        packageId: true,
        // updatedAt: true,
      },
      skip: (page - 1) * limit,
      take: Number(limit),
    });
    const totalCount = await prisma.user.count({
      where: whereClause,
    });
    return {
      data: annotators,
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      hasNextPage: page < Math.ceil(totalCount / limit),
      hasPreviousPage: page > 1,
      nextPage: page < Math.ceil(totalCount / limit) ? page + 1 : null,
      previousPage: page > 1 ? page - 1 : null,
    };
  }
  async getAnnotatorById(id: string) {
    const annotator = await prisma.user.findUnique({
      where: {
        id,
      },
    });

    if (!annotator) {
      throw new Error("Annotator not found");
    }

    return annotator;
  }
  async updateAnnotatorStatus(id: string, status: string) {
    const updatedAnnotator = await prisma.user.update({
      where: { id },
      data: { annotatorStatus: status as AnnotatorStatus },
    });

    return updatedAnnotator;
  }
  async deleteAnnotator(id: string) {
    // First, check if the user exists and get their role
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isDeleted: true,
        accountStatus: true,
        createdAt: true,
      },
    });

    if (!user) {
      throw new Error("User not found");
    }

    // Check if user is already soft deleted
    if (user.isDeleted || user.accountStatus === "DELETED") {
      throw new Error("User is already deleted");
    }

    // Only allow deletion of ANNOTATOR and PROJECT_COORDINATOR roles
    if (user.role !== "ANNOTATOR" && user.role !== "PROJECT_COORDINATOR") {
      throw new Error("Only annotators and coordinators can be deleted");
    }

    // First, check if user is assigned to any clients - if so, prevent deletion entirely
    let clientAssignmentsCount = 0;
    if (user.role === "PROJECT_COORDINATOR") {
      clientAssignmentsCount = await prisma.assignment.count({
        where: { coordinatorId: id },
      });
    } else if (user.role === "ANNOTATOR") {
      clientAssignmentsCount = await prisma.assignment.count({
        where: { developerId: id },
      });
    }

    // If user is assigned to any clients, throw an error and don't allow deletion
    if (clientAssignmentsCount > 0) {
      const clientAssignments = await prisma.assignment.findMany({
        where: user.role === "PROJECT_COORDINATOR" 
          ? { coordinatorId: id }
          : { developerId: id },
        include: {
          client: {
            select: {
              name: true,
              email: true,
            },
          },
        },
      });

      const clientNames = clientAssignments.map(assignment => assignment.client.name).join(", ");
      throw new Error(
        `Cannot delete ${user.role.toLowerCase()} as they are currently assigned to ${clientAssignmentsCount} client(s): ${clientNames}. Please unassign them from all clients before deletion.`
      );
    }

    // Check for other relationships based on user role
    let hasRelationships = false;
    const relationshipDetails: string[] = [];

    if (user.role === "PROJECT_COORDINATOR") {
      // Check coordinator relationships (excluding assignments which we already checked above)
      const [projectsCount, projectAssignmentsCount] = await Promise.all([
        prisma.project.count({
          where: { coordinatorId: id },
        }),
        prisma.projectCoordinatorAssignment.count({
          where: { projectCoordinatorId: id },
        }),
      ]);

      if (projectsCount > 0) {
        hasRelationships = true;
        relationshipDetails.push(`${projectsCount} project(s) as coordinator`);
      }
      if (projectAssignmentsCount > 0) {
        hasRelationships = true;
        relationshipDetails.push(`${projectAssignmentsCount} project assignment(s)`);
      }
    } else if (user.role === "ANNOTATOR") {
      // Check annotator relationships (excluding assignments which we already checked above)
      const [annotatorProjectsCount, annotatedTasksCount, createdTasksCount, timeLogsCount] = await Promise.all([
        prisma.project.count({
          where: {
            annotators: {
              some: { id },
            },
          },
        }),
        prisma.task.count({
          where: {
            annotators: {
              some: { id },
            },
          },
        }),
        prisma.task.count({
          where: { createdById: id },
        }),
        prisma.timeLog.count({
          where: { userId: id },
        }),
      ]);

      if (annotatorProjectsCount > 0) {
        hasRelationships = true;
        relationshipDetails.push(`${annotatorProjectsCount} project(s) as annotator`);
      }
      if (annotatedTasksCount > 0) {
        hasRelationships = true;
        relationshipDetails.push(`${annotatedTasksCount} annotated task(s)`);
      }
      if (createdTasksCount > 0) {
        hasRelationships = true;
        relationshipDetails.push(`${createdTasksCount} created task(s)`);
      }
      if (timeLogsCount > 0) {
        hasRelationships = true;
        relationshipDetails.push(`${timeLogsCount} time log(s)`);
      }
    }

    if (hasRelationships) {
      // Perform soft delete if user has relationships
      const softDeletedUser = await prisma.user.update({
        where: { id },
        data: {
          isDeleted: true,
          accountStatus: "DELETED",
          // Optionally, you can also anonymize the email to prevent conflicts
          email: `deleted_${Date.now()}_${user.email}`,
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isDeleted: true,
          accountStatus: true,
          createdAt: true,
        },
      });

      return {
        ...softDeletedUser,
        deletionType: "soft",
        message: `User is linked with other entities (${relationshipDetails.join(", ")}) and has been soft deleted`,
        relationshipDetails,
      };
    } else {
      // Perform hard delete if no relationships exist
      const deletedUser = await prisma.user.delete({
        where: { id },
      });

      return {
        ...deletedUser,
        deletionType: "hard",
        message: "User has been permanently deleted as no relationships were found",
        relationshipDetails: [],
      };
    }
  }
  async updateAnnotator(id: string, data: any) {
    const updatedAnnotator = await prisma.user.update({
      where: { id },
      data,
    });

    return updatedAnnotator;
  }
  async getAnnotatorByEmail(email: string) {
    const annotator = await prisma.user.findUnique({
      where: {
        email,
      },
    });

    if (!annotator) {
      throw new Error("Annotator not found");
    }

    return annotator;
  }

  // async getCoordinatorClients(query: any, loggedInUser: string) {
  //   const { page = 1, limit = 10, filter = "both" } = query;

  //   const include: any = {};
  //   if (filter === "client" || filter === "both")
  //     include.client = {
  //       _count: {
  //         select: {
  //           assignmentsAsClient: true, // Developers assigned to this client
  //           projectsOwned: true, // Projects owned by this client
  //         },
  //       },
  //     };
  //   if (filter === "developer" || filter === "both") include.developer = true;

  //   const whereClause: any = {
  //     coordinatorId: loggedInUser,
  //   };

  //   // Apply additional filter only when filtering specifically for clients
  //   if (filter === "client") {
  //     whereClause.client = {
  //       accountStatus: "ACTIVE",
  //       role: "CLIENT",
  //       isDeleted: false,
  //     };
  //   }

  //   const assignments = await prisma.assignment.findMany({
  //     where: whereClause,
  //     include,
  //     orderBy: {
  //       createdAt: "desc",
  //     },
  //     skip: (page - 1) * limit,
  //     take: Number(limit),
  //   });

  //   const totalCount = await prisma.assignment.count({
  //     where: whereClause,
  //   });

  //   return {
  //     data: assignments,
  //     totalCount,
  //     totalPages: Math.ceil(totalCount / limit),
  //     hasNextPage: page < Math.ceil(totalCount / limit),
  //     hasPreviousPage: page > 1,
  //     nextPage: page < Math.ceil(totalCount / limit) ? page + 1 : null,
  //     previousPage: page > 1 ? page - 1 : null,
  //   };
  // }
  async getCoordinatorClients(query: any, loggedInUser: string) {
    const { page = 1, limit = 10, filter = "both" } = query;

    const include: any = {};
    if (filter === "client" || filter === "both") {

      include.client = {
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true,
          updatedAt: true,
          Subscription: {
            where: { status: "ACTIVE" },
            select: {
              id: true,
              startDate: true,
              endDate: true,
              package: {
                select: { name: true, id: true },
              },
            },
          },
          _count: {
            select: {
              assignmentsAsClient: true,
              projectsOwned: true,
              coWorkers: true,
            },
          },
        },
      };
    }
    if (filter === "developer" || filter === "both") {
      include.developer = {
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true,
          updatedAt: true,
          availableFrom: true,
          availableTo: true,
          Package: {
            select: {
              name: true,
              id: true,
            },
          },
          _count: {
            select: {
              annotatorProjects: true, // ✅ This will count projects assigned to this user as annotator
            },
          },
        },
      };
    }

    const whereClause: any = {
      coordinatorId: loggedInUser,
    };

    if (filter === "client") {
      whereClause.client = {
        accountStatus: "ACTIVE",
        role: "CLIENT",
        isDeleted: false,
      };
    }

    const assignments = await prisma.assignment.findMany({
      where: whereClause,
      include,
      orderBy: {
        createdAt: "desc",
      },
      skip: (page - 1) * limit,
      take: Number(limit),
    });

    const totalCount = await prisma.assignment.count({
      where: whereClause,
    });

    return {
      data: assignments,
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      hasNextPage: page < Math.ceil(totalCount / limit),
      hasPreviousPage: page > 1,
      nextPage: page < Math.ceil(totalCount / limit) ? page + 1 : null,
      previousPage: page > 1 ? page - 1 : null,
    };
  }

  async getClientsAnnotators(query: any, loggedInUser: string) {
    const { page = 1, limit = 10, filter = "both" } = query;

    const include: any = {};
    if (filter === "developer" || filter === "both") {
      include.developer = {
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true,
          updatedAt: true,
          availableFrom: true,
          availableTo: true,
          Package: {
            select: {
              name: true,
              id: true,
            },
          },
          _count: {
            select: {
              annotatorProjects: true, // ✅ This will count projects assigned to this user as annotator
            },
          },
        },
      };
    }

    const whereClause: any = {
      clientId: loggedInUser,
    };

    if (filter === "client") {
      whereClause.client = {
        accountStatus: "ACTIVE",
        role: "CLIENT",
        isDeleted: false,
      };
    }

    const assignments = await prisma.assignment.findMany({
      where: whereClause,
      include,
      orderBy: {
        createdAt: "desc",
      },
      skip: (page - 1) * limit,
      take: Number(limit),
    });

    const totalCount = await prisma.assignment.count({
      where: whereClause,
    });

    return {
      data: assignments,
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      hasNextPage: page < Math.ceil(totalCount / limit),
      hasPreviousPage: page > 1,
      nextPage: page < Math.ceil(totalCount / limit) ? page + 1 : null,
      previousPage: page > 1 ? page - 1 : null,
    };
  }

  async getAllAssignedAnnotators(query: any) {
    const { page = 1, limit = 10 } = query;

    const skip = (page - 1) * limit;
    const take = Number(limit);

    const [annotators, totalCount] = await Promise.all([
      prisma.user.findMany({
        where: {
          role: "ANNOTATOR",
          accountStatus: "ACTIVE",
          assignmentsAsDeveloper: {
            some: {}, // ensures the user has at least one assignment as a developer
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true,
          packageId: true,
          availableFrom: true,
          availableTo: true,
          _count: {
            select: {
              annotatorProjects: true, // ✅ This will count projects assigned to this user as annotator
            },
          },
          // You can omit sensitive fields like passwordHash
        },
      }),
      prisma.user.count({
        where: {
          role: "ANNOTATOR",
          accountStatus: "ACTIVE",
          assignmentsAsDeveloper: {
            some: {}, // same filtering as above
          },
        },
      }),
    ]);

    const totalPages = Math.ceil(totalCount / take);

    return {
      data: annotators,
      totalCount,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
      nextPage: page < totalPages ? page + 1 : null,
      previousPage: page > 1 ? page - 1 : null,
    };
  }

  async getAllAssignedCoordinator(query: any) {
    const page = Number(query.page) || 1;
    const limit = Number(query.limit) || 10;
    const skip = (page - 1) * limit;

    const [coordinators, totalCount] = await Promise.all([
      prisma.user.findMany({
        where: {
          role: "PROJECT_COORDINATOR",
          accountStatus: "ACTIVE",
          assignmentsAsCoordinator: {
            some: {}, // at least one assignment
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true,
          packageId: true,
          assignmentsAsCoordinator: {
            include: {
              client: {
                select: {
                  _count: {
                    select: {
                      // assignmentsAsClient:true,
                      projectsOwned: true,
                      coWorkers: true,
                    },
                  },
                },
              },
            },
          },
        },
      }),

      prisma.user.count({
        where: {
          role: "PROJECT_COORDINATOR",
          accountStatus: "ACTIVE",
          assignmentsAsCoordinator: {
            some: {},
          },
        },
      }),
    ]);

    // Count unique clients and annotators for each coordinator
    const enrichedCoordinators = coordinators.map((coord) => {
      const uniqueClients = new Set(
        coord.assignmentsAsCoordinator.map((a) => a.clientId)
      );
      const uniqueAnnotators = new Set(
        coord.assignmentsAsCoordinator.map((a) => a.developerId)
      );
      const totalCoWorkers = coord.assignmentsAsCoordinator.reduce(
        (sum, assignment) => {
          return sum + (assignment.client?._count?.coWorkers || 0);
        },
        0
      );
      const totalProjects = coord.assignmentsAsCoordinator.reduce(
        (sum, assignment) => {
          return sum + (assignment.client?._count?.projectsOwned || 0);
        },
        0
      );

      return {
        ...coord,
        totalClients: uniqueClients.size,
        totalAnnotators: uniqueAnnotators.size,
        totalCoWorkers,
        totalProjects,
      };
    });

    const totalPages = Math.ceil(totalCount / limit);

    return {
      data: enrichedCoordinators,
      totalCount,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
      nextPage: page < totalPages ? page + 1 : null,
      previousPage: page > 1 ? page - 1 : null,
    };
  }

  async getAllCoordinatorClients(query: any) {
    const page = Number(query.page) || 1;
    const limit = Number(query.limit) || 10;
    const skip = (page - 1) * limit;
    const coordinatorId = query.coordinatorId;

    // Step 1: Get all distinct clientIds assigned to this coordinator
    const distinctClientAssignments = await prisma.assignment.findMany({
      where: { coordinatorId },
      distinct: ["clientId"],
      select: {
        clientId: true,
      },
    });

    const totalCount = distinctClientAssignments.length;
    const totalPages = Math.ceil(totalCount / limit);

    // Step 2: Paginate the clientIds
    const paginatedClientIds = distinctClientAssignments
      .slice(skip, skip + limit)
      .map((entry) => entry.clientId);

    // Step 3: Fetch client details
    const clients = await prisma.user.findMany({
      where: {
        id: {
          in: paginatedClientIds,
        },
      },
      include: {
        Subscription: {
          where: {
            status: "ACTIVE",
          },
          select: {
            id: true,
            startDate: true,
            endDate: true,
            package: {
              select: {
                name: true,
                id: true,
              },
            },
          },
        },
        _count: {
          select: {
            assignmentsAsClient: true,
            projectsOwned: true,
            coWorkers: true,
          },
        },
      },
    });

    return {
      data: clients,
      totalCount,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
      nextPage: page < totalPages ? page + 1 : null,
      previousPage: page > 1 ? page - 1 : null,
    };
  }

  async getProjectsForCoordinator(coordinatorId: string, page: number = 1, limit: number = 10) {
    const skip = (page - 1) * limit;

    const [totalCount, projects] = await Promise.all([
      prisma.project.count({
        where: {
          coordinatorId,
        },
      }),
      prisma.project.findMany({
        where: {
          coordinatorId,
        },
        select: {
          id: true,
          name: true,
          priority: true,
          description: true,
          startDate: true,
          dueDate: true,
          status: true,
          createdById: true,
          coordinatorId: true,
          createdBy: {
            select: {
              name: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    const formattedProjects = projects.map((project) => ({
      id: project.id,
      name: project.name,
      priority: project.priority,
      description: project.description,
      startDate: project.startDate,
      duration: Math.ceil(
        (new Date(project.dueDate).getTime() - new Date(project.startDate).getTime()) / (1000 * 60 * 60 * 24)
      ) + ' days',
      postedBy: project.createdBy.name,
      status: project.status,
      clientId: project.createdById,
      coordinatorId: project.coordinatorId,
    }));

    return {
      data: formattedProjects,
      totalCount,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
      nextPage: page < totalPages ? page + 1 : null,
      previousPage: page > 1 ? page - 1 : null,
    };
  }


  async getClientsWithSubscriptions(page: number = 1, limit: number = 10) {
    const skip = (page - 1) * limit;

    const [totalCount, clients] = await Promise.all([
      prisma.user.count({
        where: {
          role: 'CLIENT',
          Subscription: {
            some: {},
          },
        },
      }),
      prisma.user.findMany({
        where: {
          role: 'CLIENT',
          Subscription: {
            some: {},
          },
        },
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        select: {
          id: true,
          name: true,
          lastname: true,
          email: true,
          createdAt: true,
          Subscription: {
            select: {
              id: true,
              packageId: true,
              startDate: true,
              endDate: true,
              status: true,
            },
          },
          _count: {
            select: {
              projectsOwned: true,        // client's own projects
              coWorkers: true,            // coworkers under this client
              annotatorProjects: true,    // annotators assigned to projects
            },
          },
        },
      }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    const assignments = await prisma.assignment.findMany({
      where: {
        clientId: {
          in: clients.map(client => client.id),
        },
      },
      select: {
        clientId: true,
        developerId: true,
      },
    });

    // Group assignments by clientId
    const annotatorCountMap: Record<string, Set<string>> = {};
    for (const assignment of assignments) {
      if (!annotatorCountMap[assignment.clientId]) {
        annotatorCountMap[assignment.clientId] = new Set();
      }
      annotatorCountMap[assignment.clientId].add(assignment.developerId);
    }

    const formattedClients = clients.map((client) => {
      const annotatorCount = annotatorCountMap[client.id]?.size || 0;
      return {
        ...client,
        projectCount: client._count.projectsOwned,
        coworkerCount: client._count.coWorkers,
        annotatorCount
      };
    })



    return {
      data: formattedClients,
      totalCount,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
      nextPage: page < totalPages ? page + 1 : null,
      previousPage: page > 1 ? page - 1 : null,
    };
  }


  async getAnnotatorProjects(annotatorId: string, page: number = 1, limit: number = 10) {
    const skip = (page - 1) * limit;

    const [totalCount, projects] = await Promise.all([
      prisma.project.count({
        where: {
          annotators: {
            some: { id: annotatorId },
          },
        },
      }),
      prisma.project.findMany({
        where: {
          annotators: {
            some: { id: annotatorId },
          },
        },
        skip,
        take: limit,
        orderBy: {
          createdAt: "desc",
        },
      }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return {
      data: projects,
      totalCount,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
      nextPage: page < totalPages ? page + 1 : null,
      previousPage: page > 1 ? page - 1 : null,
    };
  }

  async getCoordinatorClientProjects(clientId: string, loggedInUser: string) {
    // Step 1: Check if client is assigned to the logged-in coordinator
    const assignments = await prisma.assignment.findMany({
      where: {
        coordinatorId: loggedInUser,
        clientId: clientId,
      },
    });

    if (assignments.length === 0) {
      throw new Error("Client is not assigned to this coordinator.");
    }

    const projects = await prisma.project.findMany({
      where: {
        createdById: clientId,
      },
      include: {
        annotators: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return projects;
  }

  async updateDescription(userId: string, description: string) {
  const updatedUser = await prisma.user.update({
    where: { id: userId },
    data: { description, updatedAt: new Date() },
    select: {
      id: true,
      name: true,
      email: true,
      description: true,
      updatedAt: true,
    },
  });

  return updatedUser;
}



}

export const annotatorService = new AnnotatorService();
