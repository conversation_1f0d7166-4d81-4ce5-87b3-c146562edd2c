import { Request, Response } from "express";
import * as projectService from "../../services/projects/project.service";
import * as ApiResponse from "../../helper/apiResponse";
import { JwtPayload } from "jsonwebtoken";
import * as S3Service from "../../services/s3/s3.service";
import * as fs from "fs";

interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}

export class ProjectController {
async create(req: AuthenticatedRequest, res: Response) {
  try {
    const { name, description, priority, annotatorIds, startDate, dueDate } = req.body;
    const createdById = req.user?.userId;

    if (!startDate || !dueDate) {
      return ApiResponse.errorResponse(
        res,
        "Start date and Due date are required"
      );
    }

    const validPriorities: string[] = ["LOW", "MEDIUM", "HIGH"];
    if (!validPriorities.includes(priority)) {
      return ApiResponse.errorResponse(
        res,
        "Priority must be one of 'LOW', 'MEDIUM', or 'HIGH'"
      );
    }

    // Parse annotatorIds if it's a string
    let parsedAnnotatorIds: string[];
    if (typeof annotatorIds === "string") {
      try {
        parsedAnnotatorIds = JSON.parse(annotatorIds);
      } catch (error) {
        return ApiResponse.errorResponse(
          res,
          "Invalid annotatorIds format: must be a valid JSON array of strings"
        );
      }
    } else {
      parsedAnnotatorIds = annotatorIds;
    }

    // Validate that parsedAnnotatorIds is a non-empty array of strings
    if (!Array.isArray(parsedAnnotatorIds) || parsedAnnotatorIds.length === 0) {
      return ApiResponse.errorResponse(
        res,
        "At least one annotator ID is required"
      );
    }
    if (!parsedAnnotatorIds.every(id => typeof id === "string")) {
      return ApiResponse.errorResponse(
        res,
        "All annotator IDs must be strings"
      );
    }

    const start = new Date(startDate);
    const due = new Date(dueDate);

    // Upload files to S3 and get URLs
    const attachments: string[] = [];
    if (req.files && Array.isArray(req.files)) {
      for (const file of req.files as Express.Multer.File[]) {
        try {
          const s3FileUrl = await S3Service.default.uploadFile(
            file.path,
            "project-attachments"
          );
          attachments.push(s3FileUrl);
        } catch (uploadError) {
          console.error("Error uploading file to S3:", uploadError);
        }
      }
    }

    const project = await projectService.createProject({
      name,
      description,
      priority,
      createdById,
      annotatorIds: parsedAnnotatorIds, 
      startDate: start,
      dueDate: due,
      attachment: attachments,
    });

    return ApiResponse.successResponseWithData(
      res,
      "Project created successfully",
      project
    );
  } catch (error) {
    console.error(error);
    return ApiResponse.errorResponse(res, "Failed to create project");
  }
}

  // For COOWerker if they have edit access
  async createdByCoowrker(req: AuthenticatedRequest, res: Response) {
    try {
      const { name, description, priority, annotatorIds, startDate, dueDate } =
        req.body;
      const createdById = req.user?.userId;

      if (!startDate || !dueDate) {
        return ApiResponse.errorResponse(
          res,
          "Start date and Due date are required"
        );
      }

      const validPriorities: string[] = ["LOW", "MEDIUM", "HIGH"];
      if (!validPriorities.includes(priority)) {
        return ApiResponse.errorResponse(
          res,
          "Priority must be one of 'LOW', 'MEDIUM', or 'HIGH'"
        );
      }

      const start = new Date(startDate);
      const due = new Date(dueDate);

      // Upload files to S3 and get URLs
      const attachments: string[] = [];
      if (req.files && Array.isArray(req.files)) {
        for (const file of req.files as Express.Multer.File[]) {
          try {
            const s3FileUrl = await S3Service.default.uploadFile(
              file.path,
              "project-attachments"
            );
            attachments.push(s3FileUrl);
            // Optionally clean up the temp file
            // fs.unlinkSync(file.path);
          } catch (uploadError) {
            console.error("Error uploading file to S3:", uploadError);
          }
        }
      }

      const project = await projectService.createCoowerkerProject({
        name,
        description,
        priority,
        createdById,
        annotatorIds,
        startDate: start,
        dueDate: due,
        attachment: attachments,
      });

      return ApiResponse.successResponseWithData(
        res,
        "Project created successfully",
        project
      );
    } catch (error) {
      console.error(error);
      return ApiResponse.errorResponse(res, "Failed to create project");
    }
  }

  async getAll(req: Request, res: Response) {
    try {
      const projects = await projectService.getAllProjects();
      return ApiResponse.successResponseWithData(
        res,
        "Projects fetched successfully",
        projects,
        projects.length
      );
    } catch (error) {
      console.error(error);
      return ApiResponse.errorResponse(res, "Failed to fetch projects");
    }
  }

  async getById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const project = await projectService.getProjectById(id);

      if (!project) {
        return ApiResponse.errorResponse(res, "Project not found");
      }

      return ApiResponse.successResponseWithData(
        res,
        "Project fetched successfully",
        project
      );
    } catch (error) {
      console.error(error);
      return ApiResponse.errorResponse(res, "Failed to fetch project");
    }
  }

async update(req: AuthenticatedRequest, res: Response) {
  try {
    const { id } = req.params;
    const data = req.body;

    // Convert date strings to Date objects
    if (data.startDate) data.startDate = new Date(data.startDate);
    if (data.dueDate) data.dueDate = new Date(data.dueDate);

    // Parse annotatorIds if it's a JSON string (common from multipart/form-data)
    if (data.annotatorIds && typeof data.annotatorIds === "string") {
      data.annotatorIds = JSON.parse(data.annotatorIds);
    }

    // Validate allowed status
    const allowedStatuses = ["PENDING", "IN_PROGRESS", "COMPLETED"];
    if (data.status && !allowedStatuses.includes(data.status)) {
      return ApiResponse.errorResponse(res, "Invalid status value");
    }

    // Handle file uploads
    const attachments: string[] = [];

    if (req.files && Array.isArray(req.files)) {
      for (const file of req.files as Express.Multer.File[]) {
        try {
          const s3FileUrl = await S3Service.default.uploadFile(
            file.path,
            "project-attachments"
          );
          attachments.push(s3FileUrl);
          // fs.unlinkSync(file.path); // Uncomment if cleanup needed
        } catch (uploadError) {
          console.error("Error uploading file to S3:", uploadError);
        }
      }
    }

    if (attachments.length > 0) {
      data.attachment = attachments;
    }

    const updatedProject = await projectService.updateProject(id, data);

    return ApiResponse.successResponseWithData(
      res,
      "Project updated successfully",
      updatedProject
    );
  } catch (error) {
    console.error(error);
    return ApiResponse.errorResponse(res, "Failed to update project");
  }
}

  async delete(req: Request, res: Response) {
    try {
      const { id } = req.params;
      await projectService.deleteProject(id);

      return ApiResponse.successResponseWithData(
        res,
        "Project deleted successfully",
        {}
      );
    } catch (error) {
      console.error(error);
      return ApiResponse.errorResponse(res, "Failed to delete project");
    }
  }

  async getCreatedProjectsByUser(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        return ApiResponse.errorResponse(res, "Unauthorized");
      }

      const projects = await projectService.getProjectsCreatedByUser(userId);

      return ApiResponse.successResponseWithData(
        res,
        "Projects created by user fetched successfully",
        projects,
        projects.length
      );
    } catch (error) {
      console.error(error);
      return ApiResponse.errorResponse(res, "Failed to fetch user's projects");
    }
  }

    async getCreatedProjectsByUserCooWorker(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        return ApiResponse.errorResponse(res, "Unauthorized");
      }

      const projects = await projectService.getProjectsCreatedByUserCooWorker(userId);

      return ApiResponse.successResponseWithData(
        res,
        "Projects created by user fetched successfully",
        projects,
        projects.length
      );
    } catch (error) {
      console.error(error);
      return ApiResponse.errorResponse(res, "Failed to fetch user's projects");
    }
  }

  async getAnnotatorProjects(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        return ApiResponse.errorResponse(res, "Unauthorized");
      }

      const projects = await projectService.getProjectsForAnnotator(userId);

      return ApiResponse.successResponseWithData(
        res,
        "Annotator's projects fetched successfully",
        projects,
        projects.length
      );
    } catch (error) {
      console.error(error);
      return ApiResponse.errorResponse(
        res,
        "Failed to fetch annotator's projects"
      );
    }
  }

  async getProjectsByClientId(req: Request, res: Response) {
    try {
      const { clientId } = req.params;

      if (!clientId) {
        return ApiResponse.errorResponse(res, "Client ID is required");
      }

      const projects = await projectService.getProjectsByClientId(clientId);

      return ApiResponse.successResponseWithData(
        res,
        "Projects for specific client fetched successfully",
        projects,
        projects.length
      );
    } catch (error) {
      console.error(error);
      return ApiResponse.errorResponse(
        res,
        "Failed to fetch client's projects"
      );
    }
  }
}
