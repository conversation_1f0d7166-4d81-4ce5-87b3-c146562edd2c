import { Request, Response } from "express";
import { UserService } from "../services/user.serive";
import {
  successResponseWithData,
  errorResponse,
  notFoundResponse,
} from "../helper/apiResponse";
import { AuthenticatedRequest } from "../middlewares/checkAuth";

export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  async getAllUsers(req: Request, res: Response) {
    try {
      const users = await this.userService.getAllUsers();
      return successResponseWithData(res, "Users fetched successfully", users, users.length);
    } catch (error) {
      return errorResponse(res, "Failed to fetch users");
    }
  }


  async createUser(req: Request, res: Response) {
    try {
      const { username, email, password, role } = req.body;
      const newUser = await this.userService.createUser({
        username,
        email,
        password,
        role,
      });
      return successResponseWithData(res, "User created successfully", newUser);
    } catch (error) {
      return errorResponse(res, "Failed to create user");
    }
  }

  async getUserById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const user = await this.userService.getUserById(id);
      if (!user) {
        return notFoundResponse(res, "User not found");
      }
      return successResponseWithData(res, "User fetched successfully", user);
    } catch (error) {
      return errorResponse(res, "Failed to fetch user");
    }
  }

  async updateUser(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const updatedUser = await this.userService.updateUser(id, req.body);
      return successResponseWithData(res, "User updated successfully", updatedUser);
    } catch (error) {
      return errorResponse(res, "Failed to update user");
    }
  }


  async deleteUser(req: Request, res: Response) {
    try {
      const { id } = req.params;
      await this.userService.deleteUser(id);
      return successResponseWithData(res, "User deleted successfully", {});
    } catch (error) {
      return errorResponse(res, "Failed to delete user");
    }
  }

  async getUserProfile(req: AuthenticatedRequest, res: Response) {
    try {
      const userId = req.user?.userId;
      
      if (!userId) {
        return errorResponse(res, "Unauthorized");
      }
      
      const userProfile = await this.userService.getUserCompleteProfile(userId);
      
      if (!userProfile) {
        return notFoundResponse(res, "User profile not found");
      }
      
      return successResponseWithData(res, "User profile fetched successfully", userProfile);
    } catch (error) {
      console.error("Error fetching user profile:", error);
      return errorResponse(res, "Failed to fetch user profile");
    }
  }
}
