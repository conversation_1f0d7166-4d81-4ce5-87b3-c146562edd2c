# Zoho Desk API Integration

This document explains how to use the Zoho Desk API integration in your application.

## Authentication

Before using the Zoho Desk API, you need to authenticate with Zoh<PERSON>. The application uses OAuth 2.0 for authentication.

1. Configure your environment variables:
   ```
   ZOHO_DESK_CLIENT_ID=your_client_id
   ZOHO_DESK_CLIENT_SECRET=your_client_secret
   ZOHO_DESK_REDIRECT_URI=your_redirect_uri
   ZOHO_DESK_API_DOMAIN=https://desk.zoho.com
   ZOHO_DESK_AUTH_URL=https://accounts.zoho.com/oauth/v2
   ZOHO_DESK_ORG_ID=your_org_id
   ```

2. Initiate the OAuth flow by visiting:
   ```
   GET /api/zoho-desk/auth/initiate
   ```

3. After authorization, <PERSON><PERSON><PERSON> will redirect to your callback URL with a code parameter.

4. The code is exchanged for access and refresh tokens.

## Creating Tickets

To create a ticket in Zoho Desk, make a POST request to the tickets endpoint:

```
POST /api/zoho-desk/tickets
```

### Request Body

```json
{
  "subject": "Ticket Subject",
  "description": "Ticket Description",
  "priority": "High",
  "status": "Open",
  "departmentId": "department_id",
  "contactId": "contact_id",
  "assigneeId": "assignee_id",
  "category": "general",
  "subCategory": "Sub General",
  "entitySkills": ["skill_id1", "skill_id2"],
  "productId": "product_id",
  "dueDate": "2023-06-21T16:16:16.000Z",
  "channel": "Email",
  "language": "English",
  "classification": "",
  "phone": "**************",
  "email": "<EMAIL>",
  "cf": {
    "cf_customfield1": "value1",
    "cf_customfield2": "value2"
  }
}
```

### Required Fields

- `subject`: The subject of the ticket
- `description`: The description of the ticket
- `priority`: The priority of the ticket (Low, Medium, High)
- `status`: The status of the ticket (Open, In Progress, On Hold, Escalated, Closed)

### Response

```json
{
  "status": "success",
  "message": "Ticket created successfully",
  "data": {
    "id": "ticket_id",
    "subject": "Ticket Subject",
    "description": "Ticket Description",
    "priority": "High",
    "status": "Open",
    ...
  }
}
```

## Other Ticket Operations

### Get a Ticket

```
GET /api/zoho-desk/tickets/:ticketId
```

### Update a Ticket

```
PATCH /api/zoho-desk/tickets/:ticketId
```

### Delete a Ticket

```
DELETE /api/zoho-desk/tickets/:ticketId
```

### List Tickets

```
GET /api/zoho-desk/tickets
```

Query parameters:
- `limit`: Maximum number of tickets to return
- `from`: Starting index for pagination
- `departmentId`: Filter by department ID
- `status`: Filter by status
- `priority`: Filter by priority
- `assigneeId`: Filter by assignee ID
- `contactId`: Filter by contact ID
- `sortBy`: Sort field

## Contact Operations

### Create a Contact

```
POST /api/zoho-desk/contacts
```

### Get a Contact

```
GET /api/zoho-desk/contacts/:contactId
```

### Search Contacts

```
GET /api/zoho-desk/contacts?email=<EMAIL>
```

## Utility Operations

### Get Departments

```
GET /api/zoho-desk/departments
```

### Get Agents

```
GET /api/zoho-desk/agents
```

## Ticket Comments

### Add a Comment to a Ticket

```
POST /api/zoho-desk/tickets/:ticketId/comments
```

### Get Comments for a Ticket

```
GET /api/zoho-desk/tickets/:ticketId/comments
```

## Attachments

### Upload an Attachment to a Ticket

```
POST /api/zoho-desk/tickets/:ticketId/attachments
```

Use `multipart/form-data` with a file field named 'file'.