module.exports = {
  apps: [
    {
      name: "annotator-backend",  // Change this to your app name
      script: "dist/server.js",
      instances: "1", // Uses all available CPU cores
      exec_mode: "cluster",
      watch: true, // Set to true if you want auto-restart on file changes
      ignore_watch: ["node_modules", "uploads", "logs", "uploads"],
      autorestart: true,
      max_memory_restart: "1G", // Restart if memory exceeds 1GB
      // log_date_format: "YYYY-MM-DD HH:mm Z",
      // error_file: "logs/my-app-error.log", // Error logs
      // out_file: "logs/my-app-out.log", // Output logs
      merge_logs: true,
      pid_file: "/var/run/my-app.pid", // Track process ID
      env: {
        NODE_ENV: "development",
        PORT: 4000,
      },
      env_staging: {
        NODE_ENV: "staging",
        PORT: 6000,
      },
      env_production: {
        NODE_ENV: "production",
        PORT: 5000,
      },
    },
  ],
};
