/**
 * Test script for the comprehensive subscription system
 * This script tests various subscription scenarios to ensure the system works correctly
 */

const axios = require('axios');

// Configuration
const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000/api';
const TEST_CONFIG = {
  // Add test user credentials here
  clientEmail: '<EMAIL>',
  clientPassword: 'testpassword',
  coworkerEmail: '<EMAIL>',
  adminEmail: '<EMAIL>',
  adminPassword: 'adminpassword'
};

class SubscriptionSystemTester {
  constructor() {
    this.tokens = {};
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🚀 Starting Subscription System Tests...\n');

    try {
      // 1. Authentication Tests
      await this.testAuthentication();

      // 2. Subscription Status Tests
      await this.testSubscriptionStatus();

      // 3. Feature Access Tests
      await this.testFeatureAccess();

      // 4. Protected Route Tests
      await this.testProtectedRoutes();

      // 5. Multiple Subscription Tests
      await this.testMultipleSubscriptions();

      // 6. Role-based Access Tests
      await this.testRoleBasedAccess();

      // 7. Error Message Tests
      await this.testErrorMessages();

      // Display Results
      this.displayResults();

    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
    }
  }

  async testAuthentication() {
    console.log('🔐 Testing Authentication...');

    try {
      // Test client login
      const clientLogin = await axios.post(`${BASE_URL}/clients/login`, {
        email: TEST_CONFIG.clientEmail,
        password: TEST_CONFIG.clientPassword
      });

      if (clientLogin.data.success) {
        this.tokens.client = clientLogin.data.data.token;
        this.addResult('✅ Client authentication', 'PASS');
      } else {
        this.addResult('❌ Client authentication', 'FAIL');
      }

      // Test admin login
      const adminLogin = await axios.post(`${BASE_URL}/clients/login`, {
        email: TEST_CONFIG.adminEmail,
        password: TEST_CONFIG.adminPassword
      });

      if (adminLogin.data.success) {
        this.tokens.admin = adminLogin.data.data.token;
        this.addResult('✅ Admin authentication', 'PASS');
      } else {
        this.addResult('❌ Admin authentication', 'FAIL');
      }

    } catch (error) {
      this.addResult('❌ Authentication setup', 'FAIL', error.message);
    }

    console.log('');
  }

  async testSubscriptionStatus() {
    console.log('📊 Testing Subscription Status...');

    try {
      // Test subscription status endpoint
      const response = await axios.get(`${BASE_URL}/subscription/status`, {
        headers: { Authorization: `Bearer ${this.tokens.client}` }
      });

      if (response.data.success) {
        const data = response.data.data;
        console.log(`   📋 Subscription Status: ${data.hasActiveSubscription ? 'Active' : 'Inactive'}`);
        console.log(`   📦 Active Subscriptions: ${data.activeSubscriptionCount}`);
        console.log(`   🔧 Available Features: ${data.availableFeatures.length}`);
        this.addResult('✅ Subscription status check', 'PASS');
      } else {
        this.addResult('❌ Subscription status check', 'FAIL');
      }

    } catch (error) {
      this.addResult('❌ Subscription status check', 'FAIL', error.response?.data?.message || error.message);
    }

    console.log('');
  }

  async testFeatureAccess() {
    console.log('🔧 Testing Feature Access...');

    const testFeatures = [
      'CREATE_PROJECT',
      'CREATE_TASK', 
      'INVITE_COWORKER',
      'SEND_MESSAGE',
      'UPLOAD_FILE'
    ];

    for (const feature of testFeatures) {
      try {
        const response = await axios.get(`${BASE_URL}/subscription/feature/${feature}/access`, {
          headers: { Authorization: `Bearer ${this.tokens.client}` }
        });

        if (response.data.success) {
          const hasAccess = response.data.data.hasAccess;
          console.log(`   ${hasAccess ? '✅' : '❌'} ${feature}: ${hasAccess ? 'Allowed' : 'Denied'}`);
          this.addResult(`Feature access: ${feature}`, hasAccess ? 'PASS' : 'EXPECTED');
        }

      } catch (error) {
        this.addResult(`❌ Feature access: ${feature}`, 'FAIL', error.response?.data?.message || error.message);
      }
    }

    console.log('');
  }

  async testProtectedRoutes() {
    console.log('🛡️ Testing Protected Routes...');

    const protectedRoutes = [
      { method: 'POST', path: '/projects/create-project', feature: 'CREATE_PROJECT' },
      { method: 'POST', path: '/tasks/create-task', feature: 'CREATE_TASK' },
      { method: 'POST', path: '/coworker/invite-coworker', feature: 'INVITE_COWORKER' },
      { method: 'POST', path: '/chat/upload', feature: 'UPLOAD_FILE' }
    ];

    for (const route of protectedRoutes) {
      try {
        const config = {
          method: route.method.toLowerCase(),
          url: `${BASE_URL}${route.path}`,
          headers: { Authorization: `Bearer ${this.tokens.client}` },
          data: this.getTestData(route.path)
        };

        const response = await axios(config);
        
        // If we get here, the route allowed access
        this.addResult(`✅ Protected route: ${route.path}`, 'PASS', 'Access granted');

      } catch (error) {
        const status = error.response?.status;
        const message = error.response?.data?.message || error.message;

        if (status === 403 || status === 401) {
          // Expected behavior for expired/missing subscription
          this.addResult(`🔒 Protected route: ${route.path}`, 'EXPECTED', 'Access denied (expected)');
        } else {
          this.addResult(`❌ Protected route: ${route.path}`, 'FAIL', message);
        }
      }
    }

    console.log('');
  }

  async testMultipleSubscriptions() {
    console.log('🔄 Testing Multiple Subscriptions...');

    try {
      // Get detailed subscription info
      const response = await axios.get(`${BASE_URL}/subscription/details`, {
        headers: { Authorization: `Bearer ${this.tokens.client}` }
      });

      if (response.data.success) {
        const data = response.data.data;
        console.log(`   📊 Total Subscriptions: ${data.subscriptionCount}`);
        console.log(`   ✅ Active Subscriptions: ${data.activeSubscriptionCount}`);
        console.log(`   ⏰ Expiring Soon: ${data.expiringSubscriptions?.length || 0}`);
        console.log(`   ❌ Expired: ${data.expiredSubscriptions?.length || 0}`);
        
        this.addResult('✅ Multiple subscription handling', 'PASS');
      } else {
        this.addResult('❌ Multiple subscription handling', 'FAIL');
      }

    } catch (error) {
      this.addResult('❌ Multiple subscription handling', 'FAIL', error.response?.data?.message || error.message);
    }

    console.log('');
  }

  async testRoleBasedAccess() {
    console.log('👥 Testing Role-based Access...');

    try {
      // Test admin access
      if (this.tokens.admin) {
        const adminResponse = await axios.get(`${BASE_URL}/subscription/status`, {
          headers: { Authorization: `Bearer ${this.tokens.admin}` }
        });

        if (adminResponse.data.success) {
          const data = adminResponse.data.data;
          if (data.userRole === 'ADMIN') {
            this.addResult('✅ Admin role access', 'PASS');
          } else {
            this.addResult('❌ Admin role access', 'FAIL', 'Role not recognized as admin');
          }
        }
      }

      // Test client access
      const clientResponse = await axios.get(`${BASE_URL}/subscription/status`, {
        headers: { Authorization: `Bearer ${this.tokens.client}` }
      });

      if (clientResponse.data.success) {
        const data = clientResponse.data.data;
        if (data.userRole === 'CLIENT') {
          this.addResult('✅ Client role access', 'PASS');
        } else {
          this.addResult('❌ Client role access', 'FAIL', 'Role not recognized as client');
        }
      }

    } catch (error) {
      this.addResult('❌ Role-based access', 'FAIL', error.response?.data?.message || error.message);
    }

    console.log('');
  }

  async testErrorMessages() {
    console.log('💬 Testing Error Messages...');

    try {
      // Test accessing protected route without subscription
      const response = await axios.post(`${BASE_URL}/projects/create-project`, {
        name: 'Test Project',
        description: 'Test Description'
      }, {
        headers: { Authorization: `Bearer ${this.tokens.client}` }
      });

    } catch (error) {
      const message = error.response?.data?.message || '';
      
      if (message.includes('subscription') || message.includes('expired') || message.includes('feature')) {
        this.addResult('✅ Error message quality', 'PASS', 'Clear subscription-related error');
      } else {
        this.addResult('❌ Error message quality', 'FAIL', 'Generic or unclear error message');
      }
    }

    console.log('');
  }

  getTestData(path) {
    const testData = {
      '/projects/create-project': {
        name: 'Test Project',
        description: 'Test project for subscription testing'
      },
      '/tasks/create-task': {
        title: 'Test Task',
        description: 'Test task for subscription testing',
        projectId: 'test-project-id'
      },
      '/coworker/invite-coworker': {
        email: '<EMAIL>',
        permission: 'VIEW'
      },
      '/chat/upload': new FormData()
    };

    return testData[path] || {};
  }

  addResult(test, status, details = '') {
    this.testResults.push({ test, status, details });
  }

  displayResults() {
    console.log('📋 Test Results Summary:');
    console.log('=' .repeat(60));

    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;
    const expected = this.testResults.filter(r => r.status === 'EXPECTED').length;

    this.testResults.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '🔒';
      console.log(`${icon} ${result.test}`);
      if (result.details) {
        console.log(`   └─ ${result.details}`);
      }
    });

    console.log('=' .repeat(60));
    console.log(`📊 Summary: ${passed} passed, ${failed} failed, ${expected} expected`);
    
    if (failed === 0) {
      console.log('🎉 All tests completed successfully!');
    } else {
      console.log('⚠️  Some tests failed. Please review the results above.');
    }
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  const tester = new SubscriptionSystemTester();
  tester.runAllTests().catch(console.error);
}

module.exports = SubscriptionSystemTester;