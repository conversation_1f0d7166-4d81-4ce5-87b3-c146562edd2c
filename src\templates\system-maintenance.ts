export const systemMaintenanceTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Scheduled Maintenance</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #171617;
            padding: 20px;
            text-align: center;
        }
        .logo {
            max-width: 140px;
            height: auto;
            display: block;
            margin: 0 auto;
        }
        .content {
            padding: 30px;
            background-color: #ffffff;
        }
        .maintenance-banner {
            background-color: #e2e8f0;
            border-left: 4px solid #475569;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        .maintenance-banner-title {
            font-size: 20px;
            font-weight: 600;
            color: #475569;
            padding-bottom: 10px;
        }
        .cta-container {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background-color: #1a3c34;
            color: #ffffff !important;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 10px;
        }
        .signature {
            font-size: 14px;
            line-height: 22px;
            color: #4b5563;
            margin: 0;
        }
        .signature span {
            color: #1a3c34;
            font-weight: 600;
        }
        .footer {
            padding: 20px;
            text-align: center;
            font-size: 13px;
            line-height: 20px;
            background-color: #171617;
            color: #ffffff;
        }
        .footer a {
            color: #e1e1e1 !important;
            text-decoration: none;
        }
        .footer span {
            color: #f0f0f0;
        }
        /* Email client compatibility */
        table {
            width: 100%;
            border-collapse: collapse;
        }
        @media only screen and (max-width: 600px) {
            .container {
                padding: 15px;
            }
            .content {
                padding: 20px;
            }
            .header {
                padding: 15px;
            }
            .maintenance-banner-title {
                font-size: 18px;
            }
            .cta-button {
                width: 100%;
                box-sizing: border-box;
                margin: 10px 0;
            }
        }
        /* Accessibility */
        a:focus, .cta-button:focus {
            outline: 2px solid #1a3c34;
            outline-offset: 2px;
        }
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #1a1a1a;
            }
            .container {
                background-color: #ffffff;
            }
            .content {
                background-color: #ffffff;
                color: #333333;
            }
            .maintenance-banner {
                background-color: #475569;
                border-left-color: #e2e8f0;
            }
            .maintenance-banner-title {
                color: #e2e8f0;
            }
            .signature {
                color: #999999;
            }
            .signature span {
                color: #1a3c34;
            }
        }
    </style>
</head>
<body>
    <table role="presentation" class="container">
        <tr>
            <td>
                <div class="header">
                    <img src="https://macgence.s3.ap-south-1.amazonaws.com/whitemode.png" alt="Logo" class="logo">
                </div>

                <div class="content">
                    <div class="maintenance-banner">
                        <div class="maintenance-banner-title">🔧 Scheduled Maintenance</div>
                    </div>

                    <p>Hi {{ params.firstName }},</p>
                    <p>We're performing scheduled maintenance to improve our platform's performance and features. We apologize for any inconvenience and appreciate your understanding.</p>

                    <div class="cta-container">
                        <a href="{{ params.statusPageUrl }}" class="cta-button">📊 View Status Page</a>
                    </div>

                    <p>You can monitor the maintenance progress and get real-time updates on our status page. We'll notify you once the maintenance is complete.</p>

                    <p class="signature">
                        Best Regards,<br>
                        <span>The Team</span>
                    </p>
                </div>

                <div class="footer">
                    <span><EMAIL></span> | 
                    <a href="http://app.getannotator.com/" style="color: #e1e1e1ff; text-decoration: none;">app.getannotator.com</a> | 
                    <a href="tel:+13602098904">******-209-8904</a><br>
                    <span>Need help? Reach us via our <a href="http://app.getannotator.com/contact">contact form</a>.</span>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>
`;