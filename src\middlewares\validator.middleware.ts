// middlewares/validate.ts

import { NextFunction, Request, Response } from "express";
import { ZodSchema } from "zod";

// export const validate =
//   (schema: ZodSchema) => (req: Request, res: Response, next: NextFunction) => {
//     const result = schema.safeParse(req.body);
//     if (!result.success) {
//       return res.status(400).json({
//         message: "Validation error",
//         errors: result.error.errors,
//       });
//     }
//     req.body = result.data; // sanitized data
//     next();
//   };
export const validate =
  (schema: ZodSchema) =>
  async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const result = schema.safeParse(req.body);
    if (!result.success) {
      res.status(400).json({
        message: "Validation error",
        errors: result.error.errors,
      });
      return;
    }

    req.body = result.data;
    next();
  };
