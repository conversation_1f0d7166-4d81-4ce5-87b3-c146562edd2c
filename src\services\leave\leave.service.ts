import prisma from "../../prisma";
import { LeaveStatus } from "@prisma/client";

export class LeaveService {
  async requestLeave({
    annotatorId,
    startDate,
    endDate,
    reason,
  }: {
    annotatorId: string;
    startDate: Date;
    endDate: Date;
    reason: string;
  }) {
    // Validate dates
    if (new Date(startDate) > new Date(endDate)) {
      throw new Error("Start date cannot be after end date");
    }

    // Create leave request
    const leaveRequest = await prisma.leaveRequest.create({
      data: {
        annotatorId,
        startDate,
        endDate,
        reason,
        status: LeaveStatus.PENDING,
      },
      include: {
        annotator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return leaveRequest;
  }

  async approveLeave(requestId: string, approverId: string) {
    const request = await prisma.leaveRequest.findUnique({
      where: { id: requestId },
      include: {
        annotator: true,
      },
    });

    if (!request) {
      throw new Error("Leave request not found");
    }

    if (request.status !== LeaveStatus.PENDING) {
      throw new Error("Leave request has already been processed");
    }

    // Update leave request status
    const updatedRequest = await prisma.leaveRequest.update({
      where: { id: requestId },
      data: {
        status: LeaveStatus.APPROVED,
        approvedById: approverId,
        approvedAt: new Date(),
      },
      include: {
        annotator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        approvedBy: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
      },
    });

    return updatedRequest;
  }

  async rejectLeave(
    requestId: string,
    approverId: string,
    rejectionReason: string
  ) {
    const request = await prisma.leaveRequest.findUnique({
      where: { id: requestId },
    });

    if (!request) {
      throw new Error("Leave request not found");
    }

    if (request.status !== LeaveStatus.PENDING) {
      throw new Error("Leave request has already been processed");
    }

    // Update leave request status
    const updatedRequest = await prisma.leaveRequest.update({
      where: { id: requestId },
      data: {
        status: LeaveStatus.REJECTED,
        approvedById: approverId,
        approvedAt: new Date(),
        rejectionReason,
      },
      include: {
        annotator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        approvedBy: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
      },
    });

    return updatedRequest;
  }

  async getPendingLeaveRequests(query: any) {
    const { page = 1, limit = 10 } = query;
    const skip = (page - 1) * limit;

    const [requests, total] = await Promise.all([
      prisma.leaveRequest.findMany({
        where: {
          status: LeaveStatus.PENDING,
        },
        include: {
          annotator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        skip,
        take: Number(limit),
        orderBy: {
          createdAt: "desc",
        },
      }),
      prisma.leaveRequest.count({
        where: {
          status: LeaveStatus.PENDING,
        },
      }),
    ]);

    return {
      data: requests,
      meta: {
        total,
        page: Number(page),
        limit: Number(limit),
        totalPages: Math.ceil(total / Number(limit)),
      },
    };
  }

  async getAnnotatorLeaveHistory(annotatorId: string, query: any) {
    const { page = 1, limit = 10 } = query;
    const skip = (page - 1) * limit;

    const [requests, total] = await Promise.all([
      prisma.leaveRequest.findMany({
        where: {
          annotatorId,
        },
        include: {
          approvedBy: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
        },
        skip,
        take: Number(limit),
        orderBy: {
          createdAt: "desc",
        },
      }),
      prisma.leaveRequest.count({
        where: {
          annotatorId,
        },
      }),
    ]);

    return {
      data: requests,
      meta: {
        total,
        page: Number(page),
        limit: Number(limit),
        totalPages: Math.ceil(total / Number(limit)),
      },
    };
  }

  async getAllLeaveRequests(query: any) {
    const { page = 1, limit = 10 } = query;
    const skip = (page - 1) * limit;

    const [requests, total] = await Promise.all([
      prisma.leaveRequest.findMany({
        include: {
          annotator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          approvedBy: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
        },
        skip,
        take: Number(limit),
        orderBy: {
          createdAt: "desc",
        },
      }),
      prisma.leaveRequest.count(),
    ]);

    return {
      data: requests,
      meta: {
        total,
        page: Number(page),
        limit: Number(limit),
        totalPages: Math.ceil(total / Number(limit)),
      },
    };
  }



async getLeaveRequestsByRole(userId: string, role: string) {
  let actualUserId = userId;
  let actualRole = role;

  if (role === "COWORKER") {
    const coworker = await prisma.user.findUnique({
      where: { id: userId },
      select: { clientOwnerId: true },
    });

    if (!coworker?.clientOwnerId) {
      throw new Error("Coworker is not linked to any client");
    }

    actualUserId = coworker.clientOwnerId;
    actualRole = "CLIENT";
  }

  const assignments = await prisma.assignment.findMany({
    where: actualRole === "CLIENT"
      ? { clientId: actualUserId }
      : { coordinatorId: actualUserId },
    select: { developerId: true },
  });

  const assignedAnnotatorIds = assignments.map(a => a.developerId);

  return prisma.leaveRequest.findMany({
    where: { annotatorId: { in: assignedAnnotatorIds } },
    include: {
      annotator: {
        select: { id: true, name: true, email: true },
      },
    },
    orderBy: { createdAt: "desc" },
  });
}


async getLeaveRequestsByAnnotator(userId: string) {
  return prisma.leaveRequest.findMany({
    where: { annotatorId: userId },
    include: {
      approvedBy: {
        select: { id: true, name: true, email: true },
      },
    },
    orderBy: { createdAt: "desc" },
  });
}

}

export const leaveService = new LeaveService();
