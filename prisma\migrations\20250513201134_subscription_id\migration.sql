/*
  Warnings:

  - A unique constraint covering the columns `[subscription_id]` on the table `Subscription` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "SubscriptionStatus" ADD VALUE 'CANCELLED';
ALTER TYPE "SubscriptionStatus" ADD VALUE 'DISPUTED';

-- AlterTable
ALTER TABLE "Subscription" ADD COLUMN     "subscription_id" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "Subscription_subscription_id_key" ON "Subscription"("subscription_id");
