import express from "express";
import authMiddleware from "../../middlewares/checkAuth";
import { asyncHand<PERSON> } from "../../middlewares/asyncHandler";
import attendanceController from "../../controllers/Attendance/attendance.controller";
//import { hasRole, hasMultipleRole } from "../../middlewares/checkRole";

const router = express.Router();

router.post("/clock-in", authMiddleware, asyncHandler(attendanceController.clockIn));
router.post("/clock-out", authMiddleware, asyncHandler(attendanceController.clockOut));
router.post("/start-break", authMiddleware, asyncHandler(attendanceController.startBreak));
router.post("/end-break", authMiddleware, asyncHandler(attendanceController.endBreak));
router.get("/today-log", authMiddleware, asyncHandler(attendanceController.getTodayAttendance))
router.get("/history/:userId", authMiddleware, asyncHandler(attendanceController.getUserHistory));
router.get("/report-download", authMiddleware, asyncHandler(attendanceController.downloadAttendanceReport));
router.get("/clock-log", authMiddleware, asyncHandler(attendanceController.getClockLog));
router.get(
  "/my-attendance-history", 
  authMiddleware, 
  asyncHandler(attendanceController.getAnnotatorAttendanceHistory)
);

export default router;
