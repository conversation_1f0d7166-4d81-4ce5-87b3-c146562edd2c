* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f7fa;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background-color: #2c3e50;
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-logo {
  font-size: 1.5rem;
  font-weight: bold;
}

.user-info {
  font-size: 0.9rem;
}

.app-content {
  flex: 1;
  padding: 2rem 0;
  background-color: #f5f7fa;
}

.app-footer {
  background-color: #2c3e50;
  color: #ecf0f1;
  text-align: center;
  padding: 1.5rem;
  font-size: 0.9rem;
}

.loading-app {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 1.2rem;
  color: #555;
}

.login-required {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  text-align: center;
  padding: 2rem;
}

.login-required h2 {
  margin-bottom: 2rem;
  color: #2c3e50;
}

@media (max-width: 768px) {
  .app-header {
    padding: 1rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .app-content {
    padding: 1rem 0;
  }
}