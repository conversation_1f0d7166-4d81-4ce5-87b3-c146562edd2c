-- AlterTable
ALTER TABLE "Subscription" ADD COLUMN     "currency" TEXT,
ADD COLUMN     "next_billing_date" TIMESTAMP(3),
ADD COLUMN     "payment_frequency_count" INTEGER,
ADD COLUMN     "payment_frequency_interval" TEXT,
ADD COLUMN     "previous_billing_date" TIMESTAMP(3),
ADD COLUMN     "product_id" TEXT,
ADD COLUMN     "quantity" INTEGER,
ADD COLUMN     "rawResponse" JSONB,
ADD COLUMN     "recurring_amount" DOUBLE PRECISION,
ADD COLUMN     "subscription_period_count" INTEGER,
ADD COLUMN     "subscription_period_interval" TEXT,
ADD COLUMN     "trial_period_days" INTEGER;
