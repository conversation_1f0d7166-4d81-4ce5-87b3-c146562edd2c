import { PrismaClient, Role } from '@prisma/client';
import bcrypt from 'bcrypt';

export class UserService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }


  async getAllUsers() {
    return this.prisma.user.findMany();
  }

  async createUser(data: { username: string; email: string; password: string; role?: Role }) {
    const hashedPassword = await bcrypt.hash(data.password, 10);

    return this.prisma.user.create({
      data: {
        name: data.username,
        email: data.email,
        passwordHash: hashedPassword, // ✔️ Correct field
        role: data.role || Role.CLIENT, // default to CLIENT if not provided
      },
    });
  }

  async getUserById(id: string) {
    return this.prisma.user.findUnique({
      where: { id },
    });
  }

  async updateUser(id: string, data: Partial<{ username: string; email: string; password: string; role: Role }>) {
    const updateData: any = {};

    if (data.username) updateData.name = data.username;
    if (data.email) updateData.email = data.email;
    if (data.role) updateData.role = data.role;
    if (data.password) {
      const hashed = await bcrypt.hash(data.password, 10);
      updateData.passwordHash = hashed;
    }

    return this.prisma.user.update({
      where: { id },
      data: updateData,
    });
  }

  async deleteUser(id: string) {
    // First check if the user exists and get their role
    const user = await this.prisma.user.findUnique({
      where: { id },
      select: { id: true, role: true, isDeleted: true, accountStatus: true }
    });

    if (!user) {
      throw new Error("User not found");
    }

    // Check if user is already soft deleted
    if (user.isDeleted || user.accountStatus === "DELETED") {
      throw new Error("User is already deleted");
    }

    // Check for any relationships that would prevent deletion
    const [groupMembersCount, messagesCount, reactionsCount, conversationParticipantsCount] = await Promise.all([
      this.prisma.groupMember.count({ where: { userId: id } }),
      this.prisma.message.count({ where: { OR: [{ senderId: id }, { receiverId: id }] } }),
      this.prisma.reaction.count({ where: { userId: id } }),
      this.prisma.conversationParticipant.count({ where: { userId: id } })
    ]);

    const hasRelationships = groupMembersCount > 0 || messagesCount > 0 || 
                            reactionsCount > 0 || conversationParticipantsCount > 0;

    if (hasRelationships) {
      // Perform soft delete if user has relationships
      const softDeletedUser = await this.prisma.user.update({
        where: { id },
        data: {
          isDeleted: true,
          accountStatus: "DELETED",
          email: `deleted_${Date.now()}_${user.id}@deleted.local`,
        }
      });

      return {
        ...softDeletedUser,
        deletionType: "soft",
        message: "User has relationships and has been soft deleted",
        relationshipDetails: [
          ...(groupMembersCount > 0 ? [`${groupMembersCount} group membership(s)`] : []),
          ...(messagesCount > 0 ? [`${messagesCount} message(s)`] : []),
          ...(reactionsCount > 0 ? [`${reactionsCount} reaction(s)`] : []),
          ...(conversationParticipantsCount > 0 ? [`${conversationParticipantsCount} conversation(s)`] : [])
        ]
      };
    } else {
      // Hard delete if no relationships
      const deletedUser = await this.prisma.user.delete({ where: { id } });
      return {
        ...deletedUser,
        deletionType: "hard",
        message: "User has been permanently deleted as no relationships were found",
        relationshipDetails: []
      };
    }
  }

  async getUserCompleteProfile(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        lastname: true,
        email: true,
        role: true,
        domain: true,
        availableFrom: true,
        description: true,
        availableTo: true,
        timezone: true,
        industry: true,
        category: true,
        accountStatus: true,
        annotatorStatus: true,
        createdAt: true,
        updatedAt: true,
        
        // Related entities based on user role
        assignedProjects: true,
        projectsOwned: {
          select: {
            id: true,
            name: true,
            description: true,
            status: true,
            createdAt: true,
          }
        },
        createdTasks: {
          select: {
            id: true,
            name: true,
            status: true,
            priority: true,
            createdAt: true,
          }
        },
        selfTasks: {
          select: {
            id: true,
            name: true,
            status: true,
            priority: true,
            createdAt: true,
          }
        },
        
        // For coordinators and admins
        assignmentsAsCoordinator: {
          select: {
            id: true,
            client: {
              select: {
                id: true,
                name: true,
                email: true,
              }
            },
            developer: {
              select: {
                id: true,
                name: true,
                email: true,
              }
            },
            createdAt: true,
          }
        },
        
        // For annotators
        annotatorProjects: {
          select: {
            id: true,
            name: true,
            description: true,
            status: true,
          }
        },
        
        // For clients
        coWorkers: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            coworkerPermission: true,
          }
        },
        
        // Package information
        Package: {
          select: {
            id: true,
            name: true,
            description: true,
            price: true,
          }
        },
        
        // Attendance information
        // attendanceLogs: {
        //   take: 10,
        //   orderBy: {
        //     date: 'desc',
        //   },
        //   select: {
        //     id: true,
        //     date: true,
        //     timeIn: true,
        //     timeOut: true,
        //     status: true,
        //     workingMinutes: true,
        //     breakMinutes: true,
        //   }
        // },
      }
    });

    return user;
  }
}
